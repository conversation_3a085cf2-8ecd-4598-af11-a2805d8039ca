namespace MbLinkApp;

public partial class MainPage : ContentPage
{
	public MainPage()
	{
		InitializeComponent();

#if WINDOWS
		// Set window size for Windows platform
		Microsoft.Maui.Handlers.WindowHandler.Mapper.AppendToMapping(nameof(IWindow), (handler, view) =>
		{
			var mauiWindow = handler.VirtualView;
			var nativeWindow = handler.PlatformView;
			nativeWindow.Activate();
			IntPtr windowHandle = WinRT.Interop.WindowNative.GetWindowHandle(nativeWindow);
			Microsoft.UI.WindowId windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(windowHandle);
			Microsoft.UI.Windowing.AppWindow appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
			appWindow.Resize(new Windows.Graphics.SizeInt32(400, 600));

			// Center the window
			var displayArea = Microsoft.UI.Windowing.DisplayArea.GetFromWindowId(windowId, Microsoft.UI.Windowing.DisplayAreaFallback.Nearest);
			if (displayArea is not null)
			{
				var centerPosition = new Windows.Graphics.PointInt32
				{
					X = (displayArea.WorkArea.Width - 400) / 2,
					Y = (displayArea.WorkArea.Height - 600) / 2
				};
				appWindow.Move(centerPosition);
			}

			appWindow.Title = "MbLink Login";
		});
#endif
	}

	private async void OnLoginClicked(object sender, EventArgs e)
	{
		// Get values from the form
		string username = UsernameEntry.Text?.Trim() ?? "";
		string password = PasswordEntry.Text?.Trim() ?? "";

		// Static credentials
		const string validUsername = "mblink";
		const string validPassword = "123456";

		// Basic validation
		if (string.IsNullOrEmpty(username))
		{
			await DisplayAlert("Error", "Please enter your username.", "OK");
			return;
		}

		if (string.IsNullOrEmpty(password))
		{
			await DisplayAlert("Error", "Please enter your password.", "OK");
			return;
		}

		// Check credentials
		if (username != validUsername)
		{
			await DisplayAlert("Error", "Invalid username. Please try again.", "OK");
			return;
		}

		if (password != validPassword)
		{
			await DisplayAlert("Error", "Invalid password. Please try again.", "OK");
			return;
		}

		// Disable login button during processing
		LoginButton.IsEnabled = false;
		LoginButton.Text = "Logging in...";

		try
		{
			// Simulate login process
			await Task.Delay(1000);

			// Navigate to main form
			await Navigation.PushAsync(new MainForm());
		}
		catch (Exception ex)
		{
			await DisplayAlert("Error", $"Login failed: {ex.Message}", "OK");
		}
		finally
		{
			// Re-enable login button
			LoginButton.IsEnabled = true;
			LoginButton.Text = "Login";
		}
	}
}

