{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\MbLinkApp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\MbLinkApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\MbLinkApp.csproj", "projectName": "MbLinkApp", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\MbLinkApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WinForms.config"], "originalTargetFrameworks": ["net9.0-android", "net9.0-ios", "net9.0-maccatalyst", "net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Program Files (x86)\\Syncfusion\\Essential Studio\\WinUI\\26.1.35\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "projectReferences": {}}, "net9.0-ios18.2": {"targetAlias": "net9.0-ios", "projectReferences": {}}, "net9.0-maccatalyst18.2": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {}}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.14, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.2, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.Android": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}, "net9.0-ios18.2": {"targetAlias": "net9.0-ios", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.14, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.2, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.iOS": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}, "net9.0-maccatalyst18.2": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.14, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.2, )", "autoReferenced": true}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}, "runtimes": {"android-arm64": {"#import": []}, "android-x64": {"#import": []}, "iossimulator-x64": {"#import": []}, "maccatalyst-x64": {"#import": []}, "win10-x64": {"#import": []}}}}}