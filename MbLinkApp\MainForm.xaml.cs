using System.Collections.ObjectModel;

namespace MbLinkApp;

public class VehicleModel
{
	public string ModelName { get; set; } = "";
	public string ModelCode { get; set; } = "";
	public string VehicleClass { get; set; } = "";
	public string VehicleType { get; set; } = "";
	public string ImagePath { get; set; } = "";
}

public partial class MainForm : ContentPage
{
	private ObservableCollection<VehicleModel> _allVehicles;
	private ObservableCollection<VehicleModel> _filteredVehicles;
	private string _currentVehicleType = "Binek";
	private string _currentVehicleClass = "All";

	public MainForm()
	{
		InitializeComponent();

		// Initialize vehicle collections
		_allVehicles = new ObservableCollection<VehicleModel>();
		_filteredVehicles = new ObservableCollection<VehicleModel>();
		VehicleModelsCollectionView.ItemsSource = _filteredVehicles;

		// Set login time
		LoginTimeLabel.Text = $"Logged in: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

		// Load vehicle data
		LoadVehicleData();

		// Set initial filter
		FilterVehicles();

#if WINDOWS
		// Set window size for Windows platform
		Microsoft.Maui.Handlers.WindowHandler.Mapper.AppendToMapping(nameof(IWindow), (handler, view) =>
		{
			var mauiWindow = handler.VirtualView;
			var nativeWindow = handler.PlatformView;
			nativeWindow.Activate();
			IntPtr windowHandle = WinRT.Interop.WindowNative.GetWindowHandle(nativeWindow);
			Microsoft.UI.WindowId windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(windowHandle);
			Microsoft.UI.Windowing.AppWindow appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
			appWindow.Resize(new Windows.Graphics.SizeInt32(1200, 800));

			// Center the window
			var displayArea = Microsoft.UI.Windowing.DisplayArea.GetFromWindowId(windowId, Microsoft.UI.Windowing.DisplayAreaFallback.Nearest);
			if (displayArea is not null)
			{
				var centerPosition = new Windows.Graphics.PointInt32
				{
					X = (displayArea.WorkArea.Width - 1200) / 2,
					Y = (displayArea.WorkArea.Height - 800) / 2
				};
				appWindow.Move(centerPosition);
			}

			appWindow.Title = "Mercedes-Benz XEntry Vehicle Selection";
		});
#endif
	}

	private void LoadVehicleData()
	{
		// Binek Araçları - A/B Sınıfı
		_allVehicles.Add(new VehicleModel { ModelName = "A-Class", ModelCode = "A (177)", VehicleClass = "A/B", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "A-Class", ModelCode = "A (176)", VehicleClass = "A/B", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "A-Class", ModelCode = "A (169)", VehicleClass = "A/B", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "B-Class", ModelCode = "B (247)", VehicleClass = "A/B", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "B-Class", ModelCode = "B (246)", VehicleClass = "A/B", VehicleType = "Binek" });

		// Binek Araçları - C Sınıfı
		_allVehicles.Add(new VehicleModel { ModelName = "C-Class", ModelCode = "C (206)", VehicleClass = "C", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "C-Class", ModelCode = "C (205)", VehicleClass = "C", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "C-Class", ModelCode = "C (204)", VehicleClass = "C", VehicleType = "Binek" });

		// Binek Araçları - E Sınıfı
		_allVehicles.Add(new VehicleModel { ModelName = "E-Class", ModelCode = "E (238)", VehicleClass = "E", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "E-Class", ModelCode = "E (214)", VehicleClass = "E", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "E-Class", ModelCode = "E (213)", VehicleClass = "E", VehicleType = "Binek" });

		// Binek Araçları - EQ Sınıfı
		_allVehicles.Add(new VehicleModel { ModelName = "EQA", ModelCode = "EQA (243)", VehicleClass = "EQ", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "EQB", ModelCode = "EQB (247)", VehicleClass = "EQ", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "EQC", ModelCode = "EQC (293)", VehicleClass = "EQ", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "EQE", ModelCode = "EQE (294)", VehicleClass = "EQ", VehicleType = "Binek" });

		// Binek Araçları - SUV Sınıfı
		_allVehicles.Add(new VehicleModel { ModelName = "GLA", ModelCode = "GLA (247)", VehicleClass = "SUV", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "GLB", ModelCode = "GLB (247)", VehicleClass = "SUV", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "GLC", ModelCode = "GLC (253)", VehicleClass = "SUV", VehicleType = "Binek" });
		_allVehicles.Add(new VehicleModel { ModelName = "GLE", ModelCode = "GLE (167)", VehicleClass = "SUV", VehicleType = "Binek" });

		// Süper Spor Arabalar
		_allVehicles.Add(new VehicleModel { ModelName = "AMG GT", ModelCode = "AMG GT (190)", VehicleClass = "AMG", VehicleType = "SuperSport" });
		_allVehicles.Add(new VehicleModel { ModelName = "SL", ModelCode = "SL (232)", VehicleClass = "AMG", VehicleType = "SuperSport" });
		_allVehicles.Add(new VehicleModel { ModelName = "SLS AMG", ModelCode = "SLS (197)", VehicleClass = "AMG", VehicleType = "SuperSport" });

		// Transporter
		_allVehicles.Add(new VehicleModel { ModelName = "Sprinter", ModelCode = "Sprinter (907)", VehicleClass = "Van", VehicleType = "Transporter" });
		_allVehicles.Add(new VehicleModel { ModelName = "Vito", ModelCode = "Vito (447)", VehicleClass = "Van", VehicleType = "Transporter" });
		_allVehicles.Add(new VehicleModel { ModelName = "Citan", ModelCode = "Citan (415)", VehicleClass = "Van", VehicleType = "Transporter" });

		StatusLabel.Text = $"Loaded {_allVehicles.Count} vehicle models";
	}

	private void FilterVehicles()
	{
		_filteredVehicles.Clear();

		List<VehicleModel> filtered;

		if (_currentVehicleClass == "All")
		{
			// Show all vehicles of the current type
			filtered = _allVehicles.Where(v => v.VehicleType == _currentVehicleType).ToList();
		}
		else
		{
			// Show vehicles of specific type and class
			filtered = _allVehicles.Where(v =>
				v.VehicleType == _currentVehicleType &&
				v.VehicleClass == _currentVehicleClass).ToList();
		}

		foreach (var vehicle in filtered)
		{
			_filteredVehicles.Add(vehicle);
		}

		UpdateVehicleTypeLabel();

		if (_currentVehicleClass == "All")
		{
			string typeDisplayName = _currentVehicleType switch
			{
				"Binek" => "passenger",
				"SuperSport" => "super sport",
				"Transporter" => "commercial",
				_ => "vehicle"
			};
			StatusLabel.Text = $"Showing all {_filteredVehicles.Count} {typeDisplayName} vehicles";
		}
		else
		{
			string typeDisplayName = _currentVehicleType switch
			{
				"Binek" => "passenger",
				"SuperSport" => "super sport",
				"Transporter" => "commercial",
				_ => "vehicle"
			};
			StatusLabel.Text = $"Showing {_filteredVehicles.Count} {typeDisplayName} vehicles - {_currentVehicleClass} class";
		}
	}

	private void UpdateVehicleTypeLabel()
	{
		string typeText = _currentVehicleType switch
		{
			"Binek" => "Passenger Cars",
			"SuperSport" => "Super Sport Cars",
			"Transporter" => "Commercial Vehicles",
			_ => "Vehicles"
		};

		if (_currentVehicleClass == "All")
		{
			VehicleTypeLabel.Text = $"{typeText} - All Classes";
		}
		else
		{
			VehicleTypeLabel.Text = $"{typeText} - {_currentVehicleClass} Class";
		}
	}

	private void UpdateClassButtonStyles(string selectedClass)
	{
		// Reset all class buttons to default style
		AllVehiclesBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		AllVehiclesBtn.TextColor = Color.FromArgb("#374151");
		ClassABBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		ClassABBtn.TextColor = Color.FromArgb("#374151");
		ClassCBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		ClassCBtn.TextColor = Color.FromArgb("#374151");
		ClassEBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		ClassEBtn.TextColor = Color.FromArgb("#374151");
		ClassEQBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		ClassEQBtn.TextColor = Color.FromArgb("#374151");
		ClassSUVBtn.BackgroundColor = Color.FromArgb("#E5E7EB");
		ClassSUVBtn.TextColor = Color.FromArgb("#374151");

		// Highlight selected button
		Button selectedButton = selectedClass switch
		{
			"All" => AllVehiclesBtn,
			"A/B" => ClassABBtn,
			"C" => ClassCBtn,
			"E" => ClassEBtn,
			"EQ" => ClassEQBtn,
			"SUV" => ClassSUVBtn,
			_ => ClassABBtn
		};

		selectedButton.BackgroundColor = Color.FromArgb("#3B82F6");
		selectedButton.TextColor = Colors.White;
	}

	private void UpdateTopMenuButtonStyles(string selectedType)
	{
		// Reset all top menu buttons to default style
		PassengerCarsBtn.BackgroundColor = Color.FromArgb("#6B7280");
		SuperSportBtn.BackgroundColor = Color.FromArgb("#6B7280");
		TransporterBtn.BackgroundColor = Color.FromArgb("#6B7280");

		// Highlight selected button
		Button selectedButton = selectedType switch
		{
			"Binek" => PassengerCarsBtn,
			"SuperSport" => SuperSportBtn,
			"Transporter" => TransporterBtn,
			_ => PassengerCarsBtn
		};

		selectedButton.BackgroundColor = Color.FromArgb("#3B82F6");
	}

	// Top Menu Event Handlers
	private void OnPassengerCarsClicked(object sender, EventArgs e)
	{
		_currentVehicleType = "Binek";
		_currentVehicleClass = "All"; // Default to show all passenger cars
		UpdateTopMenuButtonStyles("Binek");
		UpdateClassButtonStyles("All");
		FilterVehicles();
	}

	private void OnSuperSportClicked(object sender, EventArgs e)
	{
		_currentVehicleType = "SuperSport";
		_currentVehicleClass = "All"; // Show all super sport cars
		UpdateTopMenuButtonStyles("SuperSport");
		UpdateClassButtonStyles("All");
		FilterVehicles();
	}

	private void OnTransporterClicked(object sender, EventArgs e)
	{
		_currentVehicleType = "Transporter";
		_currentVehicleClass = "All"; // Show all transporters
		UpdateTopMenuButtonStyles("Transporter");
		UpdateClassButtonStyles("All");
		FilterVehicles();
	}

	// Left Menu (Class) Event Handlers
	private void OnAllVehiclesClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "All";
		UpdateClassButtonStyles("All");
		FilterVehicles();
	}

	private void OnClassABClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "A/B";
		UpdateClassButtonStyles("A/B");
		FilterVehicles();
	}

	private void OnClassCClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "C";
		UpdateClassButtonStyles("C");
		FilterVehicles();
	}

	private void OnClassEClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "E";
		UpdateClassButtonStyles("E");
		FilterVehicles();
	}

	private void OnClassEQClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "EQ";
		UpdateClassButtonStyles("EQ");
		FilterVehicles();
	}

	private void OnClassSUVClicked(object sender, EventArgs e)
	{
		_currentVehicleClass = "SUV";
		UpdateClassButtonStyles("SUV");
		FilterVehicles();
	}

	// Vehicle Selection Event Handler
	private async void OnVehicleSelectionChanged(object sender, SelectionChangedEventArgs e)
	{
		if (e.CurrentSelection.FirstOrDefault() is VehicleModel selectedVehicle)
		{
			StatusLabel.Text = $"Selected: {selectedVehicle.ModelName} {selectedVehicle.ModelCode}";
			await DisplayAlert("Vehicle Selected",
				$"You selected:\n{selectedVehicle.ModelName}\nModel Code: {selectedVehicle.ModelCode}\nClass: {selectedVehicle.VehicleClass}",
				"OK");
		}
	}

	// Vehicle Hover Event Handlers
	private void OnVehiclePointerEntered(object sender, PointerEventArgs e)
	{
		if (sender is Frame frame)
		{
			frame.BackgroundColor = Color.FromArgb("#E5E7EB"); // Darker background on hover
		}
	}

	private void OnVehiclePointerExited(object sender, PointerEventArgs e)
	{
		if (sender is Frame frame)
		{
			frame.BackgroundColor = Color.FromArgb("#F8F9FA"); // Original background
		}
	}

	private async void OnLogoutClicked(object sender, EventArgs e)
	{
		bool result = await DisplayAlert("Logout", "Are you sure you want to logout from Mercedes XEntry?", "Yes", "No");
		if (result)
		{
			// Navigate back to login page
			await Navigation.PopAsync();
		}
	}
}
