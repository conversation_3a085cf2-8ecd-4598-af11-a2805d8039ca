<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MbLinkApp.MainForm"
             Title="MbLink - Main Application">

    <ScrollView>
        <VerticalStackLayout
            Padding="40"
            Spacing="20">
            
            <!-- Header -->
            <Label
                Text="Welcome to MbLink!"
                FontSize="28"
                FontAttributes="Bold"
                HorizontalOptions="Center"
                TextColor="#2196F3"
                Margin="0,0,0,20" />

            <!-- User Info Card -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="10"
                   Padding="20">
                <VerticalStackLayout Spacing="10">
                    <Label Text="User Information" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333" />
                    
                    <Label x:Name="UserInfoLabel"
                           Text="Logged in as: mblink"
                           FontSize="14"
                           TextColor="#666" />
                    
                    <Label x:Name="LoginTimeLabel"
                           Text=""
                           FontSize="12"
                           TextColor="#999" />
                </VerticalStackLayout>
            </Frame>

            <!-- Main Menu -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="10"
                   Padding="20">
                <VerticalStackLayout Spacing="15">
                    <Label Text="Main Menu" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333" />

                    <Button Text="Data Management"
                            BackgroundColor="#4CAF50"
                            TextColor="White"
                            FontSize="16"
                            CornerRadius="8"
                            HeightRequest="45"
                            Clicked="OnDataManagementClicked" />

                    <Button Text="Reports"
                            BackgroundColor="#FF9800"
                            TextColor="White"
                            FontSize="16"
                            CornerRadius="8"
                            HeightRequest="45"
                            Clicked="OnReportsClicked" />

                    <Button Text="Settings"
                            BackgroundColor="#9C27B0"
                            TextColor="White"
                            FontSize="16"
                            CornerRadius="8"
                            HeightRequest="45"
                            Clicked="OnSettingsClicked" />

                    <Button Text="About"
                            BackgroundColor="#607D8B"
                            TextColor="White"
                            FontSize="16"
                            CornerRadius="8"
                            HeightRequest="45"
                            Clicked="OnAboutClicked" />
                </VerticalStackLayout>
            </Frame>

            <!-- Logout Button -->
            <Button Text="Logout"
                    BackgroundColor="#F44336"
                    TextColor="White"
                    FontSize="16"
                    FontAttributes="Bold"
                    CornerRadius="8"
                    HeightRequest="50"
                    Margin="0,20,0,0"
                    Clicked="OnLogoutClicked" />
        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
