C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\MbLinkApp.runtimeconfig.json
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\MbLinkApp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\MbLinkApp.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\com.companyname.mblinkapp.apk
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\com.companyname.mblinkapp.aab
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\com.companyname.mblinkapp-Signed.aab
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\com.companyname.mblinkapp-Signed.apk
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\bin\Debug\net9.0-android\com.companyname.mblinkapp-Signed.apk.idsig
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\.NETCoreApp,Version=v9.0.AssemblyAttributes.cs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\build.props
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\adb.props
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\_CompileBindingJava.FileList.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauiimage.inputs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauifont.inputs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauisplash.inputs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-hdpi\splash.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-mdpi\splash.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-xhdpi\splash.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-xxhdpi\splash.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable-xxxhdpi\splash.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\sp\values\maui_colors.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauisplash.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauifont.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\f\FluentUI.cs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\f\OpenSans-Regular.ttf
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\f\OpenSans-Semibold.ttf
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_round.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_round.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_round.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_round.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_round.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-hdpi\appicon.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-mdpi\appicon.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_foreground.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_foreground.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_foreground.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_foreground.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_foreground.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxxhdpi\appicon_background.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xxhdpi\appicon_background.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-xhdpi\appicon_background.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-hdpi\appicon_background.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\mipmap-mdpi\appicon_background.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\drawable-xxxhdpi\dotnet_bot.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\drawable-xxhdpi\dotnet_bot.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\drawable-xhdpi\dotnet_bot.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\drawable-hdpi\dotnet_bot.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\r\drawable-mdpi\dotnet_bot.png
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauiimage.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\mauiimage.outputs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\libraryprojectimports.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_BuildLibraryImportsCache.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\case_map.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\__Microsoft.Android.Resource.Designer.cs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\res.flag
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\6CD7C47F03026257-files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resizetizer\B81013EDA07D2EB8-files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\97\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\98\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\99\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\100\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\101\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\102\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\105\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\106\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\109\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\110\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\111\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\112\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\113\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\115\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\117\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\118\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\119\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\120\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\121\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\122\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\124\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\128\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\129\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\130\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\131\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\132\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\134\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\135\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\136\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\137\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\138\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\139\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\140\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\141\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\142\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\143\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\144\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\145\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\146\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\147\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\149\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\150\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\151\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\152\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\153\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\154\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\155\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\156\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\158\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\160\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\163\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\164\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\165\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\lp\166\jl\files.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\R.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\_Microsoft.Android.Resource.Designer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\R.cs.flag
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.AssemblyInfo.cs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\XamlC.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.genruntimeconfig.cache
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\MbLinkApp.runtimeconfig.json.bin
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\com.companyname.mblinkapp.apk
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\base.zip
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\com.companyname.mblinkapp.aab
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\com.companyname.mblinkapp.apks
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\res\values\colors.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\uploadflags.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\resolvedassemblies.hash
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ar\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ca\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\cs\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\da\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\de\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\el\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\es\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\fi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\fr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\GoogleGson.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\GoogleGson.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\he\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\hi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\hr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\hu\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\id\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\it\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Java.Interop.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Java.Interop.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ja\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Jsr305Binding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Jsr305Binding.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ko\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\MbLinkApp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\MbLinkApp.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.CSharp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Controls.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Controls.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Controls.Xaml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Controls.Xaml.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Essentials.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Essentials.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Graphics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.Graphics.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Maui.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.VisualBasic.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Microsoft.Win32.Registry.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.Export.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.Export.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Mono.Android.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\mscorlib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ms\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\nb\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\netstandard.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\nl\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\pl\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\pt-BR\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\pt\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ro\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\ru\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\sk\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\sv\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.AppContext.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Buffers.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Collections.Concurrent.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Collections.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Collections.NonGeneric.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Collections.Specialized.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Configuration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Console.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Data.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Data.DataSetExtensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Data.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.Debug.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.Tools.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Drawing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Drawing.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Dynamic.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Formats.Asn1.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Formats.Tar.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Globalization.Calendars.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Globalization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Globalization.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Compression.Brotli.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Compression.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.Pipes.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Linq.Expressions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Linq.Parallel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Linq.Queryable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Memory.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Http.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Http.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.HttpListener.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Mail.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.NameResolution.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.NetworkInformation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Ping.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Quic.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Requests.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Security.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.ServicePoint.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.Sockets.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.WebClient.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.WebProxy.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Net.WebSockets.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Numerics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ObjectModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Private.CoreLib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Private.DataContractSerialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Private.Uri.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Private.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Private.Xml.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Emit.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Resources.Reader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Resources.ResourceManager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Resources.Writer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Handles.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.InteropServices.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Intrinsics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Loader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Numerics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Serialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Claims.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Principal.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.Principal.Windows.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Security.SecureString.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ServiceModel.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ServiceProcess.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.Encoding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Text.RegularExpressions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Channels.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Overlapped.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Tasks.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Thread.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.ThreadPool.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Threading.Timer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Transactions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Transactions.Local.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.ValueTuple.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Web.HttpUtility.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Windows.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.Serialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.XDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.XPath.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\th\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\tr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\uk\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\vi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\WindowsBase.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Android.Glide.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Android.Glide.DiskLruCache.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Android.Glide.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Android.Glide.GifDecoder.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Activity.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Activity.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Activity.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Activity.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.Experimental.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.Experimental.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Annotation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.AppCompat.AppCompatResources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.AppCompat.AppCompatResources.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.AppCompat.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.AppCompat.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Arch.Core.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Arch.Core.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Arch.Core.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Arch.Core.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Browser.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Browser.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CardView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CardView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Collection.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Concurrent.Futures.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Concurrent.Futures.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ConstraintLayout.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ConstraintLayout.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ConstraintLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ConstraintLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CoordinatorLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CoordinatorLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Core.Core.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Core.Core.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CursorAdapter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CursorAdapter.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CustomView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CustomView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CustomView.PoolingContainer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.CustomView.PoolingContainer.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DocumentFile.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DocumentFile.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DrawerLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DrawerLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DynamicAnimation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.DynamicAnimation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Emoji2.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Emoji2.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Emoji2.ViewsHelper.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Emoji2.ViewsHelper.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ExifInterface.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ExifInterface.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Fragment.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Fragment.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Fragment.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Fragment.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Interpolator.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Interpolator.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Legacy.Support.Core.Utils.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Legacy.Support.Core.Utils.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Common.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Common.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.LiveData.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Process.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Process.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModel.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Loader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Loader.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.LocalBroadcastManager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.LocalBroadcastManager.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Fragment.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Fragment.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.UI.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Navigation.UI.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Print.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Print.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.RecyclerView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.RecyclerView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ResourceInspection.Annotation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ResourceInspection.Annotation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SavedState.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SavedState.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SavedState.SavedState.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SavedState.SavedState.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Security.SecurityCrypto.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Security.SecurityCrypto.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SlidingPaneLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SlidingPaneLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Startup.StartupRuntime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Startup.StartupRuntime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SwipeRefreshLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.SwipeRefreshLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Tracing.Tracing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Tracing.Tracing.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Transition.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Transition.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VectorDrawable.Animated.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VectorDrawable.Animated.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VectorDrawable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VectorDrawable.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VersionedParcelable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.VersionedParcelable.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ViewPager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ViewPager.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ViewPager2.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.ViewPager2.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Window.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Window.Extensions.Core.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Window.Extensions.Core.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.AndroidX.Window.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.Android.Material.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.Android.Material.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.Crypto.Tink.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.Crypto.Tink.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.ErrorProne.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.ErrorProne.Annotations.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Google.Guava.ListenableFuture.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Jetbrains.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Kotlin.StdLib.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.Kotlin.StdLib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.AtomicFU.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.AtomicFU.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.Coroutines.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.Coroutines.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.Coroutines.Core.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.Serialization.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\Xamarin.KotlinX.Serialization.Core.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\zh-Hans\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\zh-Hant\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\zh-HK\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\arm64-v8a\_Microsoft.Android.Resource.Designer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ar\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ca\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\cs\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\da\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\de\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\el\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\es\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\fi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\fr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\GoogleGson.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\GoogleGson.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\he\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\hi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\hr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\hu\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\id\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\it\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Java.Interop.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Java.Interop.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ja\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Jsr305Binding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Jsr305Binding.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ko\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\MbLinkApp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\MbLinkApp.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.CSharp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Controls.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Controls.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Controls.Xaml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Controls.Xaml.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Essentials.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Essentials.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Graphics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.Graphics.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Maui.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.VisualBasic.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Microsoft.Win32.Registry.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.Export.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.Export.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Mono.Android.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\mscorlib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ms\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\nb\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\netstandard.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\nl\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\pl\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\pt-BR\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\pt\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ro\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\ru\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\sk\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\sv\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.AppContext.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Buffers.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Collections.Concurrent.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Collections.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Collections.NonGeneric.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Collections.Specialized.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Configuration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Console.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Data.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Data.DataSetExtensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Data.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.Debug.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.Tools.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Drawing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Drawing.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Dynamic.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Formats.Asn1.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Formats.Tar.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Globalization.Calendars.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Globalization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Globalization.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Compression.Brotli.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Compression.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.Pipes.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Linq.Expressions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Linq.Parallel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Linq.Queryable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Memory.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Http.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Http.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.HttpListener.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Mail.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.NameResolution.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.NetworkInformation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Ping.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Quic.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Requests.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Security.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.ServicePoint.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.Sockets.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.WebClient.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.WebProxy.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Net.WebSockets.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Numerics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ObjectModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Private.CoreLib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Private.DataContractSerialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Private.Uri.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Private.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Private.Xml.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Emit.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Resources.Reader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Resources.ResourceManager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Resources.Writer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Handles.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.InteropServices.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Intrinsics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Loader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Numerics.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Serialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Claims.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Principal.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.Principal.Windows.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Security.SecureString.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ServiceModel.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ServiceProcess.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.Encoding.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.Json.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Text.RegularExpressions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Channels.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Overlapped.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Tasks.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Thread.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.ThreadPool.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Threading.Timer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Transactions.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Transactions.Local.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.ValueTuple.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Web.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Web.HttpUtility.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Windows.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.Linq.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.Serialization.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.XDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.XPath.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\th\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\tr\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\uk\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\vi\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\WindowsBase.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Android.Glide.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Android.Glide.DiskLruCache.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Android.Glide.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Android.Glide.GifDecoder.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Activity.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Activity.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Activity.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Activity.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.Experimental.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.Experimental.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Annotation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.AppCompat.AppCompatResources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.AppCompat.AppCompatResources.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.AppCompat.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.AppCompat.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Arch.Core.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Arch.Core.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Arch.Core.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Arch.Core.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Browser.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Browser.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CardView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CardView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Collection.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Concurrent.Futures.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Concurrent.Futures.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ConstraintLayout.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ConstraintLayout.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ConstraintLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ConstraintLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CoordinatorLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CoordinatorLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Core.Core.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Core.Core.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CursorAdapter.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CursorAdapter.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CustomView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CustomView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CustomView.PoolingContainer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.CustomView.PoolingContainer.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DocumentFile.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DocumentFile.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DrawerLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DrawerLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DynamicAnimation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.DynamicAnimation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Emoji2.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Emoji2.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Emoji2.ViewsHelper.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Emoji2.ViewsHelper.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ExifInterface.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ExifInterface.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Fragment.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Fragment.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Fragment.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Fragment.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Interpolator.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Interpolator.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Legacy.Support.Core.Utils.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Legacy.Support.Core.Utils.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Common.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Common.Jvm.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.LiveData.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Process.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Process.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModel.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Lifecycle.ViewModelSavedState.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Loader.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Loader.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.LocalBroadcastManager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.LocalBroadcastManager.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Common.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Fragment.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Fragment.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Runtime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.Runtime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.UI.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Navigation.UI.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Print.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Print.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.RecyclerView.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.RecyclerView.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ResourceInspection.Annotation.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ResourceInspection.Annotation.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SavedState.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SavedState.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SavedState.SavedState.Ktx.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SavedState.SavedState.Ktx.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Security.SecurityCrypto.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Security.SecurityCrypto.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SlidingPaneLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SlidingPaneLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Startup.StartupRuntime.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Startup.StartupRuntime.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SwipeRefreshLayout.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.SwipeRefreshLayout.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Tracing.Tracing.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Tracing.Tracing.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Transition.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Transition.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VectorDrawable.Animated.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VectorDrawable.Animated.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VectorDrawable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VectorDrawable.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VersionedParcelable.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.VersionedParcelable.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ViewPager.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ViewPager.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ViewPager2.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.ViewPager2.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Window.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Window.Extensions.Core.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Window.Extensions.Core.Core.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.AndroidX.Window.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.Android.Material.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.Android.Material.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.Crypto.Tink.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.Crypto.Tink.Android.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.ErrorProne.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.ErrorProne.Annotations.pdb
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Google.Guava.ListenableFuture.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Jetbrains.Annotations.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Kotlin.StdLib.Common.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.Kotlin.StdLib.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.AtomicFU.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.AtomicFU.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.Coroutines.Android.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.Coroutines.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.Coroutines.Core.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.Serialization.Core.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\Xamarin.KotlinX.Serialization.Core.Jvm.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\zh-Hans\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\zh-Hant\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\zh-HK\Microsoft.Maui.Controls.resources.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\x86_64\_Microsoft.Android.Resource.Designer.dll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\typemaps.arm64-v8a.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\typemaps.x86_64.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\AndroidManifest.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\AndroidManifest.xml
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\MonoRuntimeProvider.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\JavaInteropTypeManager.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\assets\machine.config
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\mono.android.jar
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\static.flag
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\__environment__.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\environment.arm64-v8a.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\environment.x86_64.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\activity\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\activity\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\annotation\experimental\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnCancelListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\app\AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\resources\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\appcompat\widget\Toolbar_NavigationOnClickEventDispatcher.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\arch\core\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\browser\customtabs\CustomTabsClient_CustomTabsCallbackImpl.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\browser\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\cardview\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\constraintlayout\widget\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\coordinatorlayout\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\core\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\core\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\cursoradapter\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\customview\poolingcontainer\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\customview\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\documentfile\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\drawerlayout\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\dynamicanimation\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\emoji2\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\emoji2\viewsintegration\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\exifinterface\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\fragment\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\fragment\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\interpolator\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\legacy\coreutils\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\livedata\core\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\livedata\core\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\livedata\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\process\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\runtime\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\viewmodel\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\viewmodel\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\lifecycle\viewmodel\savedstate\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\loader\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\localbroadcastmanager\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\navigation\common\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\navigation\fragment\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\navigation\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\navigation\ui\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\print\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\profileinstaller\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\recyclerview\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\savedstate\ktx\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\savedstate\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\security\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\slidingpanelayout\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\startup\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\swiperefreshlayout\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\tracing\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\transition\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\vectordrawable\animated\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\vectordrawable\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\versionedparcelable\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\viewpager2\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\viewpager\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\window\extensions\core\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\androidx\window\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\bumptech\glide\gifdecoder\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\bumptech\glide\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\google\android\material\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\google\android\material\snackbar\Snackbar_SnackbarActionClickImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\microsoft\maui\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640a1f4d108c17e3f1\ClipboardChangeListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\BatteryBroadcastReceiver.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\DeviceDisplayImplementation_Listener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640a8d9a12ddbf2cf2\EnergySaverBroadcastReceiver.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ContainerView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\CustomFrameLayout.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\RecyclerViewContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ScrollLayoutManager.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellContentFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutLayout.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter_ElementViewHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRecyclerAdapter_ShellLinearLayout.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutTemplatedContentRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFlyoutTemplatedContentRenderer_HeaderContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFragmentContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellFragmentStateAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellItemRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellItemRendererBase.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellPageContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter_CustomFilter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchViewAdapter_ObjectWrapper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSearchView_ClipDrawableWrapper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSectionRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellSectionRenderer_ViewPagerPageChanged.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellToolbarTracker.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc640ec207abc449b2ca\ShellToolbarTracker_FlyoutIconDrawerDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ColorChangeRevealDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ControlsAccessibilityDelegate.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\DragAndDropGestureHandler.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\DragAndDropGestureHandler_CustomLocalStateData.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\FragmentContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\GenericAnimatorListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\GenericGlobalLayoutListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\GenericMenuClickListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\GradientStrokeDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\InnerGestureListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\InnerScaleListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\MauiViewPager.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment_CustomComponentDialog.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\MultiPageFragmentStateAdapter_1.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\PointerGestureHandler.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\TapAndPanGestureDetector.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64338477404e88479c\ToolbarExtensions_ToolbarTitleIconImageView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6439358991bbf5dbbd\DrawableContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6439358991bbf5dbbd\DrawableWrapper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64396a3fe5f8138e3f\CustomTabsServiceConnectionImpl.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64396a3fe5f8138e3f\KeepAliveService.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc643f2b18b2570eaa5a\PlatformGraphicsView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\AccessibilityDelegateCompatWrapper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\BorderDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ContainerView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ContentViewGroup.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\FragmentManagerExtensions_CallBacks.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\LayoutViewGroup.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\LocalizedDigitsKeyListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiAccessibilityDelegateCompat.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiAppCompatEditText.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiBoxView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiDatePicker.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHorizontalScrollView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHybridWebView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiHybridWebViewClient.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiLayerDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiMaterialButton.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiMaterialButton_MauiResizableDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPageControl.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPageControl_TEditClickListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPicker.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiPickerBase.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiScrollView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSearchView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiShapeView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiStepper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSwipeRefreshLayout.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiSwipeView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiTextView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiTimePicker.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebChromeClient.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\MauiWebViewClient.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\NavigationRootManager_ElementBasedFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\NavigationViewFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\PlatformTouchGraphicsView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ScopedFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StackNavigationManager_Callbacks.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StepperHandlerHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\StepperHandlerManager_StepperListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\SwipeViewPager.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\ViewFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\WebViewExtensions_JavascriptResult.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6452ffdc5b34af3a0f\WrapperView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselSpacingItemDecoration.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\CarouselViewOnScrollListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\CenterSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\DataChangeObserver.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\EdgeSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\EmptyViewAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\EndSingleSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\EndSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\GridLayoutSpanSizeLookup.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\GroupableItemsViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\ItemContentView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\ItemsViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\MauiCarouselRecyclerView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\MauiRecyclerView_3.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\NongreedySnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\NongreedySnapHelper_InitialScrollListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\PositionalSmoothScroller.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\RecyclerViewScrollListener_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\ReorderableItemsViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\ScrollHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SelectableItemsViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SelectableViewHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SimpleItemTouchHelperCallback.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SimpleViewHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SingleSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SizedItemContentView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\SpacingItemDecoration.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\StartSingleSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\StartSnapHelper.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\StructuredItemsViewAdapter_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\TemplatedItemViewHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc645d80431ce5f73f11\TextViewHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6468b6408a11370c2f\WebAuthenticatorCallbackActivity.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6468b6408a11370c2f\WebAuthenticatorIntermediateActivity.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderCallback.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderCallbackBase_1.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\ImageLoaderResultCallback.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiAppCompatActivity.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiApplication.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6488302ad6e9e4df1a\MauiApplication_ActivityLifecycleCallbacks.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc6493855b22b6fa0721\TextToSpeechInternalImplementation.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc649ff77a65592e7d55\TabbedPageManager_Listeners.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc649ff77a65592e7d55\TabbedPageManager_TempView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64a096dc44ad241142\PlatformTicker_DurationScaleListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64a25b61d9f8ee364f\FloatArrayEvaluator.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64a25b61d9f8ee364f\RectEvaluator.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64a25b61d9f8ee364f\TransitionUtils.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64a25b61d9f8ee364f\TransitionUtils_MatrixEvaluator.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64b5e713d400f589b7\LinearGradientShaderFactory.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64b5e713d400f589b7\MauiDrawable.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64b5e713d400f589b7\RadialGradientShaderFactory.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64ba438d8f48cf7e75\ActivityLifecycleContextListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64ba438d8f48cf7e75\IntermediateActivity.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\BaseCellView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\CellAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\CellRenderer_RendererHolder.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ConditionalFocusLayout.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\EntryCellEditText.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\EntryCellView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\FrameRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\GroupedListViewAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewAdapter.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_Container.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_ListViewScrollDetector.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_ListViewSwipeRefreshLayoutListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\SwitchCellView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\TableViewModelRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\TableViewRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\TextCellRenderer_TextCellView.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer_LongPressGestureListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ViewCellRenderer_ViewCellContainer_TapGestureListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ViewRenderer.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\ViewRenderer_2.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e1fb321c08285b90\VisualElementRenderer_1.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e53d2f592022988e\ConnectivityBroadcastReceiver.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64e53d2f592022988e\ConnectivityImplementation_EssentialsNetworkCallback.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f12b78a83fab058b\MainActivity.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f12b78a83fab058b\MainApplication.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\AccelerometerListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\BarometerListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\ContinuousLocationListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\GyroscopeListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\MagnetometerListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\OrientationSensorListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\SensorListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f62664462a8937a9\SingleLocationListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f728827fec74e9c3\TapWindowTracker_GestureListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64f728827fec74e9c3\Toolbar_Container.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ButtonHandler_ButtonClickListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ButtonHandler_ButtonTouchListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\HybridWebViewHandler_HybridWebViewJavaScriptInterface.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SearchBarHandler_FocusChangeListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SliderHandler_SeekBarChangeListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\SwitchHandler_CheckedChangeListener.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\crc64fcf28c0e24b4cc31\ToolbarHandler_ProcessBackClick.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\microsoft\maui\essentials\fileProvider.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\microsoft\maui\platform\MauiNavHostFragment.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\activity\contextaware\OnContextAvailableListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\app\ActionBar_OnMenuVisibilityListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\app\ActionBar_OnNavigationListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\app\ActionBar_TabListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\ActionMenuView_OnMenuItemClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\ContentFrameLayout_OnAttachListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\FitWindowsViewGroup_OnFitSystemWindowsListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\MenuItemHoverListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\PopupMenu_OnDismissListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\PopupMenu_OnMenuItemClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnCloseListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnQueryTextListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\SearchView_OnSuggestionListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\ShareActionProvider_OnShareTargetSelectedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\Toolbar_OnMenuItemClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\appcompat\widget\ViewStubCompat_OnInflateListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\constraintlayout\motion\widget\MotionLayout_TransitionListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\constraintlayout\widget\SharedValues_SharedValuesListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\app\SharedElementCallback_OnSharedElementsReadyListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\os\CancellationSignal_OnCancelListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\accessibility\AccessibilityManagerCompat_AccessibilityStateChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\accessibility\AccessibilityManagerCompat_TouchExplorationStateChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\ActionProvider_SubUiVisibilityListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\ActionProvider_VisibilityListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\DragStartHelper_OnDragStartListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\inputmethod\InputConnectionCompat_OnCommitContentListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\MenuItemCompat_OnActionExpandListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\OnApplyWindowInsetsListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\OnReceiveContentListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\ViewPropertyAnimatorListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\ViewPropertyAnimatorUpdateListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\view\WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\core\widget\NestedScrollView_OnScrollChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\customview\poolingcontainer\PoolingContainerListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\drawerlayout\widget\DrawerLayout_DrawerListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\dynamicanimation\animation\DynamicAnimation_OnAnimationEndListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\dynamicanimation\animation\DynamicAnimation_OnAnimationUpdateListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\fragment\app\FragmentManager_OnBackStackChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\fragment\app\FragmentOnAttachListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\fragment\app\FragmentResultListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\fragment\app\strictmode\FragmentStrictMode_OnViolationListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\lifecycle\ReportFragment_ActivityInitializationListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\loader\content\Loader_OnLoadCanceledListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\loader\content\Loader_OnLoadCompleteListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\navigation\NavController_OnDestinationChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\navigation\ui\AppBarConfiguration_OnNavigateUpListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\recyclerview\widget\AsyncListDiffer_ListListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_ItemAnimator_ItemAnimatorFinishedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_OnChildAttachStateChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_OnItemTouchListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\recyclerview\widget\RecyclerView_RecyclerListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\slidingpanelayout\widget\SlidingPaneLayout_PanelSlideListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\swiperefreshlayout\widget\SwipeRefreshLayout_OnRefreshListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\transition\Transition_TransitionListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\viewpager2\adapter\FragmentStateAdapter_FragmentTransactionCallback_OnPostEventListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\viewpager\widget\ViewPager_OnAdapterChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\androidx\viewpager\widget\ViewPager_OnPageChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\android\app\ApplicationRegistration.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\bumptech\glide\load\engine\cache\MemoryCache_ResourceRemovedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\bumptech\glide\manager\ConnectivityMonitor_ConnectivityListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\bumptech\glide\manager\LifecycleListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\bumptech\glide\request\RequestListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\animation\AnimatableView_ListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\appbar\AppBarLayout_LiftOnScrollListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\appbar\AppBarLayout_OnOffsetChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\behavior\HideBottomViewOnScrollBehavior_OnScrollStateChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\behavior\SwipeDismissBehavior_OnDismissListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\button\MaterialButtonToggleGroup_OnButtonCheckedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\button\MaterialButton_OnCheckedChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\card\MaterialCardView_OnCheckedChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\carousel\OnMaskChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\checkbox\MaterialCheckBox_OnCheckedStateChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\checkbox\MaterialCheckBox_OnErrorChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\chip\ChipGroup_OnCheckedChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\chip\ChipGroup_OnCheckedStateChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\datepicker\MaterialPickerOnPositiveButtonClickListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\internal\CheckableGroup_OnCheckedStateChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\internal\MaterialCheckable_OnCheckedChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\internal\ViewUtils_OnApplyWindowInsetsListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationBarView_OnItemReselectedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationBarView_OnItemSelectedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\navigation\NavigationView_OnNavigationItemSelectedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\search\SearchView_TransitionListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\shape\ShapeAppearancePathProvider_PathListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\slider\BaseOnChangeListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\slider\BaseOnSliderTouchListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\tabs\TabLayout_BaseOnTabSelectedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\textfield\TextInputLayout_OnEditTextAttachedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\com\google\android\material\textfield\TextInputLayout_OnEndIconChangedListenerImplementor.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\mono\MonoPackageManager_Resources.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\assets\AboutAssets.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\assets\FluentUI.cs
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\assets\OpenSans-Regular.ttf
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\assets\OpenSans-Semibold.ttf
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\packaged_resources
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\src\com\companyname\mblinkapp\R.java
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\_CompileJava.FileList.txt
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\compressed_assemblies.arm64-v8a.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\compressed_assemblies.x86_64.ll
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\typemaps.arm64-v8a.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\typemaps.x86_64.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\environment.arm64-v8a.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\environment.x86_64.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\compressed_assemblies.arm64-v8a.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\compressed_assemblies.x86_64.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\marshal_methods.arm64-v8a.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\marshal_methods.x86_64.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\jni_remap.arm64-v8a.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\jni_remap.x86_64.o
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\app_shared_libraries\arm64-v8a\libxamarin-app.so
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\app_shared_libraries\x86_64\libxamarin-app.so
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\classes.dex
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\android\bin\classes2.dex
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_CleanIntermediateIfNeeded.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_CompileJava.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_CompileToDalvik.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_ConvertCustomView.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_ConvertResourcesCases.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_GenerateJavaStubs.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_GeneratePackageManagerJava.stamp
C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-android\stamp\_ResolveLibraryProjectImports.stamp
