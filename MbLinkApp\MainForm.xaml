<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MbLinkApp.MainForm"
             Title="Mercedes Vehicle Selection - XEntry"
             BackgroundColor="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Frame Grid.Row="0"
               BackgroundColor="#1E3A8A"
               CornerRadius="0"
               Padding="15"
               HasShadow="True">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <Label Grid.Column="0"
                       Text="Mercedes-Benz XEntry Vehicle Selection"
                       FontSize="20"
                       FontAttributes="Bold"
                       TextColor="White"
                       VerticalOptions="Center" />

                <Button Grid.Column="1"
                        Text="Logout"
                        BackgroundColor="#DC3545"
                        TextColor="White"
                        FontSize="12"
                        CornerRadius="4"
                        Padding="10,5"
                        Clicked="OnLogoutClicked" />
            </Grid>
        </Frame>

        <!-- Top Menu Section -->
        <Frame Grid.Row="1"
               BackgroundColor="#E5E7EB"
               CornerRadius="0"
               Padding="10"
               HasShadow="False">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0"
                        x:Name="PassengerCarsBtn"
                        Text="Binek Araçları"
                        BackgroundColor="#3B82F6"
                        TextColor="White"
                        FontSize="14"
                        FontAttributes="Bold"
                        CornerRadius="6"
                        Margin="5"
                        HeightRequest="45"
                        Clicked="OnPassengerCarsClicked" />

                <Button Grid.Column="1"
                        x:Name="SuperSportBtn"
                        Text="Süper Spor Arabalar"
                        BackgroundColor="#6B7280"
                        TextColor="White"
                        FontSize="14"
                        FontAttributes="Bold"
                        CornerRadius="6"
                        Margin="5"
                        HeightRequest="45"
                        Clicked="OnSuperSportClicked" />

                <Button Grid.Column="2"
                        x:Name="TransporterBtn"
                        Text="Transporter (Taşıma Aracı)"
                        BackgroundColor="#6B7280"
                        TextColor="White"
                        FontSize="14"
                        FontAttributes="Bold"
                        CornerRadius="6"
                        Margin="5"
                        HeightRequest="45"
                        Clicked="OnTransporterClicked" />
            </Grid>
        </Frame>

        <!-- Main Content Area -->
        <Grid Grid.Row="2" Padding="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Mercedes Class Filter Menu -->
            <Frame Grid.Column="0"
                   BackgroundColor="White"
                   CornerRadius="8"
                   Padding="15"
                   Margin="0,0,10,0"
                   HasShadow="True">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Mercedes Sınıfları"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#1E3A8A"
                           HorizontalOptions="Center"
                           Margin="0,0,0,15" />

                    <Button x:Name="ClassABBtn"
                            Text="A/B Sınıfı Araçlar"
                            BackgroundColor="#3B82F6"
                            TextColor="White"
                            FontSize="13"
                            CornerRadius="6"
                            HeightRequest="40"
                            Clicked="OnClassABClicked" />

                    <Button x:Name="ClassCBtn"
                            Text="C Sınıfı Araçlar"
                            BackgroundColor="#E5E7EB"
                            TextColor="#374151"
                            FontSize="13"
                            CornerRadius="6"
                            HeightRequest="40"
                            Clicked="OnClassCClicked" />

                    <Button x:Name="ClassEBtn"
                            Text="E Sınıfı Araçlar"
                            BackgroundColor="#E5E7EB"
                            TextColor="#374151"
                            FontSize="13"
                            CornerRadius="6"
                            HeightRequest="40"
                            Clicked="OnClassEClicked" />

                    <Button x:Name="ClassEQBtn"
                            Text="EQ Sınıfı Araçlar"
                            BackgroundColor="#E5E7EB"
                            TextColor="#374151"
                            FontSize="13"
                            CornerRadius="6"
                            HeightRequest="40"
                            Clicked="OnClassEQClicked" />

                    <Button x:Name="ClassSUVBtn"
                            Text="SUV Sınıfı Araçlar"
                            BackgroundColor="#E5E7EB"
                            TextColor="#374151"
                            FontSize="13"
                            CornerRadius="6"
                            HeightRequest="40"
                            Clicked="OnClassSUVClicked" />
                </VerticalStackLayout>
            </Frame>

            <!-- Right Panel - Vehicle Models Display -->
            <Frame Grid.Column="1"
                   BackgroundColor="White"
                   CornerRadius="8"
                   Padding="20"
                   Margin="10,0,0,0"
                   HasShadow="True">
                <ScrollView>
                    <VerticalStackLayout Spacing="15">
                        <Label x:Name="VehicleTypeLabel"
                               Text="Binek Araçları - A/B Sınıfı"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="#1E3A8A"
                               HorizontalOptions="Center"
                               Margin="0,0,0,20" />

                        <!-- Vehicle Models Grid -->
                        <CollectionView x:Name="VehicleModelsCollectionView"
                                        BackgroundColor="Transparent"
                                        SelectionMode="Single"
                                        SelectionChanged="OnVehicleSelectionChanged">
                            <CollectionView.ItemsLayout>
                                <GridItemsLayout Orientation="Vertical"
                                                 Span="3"
                                                 HorizontalItemSpacing="15"
                                                 VerticalItemSpacing="15" />
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Frame BackgroundColor="#F8F9FA"
                                           CornerRadius="8"
                                           Padding="15"
                                           HasShadow="True"
                                           HeightRequest="180">
                                        <VerticalStackLayout Spacing="10">
                                            <!-- Vehicle Image Placeholder -->
                                            <Frame BackgroundColor="#E5E7EB"
                                                   CornerRadius="6"
                                                   HeightRequest="100"
                                                   HasShadow="False">
                                                <Label Text="🚗"
                                                       FontSize="40"
                                                       HorizontalOptions="Center"
                                                       VerticalOptions="Center"
                                                       TextColor="#6B7280" />
                                            </Frame>

                                            <!-- Vehicle Model Name -->
                                            <Label Text="{Binding ModelName}"
                                                   FontSize="14"
                                                   FontAttributes="Bold"
                                                   TextColor="#1E3A8A"
                                                   HorizontalOptions="Center" />

                                            <!-- Vehicle Code -->
                                            <Label Text="{Binding ModelCode}"
                                                   FontSize="12"
                                                   TextColor="#6B7280"
                                                   HorizontalOptions="Center" />
                                        </VerticalStackLayout>
                                    </Frame>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </VerticalStackLayout>
                </ScrollView>
            </Frame>
        </Grid>

        <!-- Status Bar -->
        <Frame Grid.Row="3"
               BackgroundColor="#374151"
               CornerRadius="0"
               Padding="10"
               HasShadow="False">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <Label Grid.Column="0"
                       x:Name="StatusLabel"
                       Text="Mercedes-Benz XEntry - Ready"
                       FontSize="12"
                       TextColor="White"
                       VerticalOptions="Center" />

                <Label Grid.Column="1"
                       x:Name="LoginTimeLabel"
                       Text=""
                       FontSize="12"
                       TextColor="White"
                       VerticalOptions="Center" />
            </Grid>
        </Frame>
    </Grid>

</ContentPage>
