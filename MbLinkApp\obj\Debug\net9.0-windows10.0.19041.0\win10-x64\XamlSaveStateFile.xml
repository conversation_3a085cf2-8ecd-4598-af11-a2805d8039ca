﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><XamlFeatureControlFlags>EnableXBindDiagnostics, EnableDefaultValidationContextGeneration, EnableWin32Codegen, UsingCSWinRT</XamlFeatureControlFlags><ReferenceAssemblyList><LocalAssembly PathName="c:\users\<USER>\documents\augment-projects\mblink maui\mblinkapp\obj\debug\net9.0-windows10.0.19041.0\win10-x64\intermediatexaml\mblinkapp.dll" HashGuid="98f2494f-1e94-c115-9246-9470d786e05e" /><ReferenceAssembly PathName="c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\lib_manual\net6.0-windows10.0.17763.0\microsoft.web.webview2.core.projection.dll" HashGuid="d784d0bb-01d0-9bee-97b6-7bb2d999d35a" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="Platforms\Windows\App.xaml" ClassFullName="MbLinkApp.WinUI.App" GeneratedCodePathPrefix="C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\Platforms\Windows\App" XamlFileTimeAtLastCompileInTicks="638882099584422558" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>