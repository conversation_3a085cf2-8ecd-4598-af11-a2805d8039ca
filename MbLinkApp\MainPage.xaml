<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MbLinkApp.MainPage"
             Title="MbLink Login">

    <ScrollView>
        <VerticalStackLayout
            Padding="40"
            Spacing="20"
            VerticalOptions="Center">

            <!-- Logo/Title -->
            <Label
                Text="MbLink"
                FontSize="32"
                FontAttributes="Bold"
                HorizontalOptions="Center"
                TextColor="#2196F3"
                Margin="0,0,0,30" />

            <!-- Login Form -->
            <Frame BackgroundColor="White"
                   HasShadow="True"
                   CornerRadius="10"
                   Padding="30">
                <VerticalStackLayout Spacing="15">

                    <!-- Username -->
                    <Label Text="Username"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#333" />
                    <Entry x:Name="UsernameEntry"
                           Placeholder="Enter your username"
                           FontSize="16"
                           BackgroundColor="#F5F5F5"
                           TextColor="#333"
                           Completed="OnEntryCompleted" />

                    <!-- Password -->
                    <Label Text="Password"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#333" />
                    <Entry x:Name="PasswordEntry"
                           Placeholder="Enter your password"
                           IsPassword="True"
                           FontSize="16"
                           BackgroundColor="#F5F5F5"
                           TextColor="#333"
                           Completed="OnEntryCompleted" />

                    <!-- Token -->
                    <Label Text="Token"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#333" />
                    <Entry x:Name="TokenEntry"
                           Placeholder="Enter your token"
                           FontSize="16"
                           BackgroundColor="#F5F5F5"
                           TextColor="#333"
                           Completed="OnEntryCompleted" />

                    <!-- Remember Me -->
                    <StackLayout Orientation="Horizontal"
                                 Margin="0,10,0,0">
                        <CheckBox x:Name="RememberMeCheckBox"
                                  Color="#2196F3" />
                        <Label Text="Remember Me"
                               FontSize="14"
                               TextColor="#666"
                               VerticalOptions="Center" />
                    </StackLayout>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton"
                            Text="Login"
                            BackgroundColor="#2196F3"
                            TextColor="White"
                            FontSize="18"
                            FontAttributes="Bold"
                            CornerRadius="8"
                            HeightRequest="50"
                            Margin="0,20,0,0"
                            Clicked="OnLoginClicked" />
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
