aotassemblies=false
androidaddkeepalives=
androidaotmode=interpreter
androidembedprofilers=
androidenableprofiledaot=
androiddextool=d8
androidlinktool=
androidlinkresources=
androidpackageformat=apk
embedassembliesintoapk=false
androidlinkmode=none
androidlinkskip=
androidsdkbuildtoolsversion=35.0.0
androidsdkpath=c:\program files (x86)\android\android-sdk\
androidndkpath=c:\program files (x86)\android\android-sdk\ndk-bundle\
javasdkpath=c:\program files (x86)\android\openjdk\jdk-17.0.12\
androidsequencepointsmode=none
androidnetsdkversion=35.0.39
monosymbolarchive=false
androiduselatestplatformsdk=false
targetframeworkversion=v9.0
androidcreatepackageperabi=
androidgeneratejnimarshalmethods=false
os=windows_nt
androidincludedebugsymbols=true
androidpackagenamingpolicy=lowercasecrc64
_nugetassetsfilehash=1e38119e5c57f85f5b6d1401dedc3eb7fa414ac850f53b955659bb8be94a5803
typemapkind=strings-asm
androidmanifestplaceholders=
projectfullpath=c:\users\<USER>\documents\augment-projects\mblink maui\mblinkapp\mblinkapp.csproj
androidusedesignerassembly=true
