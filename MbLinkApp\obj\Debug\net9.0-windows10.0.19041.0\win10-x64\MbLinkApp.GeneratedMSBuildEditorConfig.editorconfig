is_global = true
build_property.TargetFramework = net9.0-windows10.0.19041.0
build_property.TargetPlatformMinVersion = 10.0.17763.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 9A19103F-16F7-4668-BE54-9A1E7A4F7556
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MbLinkApp
build_property.ProjectDir = C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotExportsEnabled = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptIn = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptOut = 
build_property.CsWinRTCcwLookupTableGeneratorEnabled = true
build_property.CsWinRTMergeReferencedActivationFactories = 
build_property.CsWinRTAotWarningLevel = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/MainForm.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.MainForm.xaml
build_metadata.AdditionalFiles.TargetPath = MainForm.xaml
build_metadata.AdditionalFiles.RelativePath = MainForm.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Colors.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Styles.xaml
