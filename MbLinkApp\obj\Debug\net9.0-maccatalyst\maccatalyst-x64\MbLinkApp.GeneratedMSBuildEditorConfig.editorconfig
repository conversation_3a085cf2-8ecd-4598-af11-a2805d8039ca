is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net9.0-maccatalyst
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MbLinkApp
build_property.ProjectDir = C:\Users\<USER>\Documents\augment-projects\MbLink MAUI\MbLinkApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/MainForm.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.MainForm.xaml
build_metadata.AdditionalFiles.TargetPath = MainForm.xaml
build_metadata.AdditionalFiles.RelativePath = MainForm.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Colors.xaml

[C:/Users/<USER>/Documents/augment-projects/MbLink MAUI/MbLinkApp/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = MbLinkApp.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Styles.xaml
