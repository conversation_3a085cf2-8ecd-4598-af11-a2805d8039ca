namespace MbLinkApp;

public partial class MainForm : ContentPage
{
	public MainForm()
	{
		InitializeComponent();
		
		// Set login time
		LoginTimeLabel.Text = $"Login time: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";
		
#if WINDOWS
		// Set window size for Windows platform
		Microsoft.Maui.Handlers.WindowHandler.Mapper.AppendToMapping(nameof(IWindow), (handler, view) =>
		{
			var mauiWindow = handler.VirtualView;
			var nativeWindow = handler.PlatformView;
			nativeWindow.Activate();
			IntPtr windowHandle = WinRT.Interop.WindowNative.GetWindowHandle(nativeWindow);
			Microsoft.UI.WindowId windowId = Microsoft.UI.Win32Interop.GetWindowIdFromWindow(windowHandle);
			Microsoft.UI.Windowing.AppWindow appWindow = Microsoft.UI.Windowing.AppWindow.GetFromWindowId(windowId);
			appWindow.Resize(new Windows.Graphics.SizeInt32(600, 700));
			
			// Center the window
			var displayArea = Microsoft.UI.Windowing.DisplayArea.GetFromWindowId(windowId, Microsoft.UI.Windowing.DisplayAreaFallback.Nearest);
			if (displayArea is not null)
			{
				var centerPosition = new Windows.Graphics.PointInt32
				{
					X = (displayArea.WorkArea.Width - 600) / 2,
					Y = (displayArea.WorkArea.Height - 700) / 2
				};
				appWindow.Move(centerPosition);
			}
			
			appWindow.Title = "MbLink - Main Application";
		});
#endif
	}

	private async void OnDataManagementClicked(object sender, EventArgs e)
	{
		await DisplayAlert("Data Management", "Data Management module will be implemented here.", "OK");
	}

	private async void OnReportsClicked(object sender, EventArgs e)
	{
		await DisplayAlert("Reports", "Reports module will be implemented here.", "OK");
	}

	private async void OnSettingsClicked(object sender, EventArgs e)
	{
		await DisplayAlert("Settings", "Settings module will be implemented here.", "OK");
	}

	private async void OnAboutClicked(object sender, EventArgs e)
	{
		string aboutMessage = "MbLink Application\n\n" +
							  "Version: 1.0.0\n" +
							  "Developed with .NET MAUI\n" +
							  "© 2024 MbLink";
		await DisplayAlert("About", aboutMessage, "OK");
	}

	private async void OnLogoutClicked(object sender, EventArgs e)
	{
		bool result = await DisplayAlert("Logout", "Are you sure you want to logout?", "Yes", "No");
		if (result)
		{
			// Navigate back to login page
			await Navigation.PopAsync();
		}
	}
}
