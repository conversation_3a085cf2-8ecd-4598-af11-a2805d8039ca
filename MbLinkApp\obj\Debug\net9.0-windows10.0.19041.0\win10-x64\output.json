{"GeneratedCodeFiles": [], "GeneratedXamlFiles": ["C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\Platforms\\Windows\\App.xaml"], "GeneratedXbfFiles": ["C:\\Users\\<USER>\\Documents\\augment-projects\\MbLink MAUI\\MbLinkApp\\obj\\Debug\\net9.0-windows10.0.19041.0\\win10-x64\\Platforms\\Windows\\App.xbf"], "GeneratedXamlPagesFiles": [], "MSBuildLogEntries": [{"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 21:14:26: 810 perfXC_StartPass2, MbLinkApp"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 21:14:26: 833 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 21:14:26: 953 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 21:14:26: 971 perfXC_RestoredGeneratedPass2CodeFileBackup"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 21:14:26: 976 perfXC_RestoredTypeInfoBackup"}]}