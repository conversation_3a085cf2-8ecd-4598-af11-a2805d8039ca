package crc64338477404e88479c;


public class DragAndDropGestureHandler_CustomLocalStateData
	extends java.lang.Object
	implements
		mono.android.IGCUserPeer
{
/** @hide */
	public static final String __md_methods;
	static {
		__md_methods = 
			"";
		mono.android.Runtime.register ("Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler+CustomLocalStateData, Microsoft.Maui.Controls", DragAndDropGestureHandler_CustomLocalStateData.class, __md_methods);
	}

	public DragAndDropGestureHandler_CustomLocalStateData ()
	{
		super ();
		if (getClass () == DragAndDropGestureHandler_CustomLocalStateData.class) {
			mono.android.TypeManager.Activate ("Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler+CustomLocalStateData, Microsoft.Maui.Controls", "", this, new java.lang.Object[] {  });
		}
	}

	private java.util.ArrayList refList;
	public void monodroidAddReference (java.lang.Object obj)
	{
		if (refList == null)
			refList = new java.util.ArrayList ();
		refList.add (obj);
	}

	public void monodroidClearReferences ()
	{
		if (refList != null)
			refList.clear ();
	}
}
