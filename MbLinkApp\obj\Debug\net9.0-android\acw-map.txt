MbLinkApp.MainActivity, MbLinkApp;crc64f12b78a83fab058b.MainActivity
MbLinkApp.MainActivity;crc64f12b78a83fab058b.MainActivity
mblinkapp.MainActivity;crc64f12b78a83fab058b.MainActivity
MbLinkApp.MainApplication, MbLinkApp;crc64f12b78a83fab058b.MainApplication
MbLinkApp.MainApplication;crc64f12b78a83fab058b.MainApplication
mblinkapp.MainApplication;crc64f12b78a83fab058b.MainApplication
GoogleGson.IExclusionStrategy, GoogleGson;com.google.gson.ExclusionStrategy
GoogleGson.IExclusionStrategy;com.google.gson.ExclusionStrategy
com.google.gson.ExclusionStrategy;com.google.gson.ExclusionStrategy
GoogleGson.IFieldNamingStrategy, GoogleGson;com.google.gson.FieldNamingStrategy
GoogleGson.IFieldNamingStrategy;com.google.gson.FieldNamingStrategy
com.google.gson.FieldNamingStrategy;com.google.gson.FieldNamingStrategy
GoogleGson.IInstanceCreator, GoogleGson;com.google.gson.InstanceCreator
GoogleGson.IInstanceCreator;com.google.gson.InstanceCreator
com.google.gson.InstanceCreator;com.google.gson.InstanceCreator
GoogleGson.IJsonDeserializationContext, GoogleGson;com.google.gson.JsonDeserializationContext
GoogleGson.IJsonDeserializationContext;com.google.gson.JsonDeserializationContext
com.google.gson.JsonDeserializationContext;com.google.gson.JsonDeserializationContext
GoogleGson.IJsonDeserializer, GoogleGson;com.google.gson.JsonDeserializer
GoogleGson.IJsonDeserializer;com.google.gson.JsonDeserializer
com.google.gson.JsonDeserializer;com.google.gson.JsonDeserializer
GoogleGson.IJsonSerializationContext, GoogleGson;com.google.gson.JsonSerializationContext
GoogleGson.IJsonSerializationContext;com.google.gson.JsonSerializationContext
com.google.gson.JsonSerializationContext;com.google.gson.JsonSerializationContext
GoogleGson.IJsonSerializer, GoogleGson;com.google.gson.JsonSerializer
GoogleGson.IJsonSerializer;com.google.gson.JsonSerializer
com.google.gson.JsonSerializer;com.google.gson.JsonSerializer
GoogleGson.IReflectionAccessFilter, GoogleGson;com.google.gson.ReflectionAccessFilter
GoogleGson.IReflectionAccessFilter;com.google.gson.ReflectionAccessFilter
com.google.gson.ReflectionAccessFilter;com.google.gson.ReflectionAccessFilter
GoogleGson.IToNumberStrategy, GoogleGson;com.google.gson.ToNumberStrategy
GoogleGson.IToNumberStrategy;com.google.gson.ToNumberStrategy
com.google.gson.ToNumberStrategy;com.google.gson.ToNumberStrategy
GoogleGson.ITypeAdapterFactory, GoogleGson;com.google.gson.TypeAdapterFactory
GoogleGson.ITypeAdapterFactory;com.google.gson.TypeAdapterFactory
com.google.gson.TypeAdapterFactory;com.google.gson.TypeAdapterFactory
GoogleGson.Annotations.IExpose, GoogleGson;com.google.gson.annotations.Expose
GoogleGson.Annotations.IExpose;com.google.gson.annotations.Expose
com.google.gson.annotations.Expose;com.google.gson.annotations.Expose
GoogleGson.Annotations.IJsonAdapter, GoogleGson;com.google.gson.annotations.JsonAdapter
GoogleGson.Annotations.IJsonAdapter;com.google.gson.annotations.JsonAdapter
com.google.gson.annotations.JsonAdapter;com.google.gson.annotations.JsonAdapter
GoogleGson.Annotations.ISerializedName, GoogleGson;com.google.gson.annotations.SerializedName
GoogleGson.Annotations.ISerializedName;com.google.gson.annotations.SerializedName
com.google.gson.annotations.SerializedName;com.google.gson.annotations.SerializedName
GoogleGson.Annotations.ISince, GoogleGson;com.google.gson.annotations.Since
GoogleGson.Annotations.ISince;com.google.gson.annotations.Since
com.google.gson.annotations.Since;com.google.gson.annotations.Since
GoogleGson.Annotations.IUntil, GoogleGson;com.google.gson.annotations.Until
GoogleGson.Annotations.IUntil;com.google.gson.annotations.Until
com.google.gson.annotations.Until;com.google.gson.annotations.Until
Microsoft.Maui.Controls.TapWindowTracker+GestureListener, Microsoft.Maui.Controls;crc64f728827fec74e9c3.TapWindowTracker_GestureListener
Microsoft.Maui.Controls.TapWindowTracker.GestureListener;crc64f728827fec74e9c3.TapWindowTracker_GestureListener
microsoft.maui.controls.TapWindowTracker_GestureListener;crc64f728827fec74e9c3.TapWindowTracker_GestureListener
Microsoft.Maui.Controls.Toolbar+Container, Microsoft.Maui.Controls;crc64f728827fec74e9c3.Toolbar_Container
Microsoft.Maui.Controls.Toolbar.Container;crc64f728827fec74e9c3.Toolbar_Container
microsoft.maui.controls.Toolbar_Container;crc64f728827fec74e9c3.Toolbar_Container
Microsoft.Maui.Controls.Platform.ColorChangeRevealDrawable, Microsoft.Maui.Controls;crc64338477404e88479c.ColorChangeRevealDrawable
Microsoft.Maui.Controls.Platform.ColorChangeRevealDrawable;crc64338477404e88479c.ColorChangeRevealDrawable
microsoft.maui.controls.platform.ColorChangeRevealDrawable;crc64338477404e88479c.ColorChangeRevealDrawable
Microsoft.Maui.Controls.Platform.ControlsAccessibilityDelegate, Microsoft.Maui.Controls;crc64338477404e88479c.ControlsAccessibilityDelegate
Microsoft.Maui.Controls.Platform.ControlsAccessibilityDelegate;crc64338477404e88479c.ControlsAccessibilityDelegate
microsoft.maui.controls.platform.ControlsAccessibilityDelegate;crc64338477404e88479c.ControlsAccessibilityDelegate
Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler, Microsoft.Maui.Controls;crc64338477404e88479c.DragAndDropGestureHandler
Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler;crc64338477404e88479c.DragAndDropGestureHandler
microsoft.maui.controls.platform.DragAndDropGestureHandler;crc64338477404e88479c.DragAndDropGestureHandler
Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler+CustomLocalStateData, Microsoft.Maui.Controls;crc64338477404e88479c.DragAndDropGestureHandler_CustomLocalStateData
Microsoft.Maui.Controls.Platform.DragAndDropGestureHandler.CustomLocalStateData;crc64338477404e88479c.DragAndDropGestureHandler_CustomLocalStateData
microsoft.maui.controls.platform.DragAndDropGestureHandler_CustomLocalStateData;crc64338477404e88479c.DragAndDropGestureHandler_CustomLocalStateData
Microsoft.Maui.Controls.Platform.ToolbarExtensions+ToolbarTitleIconImageView, Microsoft.Maui.Controls;crc64338477404e88479c.ToolbarExtensions_ToolbarTitleIconImageView
Microsoft.Maui.Controls.Platform.ToolbarExtensions.ToolbarTitleIconImageView;crc64338477404e88479c.ToolbarExtensions_ToolbarTitleIconImageView
microsoft.maui.controls.platform.ToolbarExtensions_ToolbarTitleIconImageView;crc64338477404e88479c.ToolbarExtensions_ToolbarTitleIconImageView
Microsoft.Maui.Controls.Platform.FragmentContainer, Microsoft.Maui.Controls;crc64338477404e88479c.FragmentContainer
Microsoft.Maui.Controls.Platform.FragmentContainer;crc64338477404e88479c.FragmentContainer
microsoft.maui.controls.platform.FragmentContainer;crc64338477404e88479c.FragmentContainer
Microsoft.Maui.Controls.Platform.GenericAnimatorListener, Microsoft.Maui.Controls;crc64338477404e88479c.GenericAnimatorListener
Microsoft.Maui.Controls.Platform.GenericAnimatorListener;crc64338477404e88479c.GenericAnimatorListener
microsoft.maui.controls.platform.GenericAnimatorListener;crc64338477404e88479c.GenericAnimatorListener
Microsoft.Maui.Controls.Platform.GenericGlobalLayoutListener, Microsoft.Maui.Controls;crc64338477404e88479c.GenericGlobalLayoutListener
Microsoft.Maui.Controls.Platform.GenericGlobalLayoutListener;crc64338477404e88479c.GenericGlobalLayoutListener
microsoft.maui.controls.platform.GenericGlobalLayoutListener;crc64338477404e88479c.GenericGlobalLayoutListener
Microsoft.Maui.Controls.Platform.GenericMenuClickListener, Microsoft.Maui.Controls;crc64338477404e88479c.GenericMenuClickListener
Microsoft.Maui.Controls.Platform.GenericMenuClickListener;crc64338477404e88479c.GenericMenuClickListener
microsoft.maui.controls.platform.GenericMenuClickListener;crc64338477404e88479c.GenericMenuClickListener
Microsoft.Maui.Controls.Platform.GradientStrokeDrawable, Microsoft.Maui.Controls;crc64338477404e88479c.GradientStrokeDrawable
Microsoft.Maui.Controls.Platform.GradientStrokeDrawable;crc64338477404e88479c.GradientStrokeDrawable
microsoft.maui.controls.platform.GradientStrokeDrawable;crc64338477404e88479c.GradientStrokeDrawable
Microsoft.Maui.Controls.Platform.InnerGestureListener, Microsoft.Maui.Controls;crc64338477404e88479c.InnerGestureListener
Microsoft.Maui.Controls.Platform.InnerGestureListener;crc64338477404e88479c.InnerGestureListener
microsoft.maui.controls.platform.InnerGestureListener;crc64338477404e88479c.InnerGestureListener
Microsoft.Maui.Controls.Platform.InnerScaleListener, Microsoft.Maui.Controls;crc64338477404e88479c.InnerScaleListener
Microsoft.Maui.Controls.Platform.InnerScaleListener;crc64338477404e88479c.InnerScaleListener
microsoft.maui.controls.platform.InnerScaleListener;crc64338477404e88479c.InnerScaleListener
Microsoft.Maui.Controls.Platform.MauiViewPager, Microsoft.Maui.Controls;crc64338477404e88479c.MauiViewPager
Microsoft.Maui.Controls.Platform.MauiViewPager;crc64338477404e88479c.MauiViewPager
microsoft.maui.controls.platform.MauiViewPager;crc64338477404e88479c.MauiViewPager
Microsoft.Maui.Controls.Platform.MultiPageFragmentStateAdapter`1, Microsoft.Maui.Controls;crc64338477404e88479c.MultiPageFragmentStateAdapter_1
Microsoft.Maui.Controls.Platform.MultiPageFragmentStateAdapter`1;crc64338477404e88479c.MultiPageFragmentStateAdapter_1
microsoft.maui.controls.platform.MultiPageFragmentStateAdapter_1;crc64338477404e88479c.MultiPageFragmentStateAdapter_1
Microsoft.Maui.Controls.Platform.PointerGestureHandler, Microsoft.Maui.Controls;crc64338477404e88479c.PointerGestureHandler
Microsoft.Maui.Controls.Platform.PointerGestureHandler;crc64338477404e88479c.PointerGestureHandler
microsoft.maui.controls.platform.PointerGestureHandler;crc64338477404e88479c.PointerGestureHandler
Microsoft.Maui.Controls.Platform.TapAndPanGestureDetector, Microsoft.Maui.Controls;crc64338477404e88479c.TapAndPanGestureDetector
Microsoft.Maui.Controls.Platform.TapAndPanGestureDetector;crc64338477404e88479c.TapAndPanGestureDetector
microsoft.maui.controls.platform.TapAndPanGestureDetector;crc64338477404e88479c.TapAndPanGestureDetector
Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment, Microsoft.Maui.Controls;crc64338477404e88479c.ModalNavigationManager_ModalFragment
Microsoft.Maui.Controls.Platform.ModalNavigationManager.ModalFragment;crc64338477404e88479c.ModalNavigationManager_ModalFragment
microsoft.maui.controls.platform.ModalNavigationManager_ModalFragment;crc64338477404e88479c.ModalNavigationManager_ModalFragment
Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment+CustomComponentDialog, Microsoft.Maui.Controls;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog
Microsoft.Maui.Controls.Platform.ModalNavigationManager.ModalFragment.CustomComponentDialog;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog
microsoft.maui.controls.platform.ModalNavigationManager_ModalFragment_CustomComponentDialog;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog
Microsoft.Maui.Controls.Platform.ModalNavigationManager+ModalFragment+CustomComponentDialog+CallBack, Microsoft.Maui.Controls;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack
Microsoft.Maui.Controls.Platform.ModalNavigationManager.ModalFragment.CustomComponentDialog.CallBack;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack
microsoft.maui.controls.platform.ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack;crc64338477404e88479c.ModalNavigationManager_ModalFragment_CustomComponentDialog_CallBack
Microsoft.Maui.Controls.Platform.Compatibility.ContainerView, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ContainerView
Microsoft.Maui.Controls.Platform.Compatibility.ContainerView;crc640ec207abc449b2ca.ContainerView
microsoft.maui.controls.platform.compatibility.ContainerView;crc640ec207abc449b2ca.ContainerView
Microsoft.Maui.Controls.Platform.Compatibility.CustomFrameLayout, Microsoft.Maui.Controls;crc640ec207abc449b2ca.CustomFrameLayout
Microsoft.Maui.Controls.Platform.Compatibility.CustomFrameLayout;crc640ec207abc449b2ca.CustomFrameLayout
microsoft.maui.controls.platform.compatibility.CustomFrameLayout;crc640ec207abc449b2ca.CustomFrameLayout
Microsoft.Maui.Controls.Platform.Compatibility.ShellContentFragment, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellContentFragment
Microsoft.Maui.Controls.Platform.Compatibility.ShellContentFragment;crc640ec207abc449b2ca.ShellContentFragment
microsoft.maui.controls.platform.compatibility.ShellContentFragment;crc640ec207abc449b2ca.ShellContentFragment
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutLayout, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutLayout
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutLayout;crc640ec207abc449b2ca.ShellFlyoutLayout
microsoft.maui.controls.platform.compatibility.ShellFlyoutLayout;crc640ec207abc449b2ca.ShellFlyoutLayout
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter
microsoft.maui.controls.platform.compatibility.ShellFlyoutRecyclerAdapter;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter+ShellLinearLayout, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ShellLinearLayout
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter.ShellLinearLayout;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ShellLinearLayout
microsoft.maui.controls.platform.compatibility.ShellFlyoutRecyclerAdapter_ShellLinearLayout;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ShellLinearLayout
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter+ElementViewHolder, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ElementViewHolder
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRecyclerAdapter.ElementViewHolder;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ElementViewHolder
microsoft.maui.controls.platform.compatibility.ShellFlyoutRecyclerAdapter_ElementViewHolder;crc640ec207abc449b2ca.ShellFlyoutRecyclerAdapter_ElementViewHolder
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRenderer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutRenderer;crc640ec207abc449b2ca.ShellFlyoutRenderer
microsoft.maui.controls.platform.compatibility.ShellFlyoutRenderer;crc640ec207abc449b2ca.ShellFlyoutRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutTemplatedContentRenderer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutTemplatedContentRenderer;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer
microsoft.maui.controls.platform.compatibility.ShellFlyoutTemplatedContentRenderer;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutTemplatedContentRenderer+HeaderContainer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer_HeaderContainer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFlyoutTemplatedContentRenderer.HeaderContainer;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer_HeaderContainer
microsoft.maui.controls.platform.compatibility.ShellFlyoutTemplatedContentRenderer_HeaderContainer;crc640ec207abc449b2ca.ShellFlyoutTemplatedContentRenderer_HeaderContainer
Microsoft.Maui.Controls.Platform.Compatibility.RecyclerViewContainer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.RecyclerViewContainer
Microsoft.Maui.Controls.Platform.Compatibility.RecyclerViewContainer;crc640ec207abc449b2ca.RecyclerViewContainer
microsoft.maui.controls.platform.compatibility.RecyclerViewContainer;crc640ec207abc449b2ca.RecyclerViewContainer
Microsoft.Maui.Controls.Platform.Compatibility.ScrollLayoutManager, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ScrollLayoutManager
Microsoft.Maui.Controls.Platform.Compatibility.ScrollLayoutManager;crc640ec207abc449b2ca.ScrollLayoutManager
microsoft.maui.controls.platform.compatibility.ScrollLayoutManager;crc640ec207abc449b2ca.ScrollLayoutManager
Microsoft.Maui.Controls.Platform.Compatibility.ShellFragmentContainer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFragmentContainer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFragmentContainer;crc640ec207abc449b2ca.ShellFragmentContainer
microsoft.maui.controls.platform.compatibility.ShellFragmentContainer;crc640ec207abc449b2ca.ShellFragmentContainer
Microsoft.Maui.Controls.Platform.Compatibility.ShellFragmentStateAdapter, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellFragmentStateAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellFragmentStateAdapter;crc640ec207abc449b2ca.ShellFragmentStateAdapter
microsoft.maui.controls.platform.compatibility.ShellFragmentStateAdapter;crc640ec207abc449b2ca.ShellFragmentStateAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellItemRenderer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellItemRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellItemRenderer;crc640ec207abc449b2ca.ShellItemRenderer
microsoft.maui.controls.platform.compatibility.ShellItemRenderer;crc640ec207abc449b2ca.ShellItemRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellItemRendererBase, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellItemRendererBase
Microsoft.Maui.Controls.Platform.Compatibility.ShellItemRendererBase;crc640ec207abc449b2ca.ShellItemRendererBase
microsoft.maui.controls.platform.compatibility.ShellItemRendererBase;crc640ec207abc449b2ca.ShellItemRendererBase
Microsoft.Maui.Controls.Platform.Compatibility.ShellPageContainer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellPageContainer
Microsoft.Maui.Controls.Platform.Compatibility.ShellPageContainer;crc640ec207abc449b2ca.ShellPageContainer
microsoft.maui.controls.platform.compatibility.ShellPageContainer;crc640ec207abc449b2ca.ShellPageContainer
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchView, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSearchView
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchView;crc640ec207abc449b2ca.ShellSearchView
microsoft.maui.controls.platform.compatibility.ShellSearchView;crc640ec207abc449b2ca.ShellSearchView
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchView+ClipDrawableWrapper, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSearchView_ClipDrawableWrapper
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchView.ClipDrawableWrapper;crc640ec207abc449b2ca.ShellSearchView_ClipDrawableWrapper
microsoft.maui.controls.platform.compatibility.ShellSearchView_ClipDrawableWrapper;crc640ec207abc449b2ca.ShellSearchView_ClipDrawableWrapper
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSearchViewAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter;crc640ec207abc449b2ca.ShellSearchViewAdapter
microsoft.maui.controls.platform.compatibility.ShellSearchViewAdapter;crc640ec207abc449b2ca.ShellSearchViewAdapter
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter+CustomFilter, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSearchViewAdapter_CustomFilter
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter.CustomFilter;crc640ec207abc449b2ca.ShellSearchViewAdapter_CustomFilter
microsoft.maui.controls.platform.compatibility.ShellSearchViewAdapter_CustomFilter;crc640ec207abc449b2ca.ShellSearchViewAdapter_CustomFilter
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter+ObjectWrapper, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSearchViewAdapter_ObjectWrapper
Microsoft.Maui.Controls.Platform.Compatibility.ShellSearchViewAdapter.ObjectWrapper;crc640ec207abc449b2ca.ShellSearchViewAdapter_ObjectWrapper
microsoft.maui.controls.platform.compatibility.ShellSearchViewAdapter_ObjectWrapper;crc640ec207abc449b2ca.ShellSearchViewAdapter_ObjectWrapper
Microsoft.Maui.Controls.Platform.Compatibility.ShellSectionRenderer, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSectionRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellSectionRenderer;crc640ec207abc449b2ca.ShellSectionRenderer
microsoft.maui.controls.platform.compatibility.ShellSectionRenderer;crc640ec207abc449b2ca.ShellSectionRenderer
Microsoft.Maui.Controls.Platform.Compatibility.ShellSectionRenderer+ViewPagerPageChanged, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellSectionRenderer_ViewPagerPageChanged
Microsoft.Maui.Controls.Platform.Compatibility.ShellSectionRenderer.ViewPagerPageChanged;crc640ec207abc449b2ca.ShellSectionRenderer_ViewPagerPageChanged
microsoft.maui.controls.platform.compatibility.ShellSectionRenderer_ViewPagerPageChanged;crc640ec207abc449b2ca.ShellSectionRenderer_ViewPagerPageChanged
Microsoft.Maui.Controls.Platform.Compatibility.ShellToolbarTracker, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellToolbarTracker
Microsoft.Maui.Controls.Platform.Compatibility.ShellToolbarTracker;crc640ec207abc449b2ca.ShellToolbarTracker
microsoft.maui.controls.platform.compatibility.ShellToolbarTracker;crc640ec207abc449b2ca.ShellToolbarTracker
Microsoft.Maui.Controls.Platform.Compatibility.ShellToolbarTracker+FlyoutIconDrawerDrawable, Microsoft.Maui.Controls;crc640ec207abc449b2ca.ShellToolbarTracker_FlyoutIconDrawerDrawable
Microsoft.Maui.Controls.Platform.Compatibility.ShellToolbarTracker.FlyoutIconDrawerDrawable;crc640ec207abc449b2ca.ShellToolbarTracker_FlyoutIconDrawerDrawable
microsoft.maui.controls.platform.compatibility.ShellToolbarTracker_FlyoutIconDrawerDrawable;crc640ec207abc449b2ca.ShellToolbarTracker_FlyoutIconDrawerDrawable
Microsoft.Maui.Controls.Handlers.TabbedPageManager+TempView, Microsoft.Maui.Controls;crc649ff77a65592e7d55.TabbedPageManager_TempView
Microsoft.Maui.Controls.Handlers.TabbedPageManager.TempView;crc649ff77a65592e7d55.TabbedPageManager_TempView
microsoft.maui.controls.handlers.TabbedPageManager_TempView;crc649ff77a65592e7d55.TabbedPageManager_TempView
Microsoft.Maui.Controls.Handlers.TabbedPageManager+Listeners, Microsoft.Maui.Controls;crc649ff77a65592e7d55.TabbedPageManager_Listeners
Microsoft.Maui.Controls.Handlers.TabbedPageManager.Listeners;crc649ff77a65592e7d55.TabbedPageManager_Listeners
microsoft.maui.controls.handlers.TabbedPageManager_Listeners;crc649ff77a65592e7d55.TabbedPageManager_Listeners
Microsoft.Maui.Controls.Handlers.Items.CarouselViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.CarouselViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.CarouselViewAdapter`2;crc645d80431ce5f73f11.CarouselViewAdapter_2
microsoft.maui.controls.handlers.items.CarouselViewAdapter_2;crc645d80431ce5f73f11.CarouselViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.EmptyViewAdapter, Microsoft.Maui.Controls;crc645d80431ce5f73f11.EmptyViewAdapter
Microsoft.Maui.Controls.Handlers.Items.EmptyViewAdapter;crc645d80431ce5f73f11.EmptyViewAdapter
microsoft.maui.controls.handlers.items.EmptyViewAdapter;crc645d80431ce5f73f11.EmptyViewAdapter
Microsoft.Maui.Controls.Handlers.Items.GroupableItemsViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.GroupableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.GroupableItemsViewAdapter`2;crc645d80431ce5f73f11.GroupableItemsViewAdapter_2
microsoft.maui.controls.handlers.items.GroupableItemsViewAdapter_2;crc645d80431ce5f73f11.GroupableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.ItemsViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.ItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.ItemsViewAdapter`2;crc645d80431ce5f73f11.ItemsViewAdapter_2
microsoft.maui.controls.handlers.items.ItemsViewAdapter_2;crc645d80431ce5f73f11.ItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.ReorderableItemsViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.ReorderableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.ReorderableItemsViewAdapter`2;crc645d80431ce5f73f11.ReorderableItemsViewAdapter_2
microsoft.maui.controls.handlers.items.ReorderableItemsViewAdapter_2;crc645d80431ce5f73f11.ReorderableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.SelectableItemsViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SelectableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.SelectableItemsViewAdapter`2;crc645d80431ce5f73f11.SelectableItemsViewAdapter_2
microsoft.maui.controls.handlers.items.SelectableItemsViewAdapter_2;crc645d80431ce5f73f11.SelectableItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.StructuredItemsViewAdapter`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.StructuredItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.StructuredItemsViewAdapter`2;crc645d80431ce5f73f11.StructuredItemsViewAdapter_2
microsoft.maui.controls.handlers.items.StructuredItemsViewAdapter_2;crc645d80431ce5f73f11.StructuredItemsViewAdapter_2
Microsoft.Maui.Controls.Handlers.Items.CarouselSpacingItemDecoration, Microsoft.Maui.Controls;crc645d80431ce5f73f11.CarouselSpacingItemDecoration
Microsoft.Maui.Controls.Handlers.Items.CarouselSpacingItemDecoration;crc645d80431ce5f73f11.CarouselSpacingItemDecoration
microsoft.maui.controls.handlers.items.CarouselSpacingItemDecoration;crc645d80431ce5f73f11.CarouselSpacingItemDecoration
Microsoft.Maui.Controls.Handlers.Items.CarouselViewOnScrollListener, Microsoft.Maui.Controls;crc645d80431ce5f73f11.CarouselViewOnScrollListener
Microsoft.Maui.Controls.Handlers.Items.CarouselViewOnScrollListener;crc645d80431ce5f73f11.CarouselViewOnScrollListener
microsoft.maui.controls.handlers.items.CarouselViewOnScrollListener;crc645d80431ce5f73f11.CarouselViewOnScrollListener
Microsoft.Maui.Controls.Handlers.Items.DataChangeObserver, Microsoft.Maui.Controls;crc645d80431ce5f73f11.DataChangeObserver
Microsoft.Maui.Controls.Handlers.Items.DataChangeObserver;crc645d80431ce5f73f11.DataChangeObserver
microsoft.maui.controls.handlers.items.DataChangeObserver;crc645d80431ce5f73f11.DataChangeObserver
Microsoft.Maui.Controls.Handlers.Items.GridLayoutSpanSizeLookup, Microsoft.Maui.Controls;crc645d80431ce5f73f11.GridLayoutSpanSizeLookup
Microsoft.Maui.Controls.Handlers.Items.GridLayoutSpanSizeLookup;crc645d80431ce5f73f11.GridLayoutSpanSizeLookup
microsoft.maui.controls.handlers.items.GridLayoutSpanSizeLookup;crc645d80431ce5f73f11.GridLayoutSpanSizeLookup
Microsoft.Maui.Controls.Handlers.Items.ItemContentView, Microsoft.Maui.Controls;crc645d80431ce5f73f11.ItemContentView
Microsoft.Maui.Controls.Handlers.Items.ItemContentView;crc645d80431ce5f73f11.ItemContentView
microsoft.maui.controls.handlers.items.ItemContentView;crc645d80431ce5f73f11.ItemContentView
Microsoft.Maui.Controls.Handlers.Items.MauiCarouselRecyclerView, Microsoft.Maui.Controls;crc645d80431ce5f73f11.MauiCarouselRecyclerView
Microsoft.Maui.Controls.Handlers.Items.MauiCarouselRecyclerView;crc645d80431ce5f73f11.MauiCarouselRecyclerView
microsoft.maui.controls.handlers.items.MauiCarouselRecyclerView;crc645d80431ce5f73f11.MauiCarouselRecyclerView
Microsoft.Maui.Controls.Handlers.Items.MauiCarouselRecyclerView+CarouselViewOnGlobalLayoutListener, Microsoft.Maui.Controls;crc645d80431ce5f73f11.MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener
Microsoft.Maui.Controls.Handlers.Items.MauiCarouselRecyclerView.CarouselViewOnGlobalLayoutListener;crc645d80431ce5f73f11.MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener
microsoft.maui.controls.handlers.items.MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener;crc645d80431ce5f73f11.MauiCarouselRecyclerView_CarouselViewOnGlobalLayoutListener
Microsoft.Maui.Controls.Handlers.Items.MauiRecyclerView`3, Microsoft.Maui.Controls;crc645d80431ce5f73f11.MauiRecyclerView_3
Microsoft.Maui.Controls.Handlers.Items.MauiRecyclerView`3;crc645d80431ce5f73f11.MauiRecyclerView_3
microsoft.maui.controls.handlers.items.MauiRecyclerView_3;crc645d80431ce5f73f11.MauiRecyclerView_3
Microsoft.Maui.Controls.Handlers.Items.PositionalSmoothScroller, Microsoft.Maui.Controls;crc645d80431ce5f73f11.PositionalSmoothScroller
Microsoft.Maui.Controls.Handlers.Items.PositionalSmoothScroller;crc645d80431ce5f73f11.PositionalSmoothScroller
microsoft.maui.controls.handlers.items.PositionalSmoothScroller;crc645d80431ce5f73f11.PositionalSmoothScroller
Microsoft.Maui.Controls.Handlers.Items.RecyclerViewScrollListener`2, Microsoft.Maui.Controls;crc645d80431ce5f73f11.RecyclerViewScrollListener_2
Microsoft.Maui.Controls.Handlers.Items.RecyclerViewScrollListener`2;crc645d80431ce5f73f11.RecyclerViewScrollListener_2
microsoft.maui.controls.handlers.items.RecyclerViewScrollListener_2;crc645d80431ce5f73f11.RecyclerViewScrollListener_2
Microsoft.Maui.Controls.Handlers.Items.ScrollHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.ScrollHelper
Microsoft.Maui.Controls.Handlers.Items.ScrollHelper;crc645d80431ce5f73f11.ScrollHelper
microsoft.maui.controls.handlers.items.ScrollHelper;crc645d80431ce5f73f11.ScrollHelper
Microsoft.Maui.Controls.Handlers.Items.SelectableViewHolder, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SelectableViewHolder
Microsoft.Maui.Controls.Handlers.Items.SelectableViewHolder;crc645d80431ce5f73f11.SelectableViewHolder
microsoft.maui.controls.handlers.items.SelectableViewHolder;crc645d80431ce5f73f11.SelectableViewHolder
Microsoft.Maui.Controls.Handlers.Items.SimpleItemTouchHelperCallback, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SimpleItemTouchHelperCallback
Microsoft.Maui.Controls.Handlers.Items.SimpleItemTouchHelperCallback;crc645d80431ce5f73f11.SimpleItemTouchHelperCallback
microsoft.maui.controls.handlers.items.SimpleItemTouchHelperCallback;crc645d80431ce5f73f11.SimpleItemTouchHelperCallback
Microsoft.Maui.Controls.Handlers.Items.SimpleViewHolder, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SimpleViewHolder
Microsoft.Maui.Controls.Handlers.Items.SimpleViewHolder;crc645d80431ce5f73f11.SimpleViewHolder
microsoft.maui.controls.handlers.items.SimpleViewHolder;crc645d80431ce5f73f11.SimpleViewHolder
Microsoft.Maui.Controls.Handlers.Items.SizedItemContentView, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SizedItemContentView
Microsoft.Maui.Controls.Handlers.Items.SizedItemContentView;crc645d80431ce5f73f11.SizedItemContentView
microsoft.maui.controls.handlers.items.SizedItemContentView;crc645d80431ce5f73f11.SizedItemContentView
Microsoft.Maui.Controls.Handlers.Items.CenterSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.CenterSnapHelper
Microsoft.Maui.Controls.Handlers.Items.CenterSnapHelper;crc645d80431ce5f73f11.CenterSnapHelper
microsoft.maui.controls.handlers.items.CenterSnapHelper;crc645d80431ce5f73f11.CenterSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EdgeSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.EdgeSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EdgeSnapHelper;crc645d80431ce5f73f11.EdgeSnapHelper
microsoft.maui.controls.handlers.items.EdgeSnapHelper;crc645d80431ce5f73f11.EdgeSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EndSingleSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.EndSingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EndSingleSnapHelper;crc645d80431ce5f73f11.EndSingleSnapHelper
microsoft.maui.controls.handlers.items.EndSingleSnapHelper;crc645d80431ce5f73f11.EndSingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EndSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.EndSnapHelper
Microsoft.Maui.Controls.Handlers.Items.EndSnapHelper;crc645d80431ce5f73f11.EndSnapHelper
microsoft.maui.controls.handlers.items.EndSnapHelper;crc645d80431ce5f73f11.EndSnapHelper
Microsoft.Maui.Controls.Handlers.Items.NongreedySnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.NongreedySnapHelper
Microsoft.Maui.Controls.Handlers.Items.NongreedySnapHelper;crc645d80431ce5f73f11.NongreedySnapHelper
microsoft.maui.controls.handlers.items.NongreedySnapHelper;crc645d80431ce5f73f11.NongreedySnapHelper
Microsoft.Maui.Controls.Handlers.Items.NongreedySnapHelper+InitialScrollListener, Microsoft.Maui.Controls;crc645d80431ce5f73f11.NongreedySnapHelper_InitialScrollListener
Microsoft.Maui.Controls.Handlers.Items.NongreedySnapHelper.InitialScrollListener;crc645d80431ce5f73f11.NongreedySnapHelper_InitialScrollListener
microsoft.maui.controls.handlers.items.NongreedySnapHelper_InitialScrollListener;crc645d80431ce5f73f11.NongreedySnapHelper_InitialScrollListener
Microsoft.Maui.Controls.Handlers.Items.SingleSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.SingleSnapHelper;crc645d80431ce5f73f11.SingleSnapHelper
microsoft.maui.controls.handlers.items.SingleSnapHelper;crc645d80431ce5f73f11.SingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.StartSingleSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.StartSingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.StartSingleSnapHelper;crc645d80431ce5f73f11.StartSingleSnapHelper
microsoft.maui.controls.handlers.items.StartSingleSnapHelper;crc645d80431ce5f73f11.StartSingleSnapHelper
Microsoft.Maui.Controls.Handlers.Items.StartSnapHelper, Microsoft.Maui.Controls;crc645d80431ce5f73f11.StartSnapHelper
Microsoft.Maui.Controls.Handlers.Items.StartSnapHelper;crc645d80431ce5f73f11.StartSnapHelper
microsoft.maui.controls.handlers.items.StartSnapHelper;crc645d80431ce5f73f11.StartSnapHelper
Microsoft.Maui.Controls.Handlers.Items.SpacingItemDecoration, Microsoft.Maui.Controls;crc645d80431ce5f73f11.SpacingItemDecoration
Microsoft.Maui.Controls.Handlers.Items.SpacingItemDecoration;crc645d80431ce5f73f11.SpacingItemDecoration
microsoft.maui.controls.handlers.items.SpacingItemDecoration;crc645d80431ce5f73f11.SpacingItemDecoration
Microsoft.Maui.Controls.Handlers.Items.TemplatedItemViewHolder, Microsoft.Maui.Controls;crc645d80431ce5f73f11.TemplatedItemViewHolder
Microsoft.Maui.Controls.Handlers.Items.TemplatedItemViewHolder;crc645d80431ce5f73f11.TemplatedItemViewHolder
microsoft.maui.controls.handlers.items.TemplatedItemViewHolder;crc645d80431ce5f73f11.TemplatedItemViewHolder
Microsoft.Maui.Controls.Handlers.Items.TextViewHolder, Microsoft.Maui.Controls;crc645d80431ce5f73f11.TextViewHolder
Microsoft.Maui.Controls.Handlers.Items.TextViewHolder;crc645d80431ce5f73f11.TextViewHolder
microsoft.maui.controls.handlers.items.TextViewHolder;crc645d80431ce5f73f11.TextViewHolder
Microsoft.Maui.Controls.Handlers.Compatibility.FrameRenderer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.FrameRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.FrameRenderer;crc64e1fb321c08285b90.FrameRenderer
microsoft.maui.controls.handlers.compatibility.FrameRenderer;crc64e1fb321c08285b90.FrameRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ViewRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer;crc64e1fb321c08285b90.ViewRenderer
microsoft.maui.controls.handlers.compatibility.ViewRenderer;crc64e1fb321c08285b90.ViewRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer`2, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ViewRenderer_2
Microsoft.Maui.Controls.Handlers.Compatibility.ViewRenderer`2;crc64e1fb321c08285b90.ViewRenderer_2
microsoft.maui.controls.handlers.compatibility.ViewRenderer_2;crc64e1fb321c08285b90.ViewRenderer_2
Microsoft.Maui.Controls.Handlers.Compatibility.VisualElementRenderer`1, Microsoft.Maui.Controls;crc64e1fb321c08285b90.VisualElementRenderer_1
Microsoft.Maui.Controls.Handlers.Compatibility.VisualElementRenderer`1;crc64e1fb321c08285b90.VisualElementRenderer_1
microsoft.maui.controls.handlers.compatibility.VisualElementRenderer_1;crc64e1fb321c08285b90.VisualElementRenderer_1
Microsoft.Maui.Controls.Handlers.Compatibility.BaseCellView, Microsoft.Maui.Controls;crc64e1fb321c08285b90.BaseCellView
Microsoft.Maui.Controls.Handlers.Compatibility.BaseCellView;crc64e1fb321c08285b90.BaseCellView
microsoft.maui.controls.handlers.compatibility.BaseCellView;crc64e1fb321c08285b90.BaseCellView
Microsoft.Maui.Controls.Handlers.Compatibility.CellAdapter, Microsoft.Maui.Controls;crc64e1fb321c08285b90.CellAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.CellAdapter;crc64e1fb321c08285b90.CellAdapter
microsoft.maui.controls.handlers.compatibility.CellAdapter;crc64e1fb321c08285b90.CellAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.CellRenderer+RendererHolder, Microsoft.Maui.Controls;crc64e1fb321c08285b90.CellRenderer_RendererHolder
Microsoft.Maui.Controls.Handlers.Compatibility.CellRenderer.RendererHolder;crc64e1fb321c08285b90.CellRenderer_RendererHolder
microsoft.maui.controls.handlers.compatibility.CellRenderer_RendererHolder;crc64e1fb321c08285b90.CellRenderer_RendererHolder
Microsoft.Maui.Controls.Handlers.Compatibility.ConditionalFocusLayout, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ConditionalFocusLayout
Microsoft.Maui.Controls.Handlers.Compatibility.ConditionalFocusLayout;crc64e1fb321c08285b90.ConditionalFocusLayout
microsoft.maui.controls.handlers.compatibility.ConditionalFocusLayout;crc64e1fb321c08285b90.ConditionalFocusLayout
Microsoft.Maui.Controls.Handlers.Compatibility.EntryCellEditText, Microsoft.Maui.Controls;crc64e1fb321c08285b90.EntryCellEditText
Microsoft.Maui.Controls.Handlers.Compatibility.EntryCellEditText;crc64e1fb321c08285b90.EntryCellEditText
microsoft.maui.controls.handlers.compatibility.EntryCellEditText;crc64e1fb321c08285b90.EntryCellEditText
Microsoft.Maui.Controls.Handlers.Compatibility.EntryCellView, Microsoft.Maui.Controls;crc64e1fb321c08285b90.EntryCellView
Microsoft.Maui.Controls.Handlers.Compatibility.EntryCellView;crc64e1fb321c08285b90.EntryCellView
microsoft.maui.controls.handlers.compatibility.EntryCellView;crc64e1fb321c08285b90.EntryCellView
Microsoft.Maui.Controls.Handlers.Compatibility.GroupedListViewAdapter, Microsoft.Maui.Controls;crc64e1fb321c08285b90.GroupedListViewAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.GroupedListViewAdapter;crc64e1fb321c08285b90.GroupedListViewAdapter
microsoft.maui.controls.handlers.compatibility.GroupedListViewAdapter;crc64e1fb321c08285b90.GroupedListViewAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewAdapter, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewAdapter;crc64e1fb321c08285b90.ListViewAdapter
microsoft.maui.controls.handlers.compatibility.ListViewAdapter;crc64e1fb321c08285b90.ListViewAdapter
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer;crc64e1fb321c08285b90.ListViewRenderer
microsoft.maui.controls.handlers.compatibility.ListViewRenderer;crc64e1fb321c08285b90.ListViewRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer+Container, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewRenderer_Container
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer.Container;crc64e1fb321c08285b90.ListViewRenderer_Container
microsoft.maui.controls.handlers.compatibility.ListViewRenderer_Container;crc64e1fb321c08285b90.ListViewRenderer_Container
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer+SwipeRefreshLayoutWithFixedNestedScrolling, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer.SwipeRefreshLayoutWithFixedNestedScrolling;crc64e1fb321c08285b90.ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling
microsoft.maui.controls.handlers.compatibility.ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling;crc64e1fb321c08285b90.ListViewRenderer_SwipeRefreshLayoutWithFixedNestedScrolling
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer+ListViewSwipeRefreshLayoutListener, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewRenderer_ListViewSwipeRefreshLayoutListener
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer.ListViewSwipeRefreshLayoutListener;crc64e1fb321c08285b90.ListViewRenderer_ListViewSwipeRefreshLayoutListener
microsoft.maui.controls.handlers.compatibility.ListViewRenderer_ListViewSwipeRefreshLayoutListener;crc64e1fb321c08285b90.ListViewRenderer_ListViewSwipeRefreshLayoutListener
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer+ListViewScrollDetector, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ListViewRenderer_ListViewScrollDetector
Microsoft.Maui.Controls.Handlers.Compatibility.ListViewRenderer.ListViewScrollDetector;crc64e1fb321c08285b90.ListViewRenderer_ListViewScrollDetector
microsoft.maui.controls.handlers.compatibility.ListViewRenderer_ListViewScrollDetector;crc64e1fb321c08285b90.ListViewRenderer_ListViewScrollDetector
Microsoft.Maui.Controls.Handlers.Compatibility.SwitchCellView, Microsoft.Maui.Controls;crc64e1fb321c08285b90.SwitchCellView
Microsoft.Maui.Controls.Handlers.Compatibility.SwitchCellView;crc64e1fb321c08285b90.SwitchCellView
microsoft.maui.controls.handlers.compatibility.SwitchCellView;crc64e1fb321c08285b90.SwitchCellView
Microsoft.Maui.Controls.Handlers.Compatibility.TextCellRenderer+TextCellView, Microsoft.Maui.Controls;crc64e1fb321c08285b90.TextCellRenderer_TextCellView
Microsoft.Maui.Controls.Handlers.Compatibility.TextCellRenderer.TextCellView;crc64e1fb321c08285b90.TextCellRenderer_TextCellView
microsoft.maui.controls.handlers.compatibility.TextCellRenderer_TextCellView;crc64e1fb321c08285b90.TextCellRenderer_TextCellView
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer+ViewCellContainer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer.ViewCellContainer;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer
microsoft.maui.controls.handlers.compatibility.ViewCellRenderer_ViewCellContainer;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer+ViewCellContainer+TapGestureListener, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_TapGestureListener
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer.ViewCellContainer.TapGestureListener;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_TapGestureListener
microsoft.maui.controls.handlers.compatibility.ViewCellRenderer_ViewCellContainer_TapGestureListener;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_TapGestureListener
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer+ViewCellContainer+LongPressGestureListener, Microsoft.Maui.Controls;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_LongPressGestureListener
Microsoft.Maui.Controls.Handlers.Compatibility.ViewCellRenderer.ViewCellContainer.LongPressGestureListener;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_LongPressGestureListener
microsoft.maui.controls.handlers.compatibility.ViewCellRenderer_ViewCellContainer_LongPressGestureListener;crc64e1fb321c08285b90.ViewCellRenderer_ViewCellContainer_LongPressGestureListener
Microsoft.Maui.Controls.Handlers.Compatibility.TableViewModelRenderer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.TableViewModelRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.TableViewModelRenderer;crc64e1fb321c08285b90.TableViewModelRenderer
microsoft.maui.controls.handlers.compatibility.TableViewModelRenderer;crc64e1fb321c08285b90.TableViewModelRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.TableViewRenderer, Microsoft.Maui.Controls;crc64e1fb321c08285b90.TableViewRenderer
Microsoft.Maui.Controls.Handlers.Compatibility.TableViewRenderer;crc64e1fb321c08285b90.TableViewRenderer
microsoft.maui.controls.handlers.compatibility.TableViewRenderer;crc64e1fb321c08285b90.TableViewRenderer
Microsoft.Maui.ImageLoaderResultCallback, Microsoft.Maui;crc6488302ad6e9e4df1a.ImageLoaderResultCallback
Microsoft.Maui.ImageLoaderResultCallback;crc6488302ad6e9e4df1a.ImageLoaderResultCallback
microsoft.maui.ImageLoaderResultCallback;crc6488302ad6e9e4df1a.ImageLoaderResultCallback
Microsoft.Maui.ImageLoaderCallback, Microsoft.Maui;crc6488302ad6e9e4df1a.ImageLoaderCallback
Microsoft.Maui.ImageLoaderCallback;crc6488302ad6e9e4df1a.ImageLoaderCallback
microsoft.maui.ImageLoaderCallback;crc6488302ad6e9e4df1a.ImageLoaderCallback
Microsoft.Maui.ImageLoaderCallbackBase`1, Microsoft.Maui;crc6488302ad6e9e4df1a.ImageLoaderCallbackBase_1
Microsoft.Maui.ImageLoaderCallbackBase`1;crc6488302ad6e9e4df1a.ImageLoaderCallbackBase_1
microsoft.maui.ImageLoaderCallbackBase_1;crc6488302ad6e9e4df1a.ImageLoaderCallbackBase_1
Microsoft.Maui.MauiAppCompatActivity, Microsoft.Maui;crc6488302ad6e9e4df1a.MauiAppCompatActivity
Microsoft.Maui.MauiAppCompatActivity;crc6488302ad6e9e4df1a.MauiAppCompatActivity
microsoft.maui.MauiAppCompatActivity;crc6488302ad6e9e4df1a.MauiAppCompatActivity
Microsoft.Maui.MauiApplication, Microsoft.Maui;crc6488302ad6e9e4df1a.MauiApplication
Microsoft.Maui.MauiApplication;crc6488302ad6e9e4df1a.MauiApplication
microsoft.maui.MauiApplication;crc6488302ad6e9e4df1a.MauiApplication
Microsoft.Maui.MauiApplication+ActivityLifecycleCallbacks, Microsoft.Maui;crc6488302ad6e9e4df1a.MauiApplication_ActivityLifecycleCallbacks
Microsoft.Maui.MauiApplication.ActivityLifecycleCallbacks;crc6488302ad6e9e4df1a.MauiApplication_ActivityLifecycleCallbacks
microsoft.maui.MauiApplication_ActivityLifecycleCallbacks;crc6488302ad6e9e4df1a.MauiApplication_ActivityLifecycleCallbacks
Microsoft.Maui.IImageLoaderCallback, Microsoft.Maui;com.microsoft.maui.ImageLoaderCallback
Microsoft.Maui.IImageLoaderCallback;com.microsoft.maui.ImageLoaderCallback
com.microsoft.maui.ImageLoaderCallback;com.microsoft.maui.ImageLoaderCallback
Microsoft.Maui.Platform.AccessibilityDelegateCompatWrapper, Microsoft.Maui;crc6452ffdc5b34af3a0f.AccessibilityDelegateCompatWrapper
Microsoft.Maui.Platform.AccessibilityDelegateCompatWrapper;crc6452ffdc5b34af3a0f.AccessibilityDelegateCompatWrapper
microsoft.maui.platform.AccessibilityDelegateCompatWrapper;crc6452ffdc5b34af3a0f.AccessibilityDelegateCompatWrapper
Microsoft.Maui.Platform.BorderDrawable, Microsoft.Maui;crc6452ffdc5b34af3a0f.BorderDrawable
Microsoft.Maui.Platform.BorderDrawable;crc6452ffdc5b34af3a0f.BorderDrawable
microsoft.maui.platform.BorderDrawable;crc6452ffdc5b34af3a0f.BorderDrawable
Microsoft.Maui.Platform.ContainerView, Microsoft.Maui;crc6452ffdc5b34af3a0f.ContainerView
Microsoft.Maui.Platform.ContainerView;crc6452ffdc5b34af3a0f.ContainerView
microsoft.maui.platform.ContainerView;crc6452ffdc5b34af3a0f.ContainerView
Microsoft.Maui.Platform.ContentViewGroup, Microsoft.Maui;crc6452ffdc5b34af3a0f.ContentViewGroup
Microsoft.Maui.Platform.ContentViewGroup;crc6452ffdc5b34af3a0f.ContentViewGroup
microsoft.maui.platform.ContentViewGroup;crc6452ffdc5b34af3a0f.ContentViewGroup
Microsoft.Maui.Platform.FragmentManagerExtensions+CallBacks, Microsoft.Maui;crc6452ffdc5b34af3a0f.FragmentManagerExtensions_CallBacks
Microsoft.Maui.Platform.FragmentManagerExtensions.CallBacks;crc6452ffdc5b34af3a0f.FragmentManagerExtensions_CallBacks
microsoft.maui.platform.FragmentManagerExtensions_CallBacks;crc6452ffdc5b34af3a0f.FragmentManagerExtensions_CallBacks
Microsoft.Maui.Platform.LayoutViewGroup, Microsoft.Maui;crc6452ffdc5b34af3a0f.LayoutViewGroup
Microsoft.Maui.Platform.LayoutViewGroup;crc6452ffdc5b34af3a0f.LayoutViewGroup
microsoft.maui.platform.LayoutViewGroup;crc6452ffdc5b34af3a0f.LayoutViewGroup
Microsoft.Maui.Platform.LocalizedDigitsKeyListener, Microsoft.Maui;crc6452ffdc5b34af3a0f.LocalizedDigitsKeyListener
Microsoft.Maui.Platform.LocalizedDigitsKeyListener;crc6452ffdc5b34af3a0f.LocalizedDigitsKeyListener
microsoft.maui.platform.LocalizedDigitsKeyListener;crc6452ffdc5b34af3a0f.LocalizedDigitsKeyListener
Microsoft.Maui.Platform.MauiAccessibilityDelegateCompat, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiAccessibilityDelegateCompat
Microsoft.Maui.Platform.MauiAccessibilityDelegateCompat;crc6452ffdc5b34af3a0f.MauiAccessibilityDelegateCompat
microsoft.maui.platform.MauiAccessibilityDelegateCompat;crc6452ffdc5b34af3a0f.MauiAccessibilityDelegateCompat
Microsoft.Maui.Platform.MauiAppCompatEditText, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiAppCompatEditText
Microsoft.Maui.Platform.MauiAppCompatEditText;crc6452ffdc5b34af3a0f.MauiAppCompatEditText
microsoft.maui.platform.MauiAppCompatEditText;crc6452ffdc5b34af3a0f.MauiAppCompatEditText
Microsoft.Maui.Platform.MauiBoxView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiBoxView
Microsoft.Maui.Platform.MauiBoxView;crc6452ffdc5b34af3a0f.MauiBoxView
microsoft.maui.platform.MauiBoxView;crc6452ffdc5b34af3a0f.MauiBoxView
Microsoft.Maui.Platform.MauiDatePicker, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiDatePicker
Microsoft.Maui.Platform.MauiDatePicker;crc6452ffdc5b34af3a0f.MauiDatePicker
microsoft.maui.platform.MauiDatePicker;crc6452ffdc5b34af3a0f.MauiDatePicker
Microsoft.Maui.Platform.MauiHybridWebView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiHybridWebView
Microsoft.Maui.Platform.MauiHybridWebView;crc6452ffdc5b34af3a0f.MauiHybridWebView
microsoft.maui.platform.MauiHybridWebView;crc6452ffdc5b34af3a0f.MauiHybridWebView
Microsoft.Maui.Platform.MauiHybridWebViewClient, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiHybridWebViewClient
Microsoft.Maui.Platform.MauiHybridWebViewClient;crc6452ffdc5b34af3a0f.MauiHybridWebViewClient
microsoft.maui.platform.MauiHybridWebViewClient;crc6452ffdc5b34af3a0f.MauiHybridWebViewClient
Microsoft.Maui.Platform.MauiLayerDrawable, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiLayerDrawable
Microsoft.Maui.Platform.MauiLayerDrawable;crc6452ffdc5b34af3a0f.MauiLayerDrawable
microsoft.maui.platform.MauiLayerDrawable;crc6452ffdc5b34af3a0f.MauiLayerDrawable
Microsoft.Maui.Platform.MauiMaterialButton, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiMaterialButton
Microsoft.Maui.Platform.MauiMaterialButton;crc6452ffdc5b34af3a0f.MauiMaterialButton
microsoft.maui.platform.MauiMaterialButton;crc6452ffdc5b34af3a0f.MauiMaterialButton
Microsoft.Maui.Platform.MauiMaterialButton+MauiResizableDrawable, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiMaterialButton_MauiResizableDrawable
Microsoft.Maui.Platform.MauiMaterialButton.MauiResizableDrawable;crc6452ffdc5b34af3a0f.MauiMaterialButton_MauiResizableDrawable
microsoft.maui.platform.MauiMaterialButton_MauiResizableDrawable;crc6452ffdc5b34af3a0f.MauiMaterialButton_MauiResizableDrawable
Microsoft.Maui.Platform.MauiPageControl, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiPageControl
Microsoft.Maui.Platform.MauiPageControl;crc6452ffdc5b34af3a0f.MauiPageControl
microsoft.maui.platform.MauiPageControl;crc6452ffdc5b34af3a0f.MauiPageControl
Microsoft.Maui.Platform.MauiPageControl+TEditClickListener, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiPageControl_TEditClickListener
Microsoft.Maui.Platform.MauiPageControl.TEditClickListener;crc6452ffdc5b34af3a0f.MauiPageControl_TEditClickListener
microsoft.maui.platform.MauiPageControl_TEditClickListener;crc6452ffdc5b34af3a0f.MauiPageControl_TEditClickListener
Microsoft.Maui.Platform.MauiPicker, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiPicker
Microsoft.Maui.Platform.MauiPicker;crc6452ffdc5b34af3a0f.MauiPicker
microsoft.maui.platform.MauiPicker;crc6452ffdc5b34af3a0f.MauiPicker
Microsoft.Maui.Platform.MauiPickerBase, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiPickerBase
Microsoft.Maui.Platform.MauiPickerBase;crc6452ffdc5b34af3a0f.MauiPickerBase
microsoft.maui.platform.MauiPickerBase;crc6452ffdc5b34af3a0f.MauiPickerBase
Microsoft.Maui.Platform.MauiScrollView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiScrollView
Microsoft.Maui.Platform.MauiScrollView;crc6452ffdc5b34af3a0f.MauiScrollView
microsoft.maui.platform.MauiScrollView;crc6452ffdc5b34af3a0f.MauiScrollView
Microsoft.Maui.Platform.MauiHorizontalScrollView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiHorizontalScrollView
Microsoft.Maui.Platform.MauiHorizontalScrollView;crc6452ffdc5b34af3a0f.MauiHorizontalScrollView
microsoft.maui.platform.MauiHorizontalScrollView;crc6452ffdc5b34af3a0f.MauiHorizontalScrollView
Microsoft.Maui.Platform.MauiSearchView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiSearchView
Microsoft.Maui.Platform.MauiSearchView;crc6452ffdc5b34af3a0f.MauiSearchView
microsoft.maui.platform.MauiSearchView;crc6452ffdc5b34af3a0f.MauiSearchView
Microsoft.Maui.Platform.MauiShapeView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiShapeView
Microsoft.Maui.Platform.MauiShapeView;crc6452ffdc5b34af3a0f.MauiShapeView
microsoft.maui.platform.MauiShapeView;crc6452ffdc5b34af3a0f.MauiShapeView
Microsoft.Maui.Platform.MauiStepper, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiStepper
Microsoft.Maui.Platform.MauiStepper;crc6452ffdc5b34af3a0f.MauiStepper
microsoft.maui.platform.MauiStepper;crc6452ffdc5b34af3a0f.MauiStepper
Microsoft.Maui.Platform.MauiSwipeRefreshLayout, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiSwipeRefreshLayout
Microsoft.Maui.Platform.MauiSwipeRefreshLayout;crc6452ffdc5b34af3a0f.MauiSwipeRefreshLayout
microsoft.maui.platform.MauiSwipeRefreshLayout;crc6452ffdc5b34af3a0f.MauiSwipeRefreshLayout
Microsoft.Maui.Platform.MauiSwipeView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiSwipeView
Microsoft.Maui.Platform.MauiSwipeView;crc6452ffdc5b34af3a0f.MauiSwipeView
microsoft.maui.platform.MauiSwipeView;crc6452ffdc5b34af3a0f.MauiSwipeView
Microsoft.Maui.Platform.MauiTextView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiTextView
Microsoft.Maui.Platform.MauiTextView;crc6452ffdc5b34af3a0f.MauiTextView
microsoft.maui.platform.MauiTextView;crc6452ffdc5b34af3a0f.MauiTextView
Microsoft.Maui.Platform.MauiTimePicker, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiTimePicker
Microsoft.Maui.Platform.MauiTimePicker;crc6452ffdc5b34af3a0f.MauiTimePicker
microsoft.maui.platform.MauiTimePicker;crc6452ffdc5b34af3a0f.MauiTimePicker
Microsoft.Maui.Platform.MauiWebChromeClient, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiWebChromeClient
Microsoft.Maui.Platform.MauiWebChromeClient;crc6452ffdc5b34af3a0f.MauiWebChromeClient
microsoft.maui.platform.MauiWebChromeClient;crc6452ffdc5b34af3a0f.MauiWebChromeClient
Microsoft.Maui.Platform.MauiWebView, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiWebView
Microsoft.Maui.Platform.MauiWebView;crc6452ffdc5b34af3a0f.MauiWebView
microsoft.maui.platform.MauiWebView;crc6452ffdc5b34af3a0f.MauiWebView
Microsoft.Maui.Platform.MauiWebViewClient, Microsoft.Maui;crc6452ffdc5b34af3a0f.MauiWebViewClient
Microsoft.Maui.Platform.MauiWebViewClient;crc6452ffdc5b34af3a0f.MauiWebViewClient
microsoft.maui.platform.MauiWebViewClient;crc6452ffdc5b34af3a0f.MauiWebViewClient
Microsoft.Maui.Platform.MauiNavHostFragment, Microsoft.Maui;microsoft.maui.platform.MauiNavHostFragment
Microsoft.Maui.Platform.MauiNavHostFragment;microsoft.maui.platform.MauiNavHostFragment
microsoft.maui.platform.MauiNavHostFragment;microsoft.maui.platform.MauiNavHostFragment
Microsoft.Maui.Platform.NavigationRootManager+ElementBasedFragment, Microsoft.Maui;crc6452ffdc5b34af3a0f.NavigationRootManager_ElementBasedFragment
Microsoft.Maui.Platform.NavigationRootManager.ElementBasedFragment;crc6452ffdc5b34af3a0f.NavigationRootManager_ElementBasedFragment
microsoft.maui.platform.NavigationRootManager_ElementBasedFragment;crc6452ffdc5b34af3a0f.NavigationRootManager_ElementBasedFragment
Microsoft.Maui.Platform.NavigationViewFragment, Microsoft.Maui;crc6452ffdc5b34af3a0f.NavigationViewFragment
Microsoft.Maui.Platform.NavigationViewFragment;crc6452ffdc5b34af3a0f.NavigationViewFragment
microsoft.maui.platform.NavigationViewFragment;crc6452ffdc5b34af3a0f.NavigationViewFragment
Microsoft.Maui.Platform.ScopedFragment, Microsoft.Maui;crc6452ffdc5b34af3a0f.ScopedFragment
Microsoft.Maui.Platform.ScopedFragment;crc6452ffdc5b34af3a0f.ScopedFragment
microsoft.maui.platform.ScopedFragment;crc6452ffdc5b34af3a0f.ScopedFragment
Microsoft.Maui.Platform.StackNavigationManager+Callbacks, Microsoft.Maui;crc6452ffdc5b34af3a0f.StackNavigationManager_Callbacks
Microsoft.Maui.Platform.StackNavigationManager.Callbacks;crc6452ffdc5b34af3a0f.StackNavigationManager_Callbacks
microsoft.maui.platform.StackNavigationManager_Callbacks;crc6452ffdc5b34af3a0f.StackNavigationManager_Callbacks
Microsoft.Maui.Platform.ViewFragment, Microsoft.Maui;crc6452ffdc5b34af3a0f.ViewFragment
Microsoft.Maui.Platform.ViewFragment;crc6452ffdc5b34af3a0f.ViewFragment
microsoft.maui.platform.ViewFragment;crc6452ffdc5b34af3a0f.ViewFragment
Microsoft.Maui.Platform.PlatformTouchGraphicsView, Microsoft.Maui;crc6452ffdc5b34af3a0f.PlatformTouchGraphicsView
Microsoft.Maui.Platform.PlatformTouchGraphicsView;crc6452ffdc5b34af3a0f.PlatformTouchGraphicsView
microsoft.maui.platform.PlatformTouchGraphicsView;crc6452ffdc5b34af3a0f.PlatformTouchGraphicsView
Microsoft.Maui.Platform.StepperHandlerHolder, Microsoft.Maui;crc6452ffdc5b34af3a0f.StepperHandlerHolder
Microsoft.Maui.Platform.StepperHandlerHolder;crc6452ffdc5b34af3a0f.StepperHandlerHolder
microsoft.maui.platform.StepperHandlerHolder;crc6452ffdc5b34af3a0f.StepperHandlerHolder
Microsoft.Maui.Platform.StepperHandlerManager+StepperListener, Microsoft.Maui;crc6452ffdc5b34af3a0f.StepperHandlerManager_StepperListener
Microsoft.Maui.Platform.StepperHandlerManager.StepperListener;crc6452ffdc5b34af3a0f.StepperHandlerManager_StepperListener
microsoft.maui.platform.StepperHandlerManager_StepperListener;crc6452ffdc5b34af3a0f.StepperHandlerManager_StepperListener
Microsoft.Maui.Platform.SwipeViewPager, Microsoft.Maui;crc6452ffdc5b34af3a0f.SwipeViewPager
Microsoft.Maui.Platform.SwipeViewPager;crc6452ffdc5b34af3a0f.SwipeViewPager
microsoft.maui.platform.SwipeViewPager;crc6452ffdc5b34af3a0f.SwipeViewPager
Microsoft.Maui.Platform.WebViewExtensions+JavascriptResult, Microsoft.Maui;crc6452ffdc5b34af3a0f.WebViewExtensions_JavascriptResult
Microsoft.Maui.Platform.WebViewExtensions.JavascriptResult;crc6452ffdc5b34af3a0f.WebViewExtensions_JavascriptResult
microsoft.maui.platform.WebViewExtensions_JavascriptResult;crc6452ffdc5b34af3a0f.WebViewExtensions_JavascriptResult
Microsoft.Maui.Platform.WrapperView, Microsoft.Maui;crc6452ffdc5b34af3a0f.WrapperView
Microsoft.Maui.Platform.WrapperView;crc6452ffdc5b34af3a0f.WrapperView
microsoft.maui.platform.WrapperView;crc6452ffdc5b34af3a0f.WrapperView
Microsoft.Maui.Handlers.ButtonHandler+ButtonClickListener, Microsoft.Maui;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonClickListener
Microsoft.Maui.Handlers.ButtonHandler.ButtonClickListener;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonClickListener
microsoft.maui.handlers.ButtonHandler_ButtonClickListener;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonClickListener
Microsoft.Maui.Handlers.ButtonHandler+ButtonTouchListener, Microsoft.Maui;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonTouchListener
Microsoft.Maui.Handlers.ButtonHandler.ButtonTouchListener;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonTouchListener
microsoft.maui.handlers.ButtonHandler_ButtonTouchListener;crc64fcf28c0e24b4cc31.ButtonHandler_ButtonTouchListener
Microsoft.Maui.Handlers.HybridWebViewHandler+HybridWebViewJavaScriptInterface, Microsoft.Maui;crc64fcf28c0e24b4cc31.HybridWebViewHandler_HybridWebViewJavaScriptInterface
Microsoft.Maui.Handlers.HybridWebViewHandler.HybridWebViewJavaScriptInterface;crc64fcf28c0e24b4cc31.HybridWebViewHandler_HybridWebViewJavaScriptInterface
microsoft.maui.handlers.HybridWebViewHandler_HybridWebViewJavaScriptInterface;crc64fcf28c0e24b4cc31.HybridWebViewHandler_HybridWebViewJavaScriptInterface
Microsoft.Maui.Handlers.SearchBarHandler+FocusChangeListener, Microsoft.Maui;crc64fcf28c0e24b4cc31.SearchBarHandler_FocusChangeListener
Microsoft.Maui.Handlers.SearchBarHandler.FocusChangeListener;crc64fcf28c0e24b4cc31.SearchBarHandler_FocusChangeListener
microsoft.maui.handlers.SearchBarHandler_FocusChangeListener;crc64fcf28c0e24b4cc31.SearchBarHandler_FocusChangeListener
Microsoft.Maui.Handlers.SliderHandler+SeekBarChangeListener, Microsoft.Maui;crc64fcf28c0e24b4cc31.SliderHandler_SeekBarChangeListener
Microsoft.Maui.Handlers.SliderHandler.SeekBarChangeListener;crc64fcf28c0e24b4cc31.SliderHandler_SeekBarChangeListener
microsoft.maui.handlers.SliderHandler_SeekBarChangeListener;crc64fcf28c0e24b4cc31.SliderHandler_SeekBarChangeListener
Microsoft.Maui.Handlers.SwitchHandler+CheckedChangeListener, Microsoft.Maui;crc64fcf28c0e24b4cc31.SwitchHandler_CheckedChangeListener
Microsoft.Maui.Handlers.SwitchHandler.CheckedChangeListener;crc64fcf28c0e24b4cc31.SwitchHandler_CheckedChangeListener
microsoft.maui.handlers.SwitchHandler_CheckedChangeListener;crc64fcf28c0e24b4cc31.SwitchHandler_CheckedChangeListener
Microsoft.Maui.Handlers.ToolbarHandler+ProcessBackClick, Microsoft.Maui;crc64fcf28c0e24b4cc31.ToolbarHandler_ProcessBackClick
Microsoft.Maui.Handlers.ToolbarHandler.ProcessBackClick;crc64fcf28c0e24b4cc31.ToolbarHandler_ProcessBackClick
microsoft.maui.handlers.ToolbarHandler_ProcessBackClick;crc64fcf28c0e24b4cc31.ToolbarHandler_ProcessBackClick
Microsoft.Maui.Graphics.LinearGradientShaderFactory, Microsoft.Maui;crc64b5e713d400f589b7.LinearGradientShaderFactory
Microsoft.Maui.Graphics.LinearGradientShaderFactory;crc64b5e713d400f589b7.LinearGradientShaderFactory
microsoft.maui.graphics.LinearGradientShaderFactory;crc64b5e713d400f589b7.LinearGradientShaderFactory
Microsoft.Maui.Graphics.RadialGradientShaderFactory, Microsoft.Maui;crc64b5e713d400f589b7.RadialGradientShaderFactory
Microsoft.Maui.Graphics.RadialGradientShaderFactory;crc64b5e713d400f589b7.RadialGradientShaderFactory
microsoft.maui.graphics.RadialGradientShaderFactory;crc64b5e713d400f589b7.RadialGradientShaderFactory
Microsoft.Maui.Graphics.MauiDrawable, Microsoft.Maui;crc64b5e713d400f589b7.MauiDrawable
Microsoft.Maui.Graphics.MauiDrawable;crc64b5e713d400f589b7.MauiDrawable
microsoft.maui.graphics.MauiDrawable;crc64b5e713d400f589b7.MauiDrawable
Microsoft.Maui.Animations.PlatformTicker+DurationScaleListener, Microsoft.Maui;crc64a096dc44ad241142.PlatformTicker_DurationScaleListener
Microsoft.Maui.Animations.PlatformTicker.DurationScaleListener;crc64a096dc44ad241142.PlatformTicker_DurationScaleListener
microsoft.maui.animations.PlatformTicker_DurationScaleListener;crc64a096dc44ad241142.PlatformTicker_DurationScaleListener
Microsoft.Maui.Authentication.WebAuthenticatorCallbackActivity, Microsoft.Maui.Essentials;crc6468b6408a11370c2f.WebAuthenticatorCallbackActivity
Microsoft.Maui.Authentication.WebAuthenticatorCallbackActivity;crc6468b6408a11370c2f.WebAuthenticatorCallbackActivity
microsoft.maui.authentication.WebAuthenticatorCallbackActivity;crc6468b6408a11370c2f.WebAuthenticatorCallbackActivity
Microsoft.Maui.Authentication.WebAuthenticatorIntermediateActivity, Microsoft.Maui.Essentials;crc6468b6408a11370c2f.WebAuthenticatorIntermediateActivity
Microsoft.Maui.Authentication.WebAuthenticatorIntermediateActivity;crc6468b6408a11370c2f.WebAuthenticatorIntermediateActivity
microsoft.maui.authentication.WebAuthenticatorIntermediateActivity;crc6468b6408a11370c2f.WebAuthenticatorIntermediateActivity
Microsoft.Maui.Media.TextToSpeechInternalImplementation, Microsoft.Maui.Essentials;crc6493855b22b6fa0721.TextToSpeechInternalImplementation
Microsoft.Maui.Media.TextToSpeechInternalImplementation;crc6493855b22b6fa0721.TextToSpeechInternalImplementation
microsoft.maui.media.TextToSpeechInternalImplementation;crc6493855b22b6fa0721.TextToSpeechInternalImplementation
Microsoft.Maui.Storage.FileProvider, Microsoft.Maui.Essentials;microsoft.maui.essentials.fileProvider
Microsoft.Maui.Storage.FileProvider;microsoft.maui.essentials.fileProvider
microsoft.maui.essentials.fileProvider;microsoft.maui.essentials.fileProvider
Microsoft.Maui.Networking.ConnectivityImplementation+EssentialsNetworkCallback, Microsoft.Maui.Essentials;crc64e53d2f592022988e.ConnectivityImplementation_EssentialsNetworkCallback
Microsoft.Maui.Networking.ConnectivityImplementation.EssentialsNetworkCallback;crc64e53d2f592022988e.ConnectivityImplementation_EssentialsNetworkCallback
microsoft.maui.networking.ConnectivityImplementation_EssentialsNetworkCallback;crc64e53d2f592022988e.ConnectivityImplementation_EssentialsNetworkCallback
Microsoft.Maui.Networking.ConnectivityBroadcastReceiver, Microsoft.Maui.Essentials;crc64e53d2f592022988e.ConnectivityBroadcastReceiver
Microsoft.Maui.Networking.ConnectivityBroadcastReceiver;crc64e53d2f592022988e.ConnectivityBroadcastReceiver
microsoft.maui.networking.ConnectivityBroadcastReceiver;crc64e53d2f592022988e.ConnectivityBroadcastReceiver
Microsoft.Maui.ApplicationModel.ActivityLifecycleContextListener, Microsoft.Maui.Essentials;crc64ba438d8f48cf7e75.ActivityLifecycleContextListener
Microsoft.Maui.ApplicationModel.ActivityLifecycleContextListener;crc64ba438d8f48cf7e75.ActivityLifecycleContextListener
microsoft.maui.applicationmodel.ActivityLifecycleContextListener;crc64ba438d8f48cf7e75.ActivityLifecycleContextListener
Microsoft.Maui.ApplicationModel.IntermediateActivity, Microsoft.Maui.Essentials;crc64ba438d8f48cf7e75.IntermediateActivity
Microsoft.Maui.ApplicationModel.IntermediateActivity;crc64ba438d8f48cf7e75.IntermediateActivity
microsoft.maui.applicationmodel.IntermediateActivity;crc64ba438d8f48cf7e75.IntermediateActivity
Microsoft.Maui.ApplicationModel.DataTransfer.ClipboardChangeListener, Microsoft.Maui.Essentials;crc640a1f4d108c17e3f1.ClipboardChangeListener
Microsoft.Maui.ApplicationModel.DataTransfer.ClipboardChangeListener;crc640a1f4d108c17e3f1.ClipboardChangeListener
microsoft.maui.applicationmodel.datatransfer.ClipboardChangeListener;crc640a1f4d108c17e3f1.ClipboardChangeListener
Microsoft.Maui.Devices.DeviceDisplayImplementation+Listener, Microsoft.Maui.Essentials;crc640a8d9a12ddbf2cf2.DeviceDisplayImplementation_Listener
Microsoft.Maui.Devices.DeviceDisplayImplementation.Listener;crc640a8d9a12ddbf2cf2.DeviceDisplayImplementation_Listener
microsoft.maui.devices.DeviceDisplayImplementation_Listener;crc640a8d9a12ddbf2cf2.DeviceDisplayImplementation_Listener
Microsoft.Maui.Devices.BatteryBroadcastReceiver, Microsoft.Maui.Essentials;crc640a8d9a12ddbf2cf2.BatteryBroadcastReceiver
Microsoft.Maui.Devices.BatteryBroadcastReceiver;crc640a8d9a12ddbf2cf2.BatteryBroadcastReceiver
microsoft.maui.devices.BatteryBroadcastReceiver;crc640a8d9a12ddbf2cf2.BatteryBroadcastReceiver
Microsoft.Maui.Devices.EnergySaverBroadcastReceiver, Microsoft.Maui.Essentials;crc640a8d9a12ddbf2cf2.EnergySaverBroadcastReceiver
Microsoft.Maui.Devices.EnergySaverBroadcastReceiver;crc640a8d9a12ddbf2cf2.EnergySaverBroadcastReceiver
microsoft.maui.devices.EnergySaverBroadcastReceiver;crc640a8d9a12ddbf2cf2.EnergySaverBroadcastReceiver
Microsoft.Maui.Devices.Sensors.AccelerometerListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.AccelerometerListener
Microsoft.Maui.Devices.Sensors.AccelerometerListener;crc64f62664462a8937a9.AccelerometerListener
microsoft.maui.devices.sensors.AccelerometerListener;crc64f62664462a8937a9.AccelerometerListener
Microsoft.Maui.Devices.Sensors.BarometerListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.BarometerListener
Microsoft.Maui.Devices.Sensors.BarometerListener;crc64f62664462a8937a9.BarometerListener
microsoft.maui.devices.sensors.BarometerListener;crc64f62664462a8937a9.BarometerListener
Microsoft.Maui.Devices.Sensors.SensorListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.SensorListener
Microsoft.Maui.Devices.Sensors.SensorListener;crc64f62664462a8937a9.SensorListener
microsoft.maui.devices.sensors.SensorListener;crc64f62664462a8937a9.SensorListener
Microsoft.Maui.Devices.Sensors.SingleLocationListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.SingleLocationListener
Microsoft.Maui.Devices.Sensors.SingleLocationListener;crc64f62664462a8937a9.SingleLocationListener
microsoft.maui.devices.sensors.SingleLocationListener;crc64f62664462a8937a9.SingleLocationListener
Microsoft.Maui.Devices.Sensors.ContinuousLocationListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.ContinuousLocationListener
Microsoft.Maui.Devices.Sensors.ContinuousLocationListener;crc64f62664462a8937a9.ContinuousLocationListener
microsoft.maui.devices.sensors.ContinuousLocationListener;crc64f62664462a8937a9.ContinuousLocationListener
Microsoft.Maui.Devices.Sensors.GyroscopeListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.GyroscopeListener
Microsoft.Maui.Devices.Sensors.GyroscopeListener;crc64f62664462a8937a9.GyroscopeListener
microsoft.maui.devices.sensors.GyroscopeListener;crc64f62664462a8937a9.GyroscopeListener
Microsoft.Maui.Devices.Sensors.MagnetometerListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.MagnetometerListener
Microsoft.Maui.Devices.Sensors.MagnetometerListener;crc64f62664462a8937a9.MagnetometerListener
microsoft.maui.devices.sensors.MagnetometerListener;crc64f62664462a8937a9.MagnetometerListener
Microsoft.Maui.Devices.Sensors.OrientationSensorListener, Microsoft.Maui.Essentials;crc64f62664462a8937a9.OrientationSensorListener
Microsoft.Maui.Devices.Sensors.OrientationSensorListener;crc64f62664462a8937a9.OrientationSensorListener
microsoft.maui.devices.sensors.OrientationSensorListener;crc64f62664462a8937a9.OrientationSensorListener
Microsoft.Maui.Graphics.Platform.PlatformGraphicsView, Microsoft.Maui.Graphics;crc643f2b18b2570eaa5a.PlatformGraphicsView
Microsoft.Maui.Graphics.Platform.PlatformGraphicsView;crc643f2b18b2570eaa5a.PlatformGraphicsView
microsoft.maui.graphics.platform.PlatformGraphicsView;crc643f2b18b2570eaa5a.PlatformGraphicsView
Bumptech.Glide.Glide+IRequestOptionsFactory, Xamarin.Android.Glide;com.bumptech.glide.Glide$RequestOptionsFactory
Bumptech.Glide.Glide.IRequestOptionsFactory;com.bumptech.glide.Glide$RequestOptionsFactory
com.bumptech.glide.Glide$RequestOptionsFactory;com.bumptech.glide.Glide$RequestOptionsFactory
Bumptech.Glide.ListPreloader+IPreloadModelProvider, Xamarin.Android.Glide;com.bumptech.glide.ListPreloader$PreloadModelProvider
Bumptech.Glide.ListPreloader.IPreloadModelProvider;com.bumptech.glide.ListPreloader$PreloadModelProvider
com.bumptech.glide.ListPreloader$PreloadModelProvider;com.bumptech.glide.ListPreloader$PreloadModelProvider
Bumptech.Glide.ListPreloader+IPreloadSizeProvider, Xamarin.Android.Glide;com.bumptech.glide.ListPreloader$PreloadSizeProvider
Bumptech.Glide.ListPreloader.IPreloadSizeProvider;com.bumptech.glide.ListPreloader$PreloadSizeProvider
com.bumptech.glide.ListPreloader$PreloadSizeProvider;com.bumptech.glide.ListPreloader$PreloadSizeProvider
Bumptech.Glide.Util.GlideSuppliers+IGlideSupplier, Xamarin.Android.Glide;com.bumptech.glide.util.GlideSuppliers$GlideSupplier
Bumptech.Glide.Util.GlideSuppliers.IGlideSupplier;com.bumptech.glide.util.GlideSuppliers$GlideSupplier
com.bumptech.glide.util.GlideSuppliers$GlideSupplier;com.bumptech.glide.util.GlideSuppliers$GlideSupplier
Bumptech.Glide.Util.ISynthetic, Xamarin.Android.Glide;com.bumptech.glide.util.Synthetic
Bumptech.Glide.Util.ISynthetic;com.bumptech.glide.util.Synthetic
com.bumptech.glide.util.Synthetic;com.bumptech.glide.util.Synthetic
Bumptech.Glide.Util.Pool.FactoryPools+IFactory, Xamarin.Android.Glide;com.bumptech.glide.util.pool.FactoryPools$Factory
Bumptech.Glide.Util.Pool.FactoryPools.IFactory;com.bumptech.glide.util.pool.FactoryPools$Factory
com.bumptech.glide.util.pool.FactoryPools$Factory;com.bumptech.glide.util.pool.FactoryPools$Factory
Bumptech.Glide.Util.Pool.FactoryPools+IPoolable, Xamarin.Android.Glide;com.bumptech.glide.util.pool.FactoryPools$Poolable
Bumptech.Glide.Util.Pool.FactoryPools.IPoolable;com.bumptech.glide.util.pool.FactoryPools$Poolable
com.bumptech.glide.util.pool.FactoryPools$Poolable;com.bumptech.glide.util.pool.FactoryPools$Poolable
Bumptech.Glide.Util.Pool.FactoryPools+IResetter, Xamarin.Android.Glide;com.bumptech.glide.util.pool.FactoryPools$Resetter
Bumptech.Glide.Util.Pool.FactoryPools.IResetter;com.bumptech.glide.util.pool.FactoryPools$Resetter
com.bumptech.glide.util.pool.FactoryPools$Resetter;com.bumptech.glide.util.pool.FactoryPools$Resetter
Bumptech.Glide.Module.IAppliesOptions, Xamarin.Android.Glide;com.bumptech.glide.module.AppliesOptions
Bumptech.Glide.Module.IAppliesOptions;com.bumptech.glide.module.AppliesOptions
com.bumptech.glide.module.AppliesOptions;com.bumptech.glide.module.AppliesOptions
Bumptech.Glide.Module.IGlideModule, Xamarin.Android.Glide;com.bumptech.glide.module.GlideModule
Bumptech.Glide.Module.IGlideModule;com.bumptech.glide.module.GlideModule
com.bumptech.glide.module.GlideModule;com.bumptech.glide.module.GlideModule
Bumptech.Glide.Module.IRegistersComponents, Xamarin.Android.Glide;com.bumptech.glide.module.RegistersComponents
Bumptech.Glide.Module.IRegistersComponents;com.bumptech.glide.module.RegistersComponents
com.bumptech.glide.module.RegistersComponents;com.bumptech.glide.module.RegistersComponents
Bumptech.Glide.Manager.IConnectivityMonitorConnectivityListener, Xamarin.Android.Glide;com.bumptech.glide.manager.ConnectivityMonitor$ConnectivityListener
Bumptech.Glide.Manager.IConnectivityMonitorConnectivityListener;com.bumptech.glide.manager.ConnectivityMonitor$ConnectivityListener
com.bumptech.glide.manager.ConnectivityMonitor$ConnectivityListener;com.bumptech.glide.manager.ConnectivityMonitor$ConnectivityListener
Bumptech.Glide.Manager.IConnectivityMonitorConnectivityListenerImplementor, Xamarin.Android.Glide;mono.com.bumptech.glide.manager.ConnectivityMonitor_ConnectivityListenerImplementor
Bumptech.Glide.Manager.IConnectivityMonitorConnectivityListenerImplementor;mono.com.bumptech.glide.manager.ConnectivityMonitor_ConnectivityListenerImplementor
mono.com.bumptech.glide.manager.ConnectivityMonitor_ConnectivityListenerImplementor;mono.com.bumptech.glide.manager.ConnectivityMonitor_ConnectivityListenerImplementor
Bumptech.Glide.Manager.IConnectivityMonitor, Xamarin.Android.Glide;com.bumptech.glide.manager.ConnectivityMonitor
Bumptech.Glide.Manager.IConnectivityMonitor;com.bumptech.glide.manager.ConnectivityMonitor
com.bumptech.glide.manager.ConnectivityMonitor;com.bumptech.glide.manager.ConnectivityMonitor
Bumptech.Glide.Manager.IConnectivityMonitorFactory, Xamarin.Android.Glide;com.bumptech.glide.manager.ConnectivityMonitorFactory
Bumptech.Glide.Manager.IConnectivityMonitorFactory;com.bumptech.glide.manager.ConnectivityMonitorFactory
com.bumptech.glide.manager.ConnectivityMonitorFactory;com.bumptech.glide.manager.ConnectivityMonitorFactory
Bumptech.Glide.Manager.ILifecycle, Xamarin.Android.Glide;com.bumptech.glide.manager.Lifecycle
Bumptech.Glide.Manager.ILifecycle;com.bumptech.glide.manager.Lifecycle
com.bumptech.glide.manager.Lifecycle;com.bumptech.glide.manager.Lifecycle
Bumptech.Glide.Manager.ILifecycleListener, Xamarin.Android.Glide;com.bumptech.glide.manager.LifecycleListener
Bumptech.Glide.Manager.ILifecycleListener;com.bumptech.glide.manager.LifecycleListener
com.bumptech.glide.manager.LifecycleListener;com.bumptech.glide.manager.LifecycleListener
Bumptech.Glide.Manager.ILifecycleListenerImplementor, Xamarin.Android.Glide;mono.com.bumptech.glide.manager.LifecycleListenerImplementor
Bumptech.Glide.Manager.ILifecycleListenerImplementor;mono.com.bumptech.glide.manager.LifecycleListenerImplementor
mono.com.bumptech.glide.manager.LifecycleListenerImplementor;mono.com.bumptech.glide.manager.LifecycleListenerImplementor
Bumptech.Glide.Manager.IRequestManagerTreeNode, Xamarin.Android.Glide;com.bumptech.glide.manager.RequestManagerTreeNode
Bumptech.Glide.Manager.IRequestManagerTreeNode;com.bumptech.glide.manager.RequestManagerTreeNode
com.bumptech.glide.manager.RequestManagerTreeNode;com.bumptech.glide.manager.RequestManagerTreeNode
Bumptech.Glide.Manager.RequestManagerRetriever+IRequestManagerFactory, Xamarin.Android.Glide;com.bumptech.glide.manager.RequestManagerRetriever$RequestManagerFactory
Bumptech.Glide.Manager.RequestManagerRetriever.IRequestManagerFactory;com.bumptech.glide.manager.RequestManagerRetriever$RequestManagerFactory
com.bumptech.glide.manager.RequestManagerRetriever$RequestManagerFactory;com.bumptech.glide.manager.RequestManagerRetriever$RequestManagerFactory
Bumptech.Glide.Request.IFutureTarget, Xamarin.Android.Glide;com.bumptech.glide.request.FutureTarget
Bumptech.Glide.Request.IFutureTarget;com.bumptech.glide.request.FutureTarget
com.bumptech.glide.request.FutureTarget;com.bumptech.glide.request.FutureTarget
Bumptech.Glide.Request.IRequest, Xamarin.Android.Glide;com.bumptech.glide.request.Request
Bumptech.Glide.Request.IRequest;com.bumptech.glide.request.Request
com.bumptech.glide.request.Request;com.bumptech.glide.request.Request
Bumptech.Glide.Request.IRequestCoordinator, Xamarin.Android.Glide;com.bumptech.glide.request.RequestCoordinator
Bumptech.Glide.Request.IRequestCoordinator;com.bumptech.glide.request.RequestCoordinator
com.bumptech.glide.request.RequestCoordinator;com.bumptech.glide.request.RequestCoordinator
Bumptech.Glide.Request.IRequestListener, Xamarin.Android.Glide;com.bumptech.glide.request.RequestListener
Bumptech.Glide.Request.IRequestListener;com.bumptech.glide.request.RequestListener
com.bumptech.glide.request.RequestListener;com.bumptech.glide.request.RequestListener
Bumptech.Glide.Request.IRequestListenerImplementor, Xamarin.Android.Glide;mono.com.bumptech.glide.request.RequestListenerImplementor
Bumptech.Glide.Request.IRequestListenerImplementor;mono.com.bumptech.glide.request.RequestListenerImplementor
mono.com.bumptech.glide.request.RequestListenerImplementor;mono.com.bumptech.glide.request.RequestListenerImplementor
Bumptech.Glide.Request.IResourceCallback, Xamarin.Android.Glide;com.bumptech.glide.request.ResourceCallback
Bumptech.Glide.Request.IResourceCallback;com.bumptech.glide.request.ResourceCallback
com.bumptech.glide.request.ResourceCallback;com.bumptech.glide.request.ResourceCallback
Bumptech.Glide.Request.Transition.ITransitionViewAdapter, Xamarin.Android.Glide;com.bumptech.glide.request.transition.Transition$ViewAdapter
Bumptech.Glide.Request.Transition.ITransitionViewAdapter;com.bumptech.glide.request.transition.Transition$ViewAdapter
com.bumptech.glide.request.transition.Transition$ViewAdapter;com.bumptech.glide.request.transition.Transition$ViewAdapter
Bumptech.Glide.Request.Transition.ITransition, Xamarin.Android.Glide;com.bumptech.glide.request.transition.Transition
Bumptech.Glide.Request.Transition.ITransition;com.bumptech.glide.request.transition.Transition
com.bumptech.glide.request.transition.Transition;com.bumptech.glide.request.transition.Transition
Bumptech.Glide.Request.Transition.ITransitionFactory, Xamarin.Android.Glide;com.bumptech.glide.request.transition.TransitionFactory
Bumptech.Glide.Request.Transition.ITransitionFactory;com.bumptech.glide.request.transition.TransitionFactory
com.bumptech.glide.request.transition.TransitionFactory;com.bumptech.glide.request.transition.TransitionFactory
Bumptech.Glide.Request.Transition.ViewPropertyTransition+IAnimator, Xamarin.Android.Glide;com.bumptech.glide.request.transition.ViewPropertyTransition$Animator
Bumptech.Glide.Request.Transition.ViewPropertyTransition.IAnimator;com.bumptech.glide.request.transition.ViewPropertyTransition$Animator
com.bumptech.glide.request.transition.ViewPropertyTransition$Animator;com.bumptech.glide.request.transition.ViewPropertyTransition$Animator
Bumptech.Glide.Request.Target.ISizeReadyCallback, Xamarin.Android.Glide;com.bumptech.glide.request.target.SizeReadyCallback
Bumptech.Glide.Request.Target.ISizeReadyCallback;com.bumptech.glide.request.target.SizeReadyCallback
com.bumptech.glide.request.target.SizeReadyCallback;com.bumptech.glide.request.target.SizeReadyCallback
Bumptech.Glide.Request.Target.ITarget, Xamarin.Android.Glide;com.bumptech.glide.request.target.Target
Bumptech.Glide.Request.Target.ITarget;com.bumptech.glide.request.target.Target
com.bumptech.glide.request.target.Target;com.bumptech.glide.request.target.Target
Bumptech.Glide.Load.IEncoder, Xamarin.Android.Glide;com.bumptech.glide.load.Encoder
Bumptech.Glide.Load.IEncoder;com.bumptech.glide.load.Encoder
com.bumptech.glide.load.Encoder;com.bumptech.glide.load.Encoder
Bumptech.Glide.Load.IImageHeaderParser, Xamarin.Android.Glide;com.bumptech.glide.load.ImageHeaderParser
Bumptech.Glide.Load.IImageHeaderParser;com.bumptech.glide.load.ImageHeaderParser
com.bumptech.glide.load.ImageHeaderParser;com.bumptech.glide.load.ImageHeaderParser
Bumptech.Glide.Load.IKey, Xamarin.Android.Glide;com.bumptech.glide.load.Key
Bumptech.Glide.Load.IKey;com.bumptech.glide.load.Key
com.bumptech.glide.load.Key;com.bumptech.glide.load.Key
Bumptech.Glide.Load.IResourceDecoder, Xamarin.Android.Glide;com.bumptech.glide.load.ResourceDecoder
Bumptech.Glide.Load.IResourceDecoder;com.bumptech.glide.load.ResourceDecoder
com.bumptech.glide.load.ResourceDecoder;com.bumptech.glide.load.ResourceDecoder
Bumptech.Glide.Load.IResourceEncoder, Xamarin.Android.Glide;com.bumptech.glide.load.ResourceEncoder
Bumptech.Glide.Load.IResourceEncoder;com.bumptech.glide.load.ResourceEncoder
com.bumptech.glide.load.ResourceEncoder;com.bumptech.glide.load.ResourceEncoder
Bumptech.Glide.Load.ITransformation, Xamarin.Android.Glide;com.bumptech.glide.load.Transformation
Bumptech.Glide.Load.ITransformation;com.bumptech.glide.load.Transformation
com.bumptech.glide.load.Transformation;com.bumptech.glide.load.Transformation
Bumptech.Glide.Load.Option+ICacheKeyUpdater, Xamarin.Android.Glide;com.bumptech.glide.load.Option$CacheKeyUpdater
Bumptech.Glide.Load.Option.ICacheKeyUpdater;com.bumptech.glide.load.Option$CacheKeyUpdater
com.bumptech.glide.load.Option$CacheKeyUpdater;com.bumptech.glide.load.Option$CacheKeyUpdater
Bumptech.Glide.Load.Resource.Transcode.IResourceTranscoder, Xamarin.Android.Glide;com.bumptech.glide.load.resource.transcode.ResourceTranscoder
Bumptech.Glide.Load.Resource.Transcode.IResourceTranscoder;com.bumptech.glide.load.resource.transcode.ResourceTranscoder
com.bumptech.glide.load.resource.transcode.ResourceTranscoder;com.bumptech.glide.load.resource.transcode.ResourceTranscoder
Bumptech.Glide.Load.Resource.Bitmap.Downsampler+IDecodeCallbacks, Xamarin.Android.Glide;com.bumptech.glide.load.resource.bitmap.Downsampler$DecodeCallbacks
Bumptech.Glide.Load.Resource.Bitmap.Downsampler.IDecodeCallbacks;com.bumptech.glide.load.resource.bitmap.Downsampler$DecodeCallbacks
com.bumptech.glide.load.resource.bitmap.Downsampler$DecodeCallbacks;com.bumptech.glide.load.resource.bitmap.Downsampler$DecodeCallbacks
Bumptech.Glide.Load.Model.AssetUriLoader+IAssetFetcherFactory, Xamarin.Android.Glide;com.bumptech.glide.load.model.AssetUriLoader$AssetFetcherFactory
Bumptech.Glide.Load.Model.AssetUriLoader.IAssetFetcherFactory;com.bumptech.glide.load.model.AssetUriLoader$AssetFetcherFactory
com.bumptech.glide.load.model.AssetUriLoader$AssetFetcherFactory;com.bumptech.glide.load.model.AssetUriLoader$AssetFetcherFactory
Bumptech.Glide.Load.Model.ByteArrayLoader+IConverter, Xamarin.Android.Glide;com.bumptech.glide.load.model.ByteArrayLoader$Converter
Bumptech.Glide.Load.Model.ByteArrayLoader.IConverter;com.bumptech.glide.load.model.ByteArrayLoader$Converter
com.bumptech.glide.load.model.ByteArrayLoader$Converter;com.bumptech.glide.load.model.ByteArrayLoader$Converter
Bumptech.Glide.Load.Model.FileLoader+IFileOpener, Xamarin.Android.Glide;com.bumptech.glide.load.model.FileLoader$FileOpener
Bumptech.Glide.Load.Model.FileLoader.IFileOpener;com.bumptech.glide.load.model.FileLoader$FileOpener
com.bumptech.glide.load.model.FileLoader$FileOpener;com.bumptech.glide.load.model.FileLoader$FileOpener
Bumptech.Glide.Load.Model.UriLoader+ILocalUriFetcherFactory, Xamarin.Android.Glide;com.bumptech.glide.load.model.UriLoader$LocalUriFetcherFactory
Bumptech.Glide.Load.Model.UriLoader.ILocalUriFetcherFactory;com.bumptech.glide.load.model.UriLoader$LocalUriFetcherFactory
com.bumptech.glide.load.model.UriLoader$LocalUriFetcherFactory;com.bumptech.glide.load.model.UriLoader$LocalUriFetcherFactory
Bumptech.Glide.Load.Model.DataUrlLoader+IDataDecoder, Xamarin.Android.Glide;com.bumptech.glide.load.model.DataUrlLoader$DataDecoder
Bumptech.Glide.Load.Model.DataUrlLoader.IDataDecoder;com.bumptech.glide.load.model.DataUrlLoader$DataDecoder
com.bumptech.glide.load.model.DataUrlLoader$DataDecoder;com.bumptech.glide.load.model.DataUrlLoader$DataDecoder
Bumptech.Glide.Load.Model.IHeaders, Xamarin.Android.Glide;com.bumptech.glide.load.model.Headers
Bumptech.Glide.Load.Model.IHeaders;com.bumptech.glide.load.model.Headers
com.bumptech.glide.load.model.Headers;com.bumptech.glide.load.model.Headers
Bumptech.Glide.Load.Model.ILazyHeaderFactory, Xamarin.Android.Glide;com.bumptech.glide.load.model.LazyHeaderFactory
Bumptech.Glide.Load.Model.ILazyHeaderFactory;com.bumptech.glide.load.model.LazyHeaderFactory
com.bumptech.glide.load.model.LazyHeaderFactory;com.bumptech.glide.load.model.LazyHeaderFactory
Bumptech.Glide.Load.Model.IModel, Xamarin.Android.Glide;com.bumptech.glide.load.model.Model
Bumptech.Glide.Load.Model.IModel;com.bumptech.glide.load.model.Model
com.bumptech.glide.load.model.Model;com.bumptech.glide.load.model.Model
Bumptech.Glide.Load.Model.IModelLoader, Xamarin.Android.Glide;com.bumptech.glide.load.model.ModelLoader
Bumptech.Glide.Load.Model.IModelLoader;com.bumptech.glide.load.model.ModelLoader
com.bumptech.glide.load.model.ModelLoader;com.bumptech.glide.load.model.ModelLoader
Bumptech.Glide.Load.Model.IModelLoaderFactory, Xamarin.Android.Glide;com.bumptech.glide.load.model.ModelLoaderFactory
Bumptech.Glide.Load.Model.IModelLoaderFactory;com.bumptech.glide.load.model.ModelLoaderFactory
com.bumptech.glide.load.model.ModelLoaderFactory;com.bumptech.glide.load.model.ModelLoaderFactory
Bumptech.Glide.Load.Engine.IInitializable, Xamarin.Android.Glide;com.bumptech.glide.load.engine.Initializable
Bumptech.Glide.Load.Engine.IInitializable;com.bumptech.glide.load.engine.Initializable
com.bumptech.glide.load.engine.Initializable;com.bumptech.glide.load.engine.Initializable
Bumptech.Glide.Load.Engine.IResource, Xamarin.Android.Glide;com.bumptech.glide.load.engine.Resource
Bumptech.Glide.Load.Engine.IResource;com.bumptech.glide.load.engine.Resource
com.bumptech.glide.load.engine.Resource;com.bumptech.glide.load.engine.Resource
Bumptech.Glide.Load.Engine.Cache.DiskLruCacheFactory+ICacheDirectoryGetter, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.DiskLruCacheFactory$CacheDirectoryGetter
Bumptech.Glide.Load.Engine.Cache.DiskLruCacheFactory.ICacheDirectoryGetter;com.bumptech.glide.load.engine.cache.DiskLruCacheFactory$CacheDirectoryGetter
com.bumptech.glide.load.engine.cache.DiskLruCacheFactory$CacheDirectoryGetter;com.bumptech.glide.load.engine.cache.DiskLruCacheFactory$CacheDirectoryGetter
Bumptech.Glide.Load.Engine.Cache.IDiskCacheFactory, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.DiskCache$Factory
Bumptech.Glide.Load.Engine.Cache.IDiskCacheFactory;com.bumptech.glide.load.engine.cache.DiskCache$Factory
com.bumptech.glide.load.engine.cache.DiskCache$Factory;com.bumptech.glide.load.engine.cache.DiskCache$Factory
Bumptech.Glide.Load.Engine.Cache.IDiskCacheWriter, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.DiskCache$Writer
Bumptech.Glide.Load.Engine.Cache.IDiskCacheWriter;com.bumptech.glide.load.engine.cache.DiskCache$Writer
com.bumptech.glide.load.engine.cache.DiskCache$Writer;com.bumptech.glide.load.engine.cache.DiskCache$Writer
Bumptech.Glide.Load.Engine.Cache.IDiskCache, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.DiskCache
Bumptech.Glide.Load.Engine.Cache.IDiskCache;com.bumptech.glide.load.engine.cache.DiskCache
com.bumptech.glide.load.engine.cache.DiskCache;com.bumptech.glide.load.engine.cache.DiskCache
Bumptech.Glide.Load.Engine.Cache.IMemoryCacheResourceRemovedListener, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.MemoryCache$ResourceRemovedListener
Bumptech.Glide.Load.Engine.Cache.IMemoryCacheResourceRemovedListener;com.bumptech.glide.load.engine.cache.MemoryCache$ResourceRemovedListener
com.bumptech.glide.load.engine.cache.MemoryCache$ResourceRemovedListener;com.bumptech.glide.load.engine.cache.MemoryCache$ResourceRemovedListener
Bumptech.Glide.Load.Engine.Cache.IMemoryCacheResourceRemovedListenerImplementor, Xamarin.Android.Glide;mono.com.bumptech.glide.load.engine.cache.MemoryCache_ResourceRemovedListenerImplementor
Bumptech.Glide.Load.Engine.Cache.IMemoryCacheResourceRemovedListenerImplementor;mono.com.bumptech.glide.load.engine.cache.MemoryCache_ResourceRemovedListenerImplementor
mono.com.bumptech.glide.load.engine.cache.MemoryCache_ResourceRemovedListenerImplementor;mono.com.bumptech.glide.load.engine.cache.MemoryCache_ResourceRemovedListenerImplementor
Bumptech.Glide.Load.Engine.Cache.IMemoryCache, Xamarin.Android.Glide;com.bumptech.glide.load.engine.cache.MemoryCache
Bumptech.Glide.Load.Engine.Cache.IMemoryCache;com.bumptech.glide.load.engine.cache.MemoryCache
com.bumptech.glide.load.engine.cache.MemoryCache;com.bumptech.glide.load.engine.cache.MemoryCache
Bumptech.Glide.Load.Engine.BitmapRecycle.IArrayPool, Xamarin.Android.Glide;com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool
Bumptech.Glide.Load.Engine.BitmapRecycle.IArrayPool;com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool
com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool;com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool
Bumptech.Glide.Load.Engine.BitmapRecycle.IBitmapPool, Xamarin.Android.Glide;com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
Bumptech.Glide.Load.Engine.BitmapRecycle.IBitmapPool;com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
Bumptech.Glide.Load.Engine.BitmapRecycle.ILruPoolStrategy, Xamarin.Android.Glide;com.bumptech.glide.load.engine.bitmap_recycle.LruPoolStrategy
Bumptech.Glide.Load.Engine.BitmapRecycle.ILruPoolStrategy;com.bumptech.glide.load.engine.bitmap_recycle.LruPoolStrategy
com.bumptech.glide.load.engine.bitmap_recycle.LruPoolStrategy;com.bumptech.glide.load.engine.bitmap_recycle.LruPoolStrategy
Bumptech.Glide.Load.Engine.BitmapRecycle.IPoolable, Xamarin.Android.Glide;com.bumptech.glide.load.engine.bitmap_recycle.Poolable
Bumptech.Glide.Load.Engine.BitmapRecycle.IPoolable;com.bumptech.glide.load.engine.bitmap_recycle.Poolable
com.bumptech.glide.load.engine.bitmap_recycle.Poolable;com.bumptech.glide.load.engine.bitmap_recycle.Poolable
Bumptech.Glide.Load.Engine.Executor.GlideExecutor+IUncaughtThrowableStrategy, Xamarin.Android.Glide;com.bumptech.glide.load.engine.executor.GlideExecutor$UncaughtThrowableStrategy
Bumptech.Glide.Load.Engine.Executor.GlideExecutor.IUncaughtThrowableStrategy;com.bumptech.glide.load.engine.executor.GlideExecutor$UncaughtThrowableStrategy
com.bumptech.glide.load.engine.executor.GlideExecutor$UncaughtThrowableStrategy;com.bumptech.glide.load.engine.executor.GlideExecutor$UncaughtThrowableStrategy
Bumptech.Glide.Load.Data.IDataFetcherDataCallback, Xamarin.Android.Glide;com.bumptech.glide.load.data.DataFetcher$DataCallback
Bumptech.Glide.Load.Data.IDataFetcherDataCallback;com.bumptech.glide.load.data.DataFetcher$DataCallback
com.bumptech.glide.load.data.DataFetcher$DataCallback;com.bumptech.glide.load.data.DataFetcher$DataCallback
Bumptech.Glide.Load.Data.IDataFetcher, Xamarin.Android.Glide;com.bumptech.glide.load.data.DataFetcher
Bumptech.Glide.Load.Data.IDataFetcher;com.bumptech.glide.load.data.DataFetcher
com.bumptech.glide.load.data.DataFetcher;com.bumptech.glide.load.data.DataFetcher
Bumptech.Glide.Load.Data.IDataRewinderFactory, Xamarin.Android.Glide;com.bumptech.glide.load.data.DataRewinder$Factory
Bumptech.Glide.Load.Data.IDataRewinderFactory;com.bumptech.glide.load.data.DataRewinder$Factory
com.bumptech.glide.load.data.DataRewinder$Factory;com.bumptech.glide.load.data.DataRewinder$Factory
Bumptech.Glide.Load.Data.IDataRewinder, Xamarin.Android.Glide;com.bumptech.glide.load.data.DataRewinder
Bumptech.Glide.Load.Data.IDataRewinder;com.bumptech.glide.load.data.DataRewinder
com.bumptech.glide.load.data.DataRewinder;com.bumptech.glide.load.data.DataRewinder
Bumptech.Glide.Load.Data.Mediastore.IThumbnailQuery, Xamarin.Android.Glide;com.bumptech.glide.load.data.mediastore.ThumbnailQuery
Bumptech.Glide.Load.Data.Mediastore.IThumbnailQuery;com.bumptech.glide.load.data.mediastore.ThumbnailQuery
com.bumptech.glide.load.data.mediastore.ThumbnailQuery;com.bumptech.glide.load.data.mediastore.ThumbnailQuery
Bumptech.Glide.GifDecoder.IGifDecoderBitmapProvider, Xamarin.Android.Glide.GifDecoder;com.bumptech.glide.gifdecoder.GifDecoder$BitmapProvider
Bumptech.Glide.GifDecoder.IGifDecoderBitmapProvider;com.bumptech.glide.gifdecoder.GifDecoder$BitmapProvider
com.bumptech.glide.gifdecoder.GifDecoder$BitmapProvider;com.bumptech.glide.gifdecoder.GifDecoder$BitmapProvider
Bumptech.Glide.GifDecoder.IGifDecoderGifDecodeStatus, Xamarin.Android.Glide.GifDecoder;com.bumptech.glide.gifdecoder.GifDecoder$GifDecodeStatus
Bumptech.Glide.GifDecoder.IGifDecoderGifDecodeStatus;com.bumptech.glide.gifdecoder.GifDecoder$GifDecodeStatus
com.bumptech.glide.gifdecoder.GifDecoder$GifDecodeStatus;com.bumptech.glide.gifdecoder.GifDecoder$GifDecodeStatus
Bumptech.Glide.GifDecoder.IGifDecoder, Xamarin.Android.Glide.GifDecoder;com.bumptech.glide.gifdecoder.GifDecoder
Bumptech.Glide.GifDecoder.IGifDecoder;com.bumptech.glide.gifdecoder.GifDecoder
com.bumptech.glide.gifdecoder.GifDecoder;com.bumptech.glide.gifdecoder.GifDecoder
AndroidX.Activity.BackEventCompat+ISwipeEdge, Xamarin.AndroidX.Activity;androidx.activity.BackEventCompat$SwipeEdge
AndroidX.Activity.BackEventCompat.ISwipeEdge;androidx.activity.BackEventCompat$SwipeEdge
androidx.activity.BackEventCompat$SwipeEdge;androidx.activity.BackEventCompat$SwipeEdge
AndroidX.Activity.IFullyDrawnReporterOwner, Xamarin.AndroidX.Activity;androidx.activity.FullyDrawnReporterOwner
AndroidX.Activity.IFullyDrawnReporterOwner;androidx.activity.FullyDrawnReporterOwner
androidx.activity.FullyDrawnReporterOwner;androidx.activity.FullyDrawnReporterOwner
AndroidX.Activity.IOnBackPressedDispatcherOwner, Xamarin.AndroidX.Activity;androidx.activity.OnBackPressedDispatcherOwner
AndroidX.Activity.IOnBackPressedDispatcherOwner;androidx.activity.OnBackPressedDispatcherOwner
androidx.activity.OnBackPressedDispatcherOwner;androidx.activity.OnBackPressedDispatcherOwner
AndroidX.Activity.ContextAware.IContextAware, Xamarin.AndroidX.Activity;androidx.activity.contextaware.ContextAware
AndroidX.Activity.ContextAware.IContextAware;androidx.activity.contextaware.ContextAware
androidx.activity.contextaware.ContextAware;androidx.activity.contextaware.ContextAware
AndroidX.Activity.ContextAware.IOnContextAvailableListener, Xamarin.AndroidX.Activity;androidx.activity.contextaware.OnContextAvailableListener
AndroidX.Activity.ContextAware.IOnContextAvailableListener;androidx.activity.contextaware.OnContextAvailableListener
androidx.activity.contextaware.OnContextAvailableListener;androidx.activity.contextaware.OnContextAvailableListener
AndroidX.Activity.ContextAware.IOnContextAvailableListenerImplementor, Xamarin.AndroidX.Activity;mono.androidx.activity.contextaware.OnContextAvailableListenerImplementor
AndroidX.Activity.ContextAware.IOnContextAvailableListenerImplementor;mono.androidx.activity.contextaware.OnContextAvailableListenerImplementor
mono.androidx.activity.contextaware.OnContextAvailableListenerImplementor;mono.androidx.activity.contextaware.OnContextAvailableListenerImplementor
AndroidX.Activity.Result.IActivityResultCallback, Xamarin.AndroidX.Activity;androidx.activity.result.ActivityResultCallback
AndroidX.Activity.Result.IActivityResultCallback;androidx.activity.result.ActivityResultCallback
androidx.activity.result.ActivityResultCallback;androidx.activity.result.ActivityResultCallback
AndroidX.Activity.Result.IActivityResultCaller, Xamarin.AndroidX.Activity;androidx.activity.result.ActivityResultCaller
AndroidX.Activity.Result.IActivityResultCaller;androidx.activity.result.ActivityResultCaller
androidx.activity.result.ActivityResultCaller;androidx.activity.result.ActivityResultCaller
AndroidX.Activity.Result.IActivityResultRegistryOwner, Xamarin.AndroidX.Activity;androidx.activity.result.ActivityResultRegistryOwner
AndroidX.Activity.Result.IActivityResultRegistryOwner;androidx.activity.result.ActivityResultRegistryOwner
androidx.activity.result.ActivityResultRegistryOwner;androidx.activity.result.ActivityResultRegistryOwner
AndroidX.Activity.Result.Contract.ActivityResultContracts+PickVisualMedia+IVisualMediaType, Xamarin.AndroidX.Activity;androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType
AndroidX.Activity.Result.Contract.ActivityResultContracts.PickVisualMedia.IVisualMediaType;androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType
androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType
AndroidX.Annotations.IOptIn, Xamarin.AndroidX.Annotation.Experimental;androidx.annotation.OptIn
AndroidX.Annotations.IOptIn;androidx.annotation.OptIn
androidx.annotation.OptIn;androidx.annotation.OptIn
AndroidX.Annotations.IRequiresOptIn, Xamarin.AndroidX.Annotation.Experimental;androidx.annotation.RequiresOptIn
AndroidX.Annotations.IRequiresOptIn;androidx.annotation.RequiresOptIn
androidx.annotation.RequiresOptIn;androidx.annotation.RequiresOptIn
AndroidX.Annotations.Experimental.IExperimental, Xamarin.AndroidX.Annotation.Experimental;androidx.annotation.experimental.Experimental
AndroidX.Annotations.Experimental.IExperimental;androidx.annotation.experimental.Experimental
androidx.annotation.experimental.Experimental;androidx.annotation.experimental.Experimental
AndroidX.Annotations.Experimental.IUseExperimental, Xamarin.AndroidX.Annotation.Experimental;androidx.annotation.experimental.UseExperimental
AndroidX.Annotations.Experimental.IUseExperimental;androidx.annotation.experimental.UseExperimental
androidx.annotation.experimental.UseExperimental;androidx.annotation.experimental.UseExperimental
AndroidX.Annotations.IAnimatorRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.AnimatorRes
AndroidX.Annotations.IAnimatorRes;androidx.annotation.AnimatorRes
androidx.annotation.AnimatorRes;androidx.annotation.AnimatorRes
AndroidX.Annotations.IAnimRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.AnimRes
AndroidX.Annotations.IAnimRes;androidx.annotation.AnimRes
androidx.annotation.AnimRes;androidx.annotation.AnimRes
AndroidX.Annotations.IAnyRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.AnyRes
AndroidX.Annotations.IAnyRes;androidx.annotation.AnyRes
androidx.annotation.AnyRes;androidx.annotation.AnyRes
AndroidX.Annotations.IAnyThread, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.AnyThread
AndroidX.Annotations.IAnyThread;androidx.annotation.AnyThread
androidx.annotation.AnyThread;androidx.annotation.AnyThread
AndroidX.Annotations.IArrayRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ArrayRes
AndroidX.Annotations.IArrayRes;androidx.annotation.ArrayRes
androidx.annotation.ArrayRes;androidx.annotation.ArrayRes
AndroidX.Annotations.IAttrRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.AttrRes
AndroidX.Annotations.IAttrRes;androidx.annotation.AttrRes
androidx.annotation.AttrRes;androidx.annotation.AttrRes
AndroidX.Annotations.IBinderThread, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.BinderThread
AndroidX.Annotations.IBinderThread;androidx.annotation.BinderThread
androidx.annotation.BinderThread;androidx.annotation.BinderThread
AndroidX.Annotations.IBoolRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.BoolRes
AndroidX.Annotations.IBoolRes;androidx.annotation.BoolRes
androidx.annotation.BoolRes;androidx.annotation.BoolRes
AndroidX.Annotations.ICallSuper, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.CallSuper
AndroidX.Annotations.ICallSuper;androidx.annotation.CallSuper
androidx.annotation.CallSuper;androidx.annotation.CallSuper
AndroidX.Annotations.ICheckResult, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.CheckResult
AndroidX.Annotations.ICheckResult;androidx.annotation.CheckResult
androidx.annotation.CheckResult;androidx.annotation.CheckResult
AndroidX.Annotations.IChecksSdkIntAtLeast, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ChecksSdkIntAtLeast
AndroidX.Annotations.IChecksSdkIntAtLeast;androidx.annotation.ChecksSdkIntAtLeast
androidx.annotation.ChecksSdkIntAtLeast;androidx.annotation.ChecksSdkIntAtLeast
AndroidX.Annotations.IColorInt, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ColorInt
AndroidX.Annotations.IColorInt;androidx.annotation.ColorInt
androidx.annotation.ColorInt;androidx.annotation.ColorInt
AndroidX.Annotations.IColorLong, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ColorLong
AndroidX.Annotations.IColorLong;androidx.annotation.ColorLong
androidx.annotation.ColorLong;androidx.annotation.ColorLong
AndroidX.Annotations.IColorRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ColorRes
AndroidX.Annotations.IColorRes;androidx.annotation.ColorRes
androidx.annotation.ColorRes;androidx.annotation.ColorRes
AndroidX.Annotations.IContentView, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ContentView
AndroidX.Annotations.IContentView;androidx.annotation.ContentView
androidx.annotation.ContentView;androidx.annotation.ContentView
AndroidX.Annotations.IDeprecatedSinceApi, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.DeprecatedSinceApi
AndroidX.Annotations.IDeprecatedSinceApi;androidx.annotation.DeprecatedSinceApi
androidx.annotation.DeprecatedSinceApi;androidx.annotation.DeprecatedSinceApi
AndroidX.Annotations.IDimenRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.DimenRes
AndroidX.Annotations.IDimenRes;androidx.annotation.DimenRes
androidx.annotation.DimenRes;androidx.annotation.DimenRes
AndroidX.Annotations.IDimension, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Dimension
AndroidX.Annotations.IDimension;androidx.annotation.Dimension
androidx.annotation.Dimension;androidx.annotation.Dimension
AndroidX.Annotations.IDiscouraged, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Discouraged
AndroidX.Annotations.IDiscouraged;androidx.annotation.Discouraged
androidx.annotation.Discouraged;androidx.annotation.Discouraged
AndroidX.Annotations.IDisplayContext, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.DisplayContext
AndroidX.Annotations.IDisplayContext;androidx.annotation.DisplayContext
androidx.annotation.DisplayContext;androidx.annotation.DisplayContext
AndroidX.Annotations.IDoNotInline, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.DoNotInline
AndroidX.Annotations.IDoNotInline;androidx.annotation.DoNotInline
androidx.annotation.DoNotInline;androidx.annotation.DoNotInline
AndroidX.Annotations.IDrawableRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.DrawableRes
AndroidX.Annotations.IDrawableRes;androidx.annotation.DrawableRes
androidx.annotation.DrawableRes;androidx.annotation.DrawableRes
AndroidX.Annotations.IEmptySuper, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.EmptySuper
AndroidX.Annotations.IEmptySuper;androidx.annotation.EmptySuper
androidx.annotation.EmptySuper;androidx.annotation.EmptySuper
AndroidX.Annotations.IFloatRange, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.FloatRange
AndroidX.Annotations.IFloatRange;androidx.annotation.FloatRange
androidx.annotation.FloatRange;androidx.annotation.FloatRange
AndroidX.Annotations.IFontRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.FontRes
AndroidX.Annotations.IFontRes;androidx.annotation.FontRes
androidx.annotation.FontRes;androidx.annotation.FontRes
AndroidX.Annotations.IFractionRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.FractionRes
AndroidX.Annotations.IFractionRes;androidx.annotation.FractionRes
androidx.annotation.FractionRes;androidx.annotation.FractionRes
AndroidX.Annotations.IGravityInt, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.GravityInt
AndroidX.Annotations.IGravityInt;androidx.annotation.GravityInt
androidx.annotation.GravityInt;androidx.annotation.GravityInt
AndroidX.Annotations.IGuardedBy, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.GuardedBy
AndroidX.Annotations.IGuardedBy;androidx.annotation.GuardedBy
androidx.annotation.GuardedBy;androidx.annotation.GuardedBy
AndroidX.Annotations.IHalfFloat, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.HalfFloat
AndroidX.Annotations.IHalfFloat;androidx.annotation.HalfFloat
androidx.annotation.HalfFloat;androidx.annotation.HalfFloat
AndroidX.Annotations.IIdRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.IdRes
AndroidX.Annotations.IIdRes;androidx.annotation.IdRes
androidx.annotation.IdRes;androidx.annotation.IdRes
AndroidX.Annotations.IInspectablePropertyEnumEntry, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.InspectableProperty$EnumEntry
AndroidX.Annotations.IInspectablePropertyEnumEntry;androidx.annotation.InspectableProperty$EnumEntry
androidx.annotation.InspectableProperty$EnumEntry;androidx.annotation.InspectableProperty$EnumEntry
AndroidX.Annotations.IInspectablePropertyFlagEntry, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.InspectableProperty$FlagEntry
AndroidX.Annotations.IInspectablePropertyFlagEntry;androidx.annotation.InspectableProperty$FlagEntry
androidx.annotation.InspectableProperty$FlagEntry;androidx.annotation.InspectableProperty$FlagEntry
AndroidX.Annotations.IInspectableProperty, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.InspectableProperty
AndroidX.Annotations.IInspectableProperty;androidx.annotation.InspectableProperty
androidx.annotation.InspectableProperty;androidx.annotation.InspectableProperty
AndroidX.Annotations.IIntDef, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.IntDef
AndroidX.Annotations.IIntDef;androidx.annotation.IntDef
androidx.annotation.IntDef;androidx.annotation.IntDef
AndroidX.Annotations.IIntegerRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.IntegerRes
AndroidX.Annotations.IIntegerRes;androidx.annotation.IntegerRes
androidx.annotation.IntegerRes;androidx.annotation.IntegerRes
AndroidX.Annotations.IInterpolatorRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.InterpolatorRes
AndroidX.Annotations.IInterpolatorRes;androidx.annotation.InterpolatorRes
androidx.annotation.InterpolatorRes;androidx.annotation.InterpolatorRes
AndroidX.Annotations.IIntRange, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.IntRange
AndroidX.Annotations.IIntRange;androidx.annotation.IntRange
androidx.annotation.IntRange;androidx.annotation.IntRange
AndroidX.Annotations.IKeep, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Keep
AndroidX.Annotations.IKeep;androidx.annotation.Keep
androidx.annotation.Keep;androidx.annotation.Keep
AndroidX.Annotations.ILayoutRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.LayoutRes
AndroidX.Annotations.ILayoutRes;androidx.annotation.LayoutRes
androidx.annotation.LayoutRes;androidx.annotation.LayoutRes
AndroidX.Annotations.ILongDef, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.LongDef
AndroidX.Annotations.ILongDef;androidx.annotation.LongDef
androidx.annotation.LongDef;androidx.annotation.LongDef
AndroidX.Annotations.IMainThread, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.MainThread
AndroidX.Annotations.IMainThread;androidx.annotation.MainThread
androidx.annotation.MainThread;androidx.annotation.MainThread
AndroidX.Annotations.IMenuRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.MenuRes
AndroidX.Annotations.IMenuRes;androidx.annotation.MenuRes
androidx.annotation.MenuRes;androidx.annotation.MenuRes
AndroidX.Annotations.INavigationRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.NavigationRes
AndroidX.Annotations.INavigationRes;androidx.annotation.NavigationRes
androidx.annotation.NavigationRes;androidx.annotation.NavigationRes
AndroidX.Annotations.INonNull, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.NonNull
AndroidX.Annotations.INonNull;androidx.annotation.NonNull
androidx.annotation.NonNull;androidx.annotation.NonNull
AndroidX.Annotations.INonUiContext, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.NonUiContext
AndroidX.Annotations.INonUiContext;androidx.annotation.NonUiContext
androidx.annotation.NonUiContext;androidx.annotation.NonUiContext
AndroidX.Annotations.INullable, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Nullable
AndroidX.Annotations.INullable;androidx.annotation.Nullable
androidx.annotation.Nullable;androidx.annotation.Nullable
AndroidX.Annotations.IOpenForTesting, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.OpenForTesting
AndroidX.Annotations.IOpenForTesting;androidx.annotation.OpenForTesting
androidx.annotation.OpenForTesting;androidx.annotation.OpenForTesting
AndroidX.Annotations.IPluralsRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.PluralsRes
AndroidX.Annotations.IPluralsRes;androidx.annotation.PluralsRes
androidx.annotation.PluralsRes;androidx.annotation.PluralsRes
AndroidX.Annotations.IPx, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Px
AndroidX.Annotations.IPx;androidx.annotation.Px
androidx.annotation.Px;androidx.annotation.Px
AndroidX.Annotations.IRawRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RawRes
AndroidX.Annotations.IRawRes;androidx.annotation.RawRes
androidx.annotation.RawRes;androidx.annotation.RawRes
AndroidX.Annotations.IReplaceWith, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ReplaceWith
AndroidX.Annotations.IReplaceWith;androidx.annotation.ReplaceWith
androidx.annotation.ReplaceWith;androidx.annotation.ReplaceWith
AndroidX.Annotations.IRequiresApi, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresApi
AndroidX.Annotations.IRequiresApi;androidx.annotation.RequiresApi
androidx.annotation.RequiresApi;androidx.annotation.RequiresApi
AndroidX.Annotations.IRequiresExtensionContainer, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresExtension$Container
AndroidX.Annotations.IRequiresExtensionContainer;androidx.annotation.RequiresExtension$Container
androidx.annotation.RequiresExtension$Container;androidx.annotation.RequiresExtension$Container
AndroidX.Annotations.IRequiresExtension, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresExtension
AndroidX.Annotations.IRequiresExtension;androidx.annotation.RequiresExtension
androidx.annotation.RequiresExtension;androidx.annotation.RequiresExtension
AndroidX.Annotations.IRequiresFeature, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresFeature
AndroidX.Annotations.IRequiresFeature;androidx.annotation.RequiresFeature
androidx.annotation.RequiresFeature;androidx.annotation.RequiresFeature
AndroidX.Annotations.IRequiresPermissionRead, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresPermission$Read
AndroidX.Annotations.IRequiresPermissionRead;androidx.annotation.RequiresPermission$Read
androidx.annotation.RequiresPermission$Read;androidx.annotation.RequiresPermission$Read
AndroidX.Annotations.IRequiresPermissionWrite, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresPermission$Write
AndroidX.Annotations.IRequiresPermissionWrite;androidx.annotation.RequiresPermission$Write
androidx.annotation.RequiresPermission$Write;androidx.annotation.RequiresPermission$Write
AndroidX.Annotations.IRequiresPermission, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RequiresPermission
AndroidX.Annotations.IRequiresPermission;androidx.annotation.RequiresPermission
androidx.annotation.RequiresPermission;androidx.annotation.RequiresPermission
AndroidX.Annotations.IRestrictTo, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.RestrictTo
AndroidX.Annotations.IRestrictTo;androidx.annotation.RestrictTo
androidx.annotation.RestrictTo;androidx.annotation.RestrictTo
AndroidX.Annotations.IReturnThis, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.ReturnThis
AndroidX.Annotations.IReturnThis;androidx.annotation.ReturnThis
androidx.annotation.ReturnThis;androidx.annotation.ReturnThis
AndroidX.Annotations.ISize, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.Size
AndroidX.Annotations.ISize;androidx.annotation.Size
androidx.annotation.Size;androidx.annotation.Size
AndroidX.Annotations.IStringDef, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.StringDef
AndroidX.Annotations.IStringDef;androidx.annotation.StringDef
androidx.annotation.StringDef;androidx.annotation.StringDef
AndroidX.Annotations.IStringRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.StringRes
AndroidX.Annotations.IStringRes;androidx.annotation.StringRes
androidx.annotation.StringRes;androidx.annotation.StringRes
AndroidX.Annotations.IStyleableRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.StyleableRes
AndroidX.Annotations.IStyleableRes;androidx.annotation.StyleableRes
androidx.annotation.StyleableRes;androidx.annotation.StyleableRes
AndroidX.Annotations.IStyleRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.StyleRes
AndroidX.Annotations.IStyleRes;androidx.annotation.StyleRes
androidx.annotation.StyleRes;androidx.annotation.StyleRes
AndroidX.Annotations.ITransitionRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.TransitionRes
AndroidX.Annotations.ITransitionRes;androidx.annotation.TransitionRes
androidx.annotation.TransitionRes;androidx.annotation.TransitionRes
AndroidX.Annotations.IUiContext, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.UiContext
AndroidX.Annotations.IUiContext;androidx.annotation.UiContext
androidx.annotation.UiContext;androidx.annotation.UiContext
AndroidX.Annotations.IUiThread, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.UiThread
AndroidX.Annotations.IUiThread;androidx.annotation.UiThread
androidx.annotation.UiThread;androidx.annotation.UiThread
AndroidX.Annotations.IVisibleForTesting, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.VisibleForTesting
AndroidX.Annotations.IVisibleForTesting;androidx.annotation.VisibleForTesting
androidx.annotation.VisibleForTesting;androidx.annotation.VisibleForTesting
AndroidX.Annotations.IWorkerThread, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.WorkerThread
AndroidX.Annotations.IWorkerThread;androidx.annotation.WorkerThread
androidx.annotation.WorkerThread;androidx.annotation.WorkerThread
AndroidX.Annotations.IXmlRes, Xamarin.AndroidX.Annotation.Jvm;androidx.annotation.XmlRes
AndroidX.Annotations.IXmlRes;androidx.annotation.XmlRes
androidx.annotation.XmlRes;androidx.annotation.XmlRes
AndroidX.AppCompat.Graphics.Drawable.DrawerArrowDrawable+IArrowDirection, Xamarin.AndroidX.AppCompat;androidx.appcompat.graphics.drawable.DrawerArrowDrawable$ArrowDirection
AndroidX.AppCompat.Graphics.Drawable.DrawerArrowDrawable.IArrowDirection;androidx.appcompat.graphics.drawable.DrawerArrowDrawable$ArrowDirection
androidx.appcompat.graphics.drawable.DrawerArrowDrawable$ArrowDirection;androidx.appcompat.graphics.drawable.DrawerArrowDrawable$ArrowDirection
AndroidX.AppCompat.App.AlertDialog+IDialogInterfaceOnClickListenerImplementor, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnClickListenerImplementor
AndroidX.AppCompat.App.AlertDialog.IDialogInterfaceOnClickListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnClickListenerImplementor
androidx.appcompat.app.AlertDialog_IDialogInterfaceOnClickListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnClickListenerImplementor
AndroidX.AppCompat.App.AlertDialog+IDialogInterfaceOnCancelListenerImplementor, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnCancelListenerImplementor
AndroidX.AppCompat.App.AlertDialog.IDialogInterfaceOnCancelListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnCancelListenerImplementor
androidx.appcompat.app.AlertDialog_IDialogInterfaceOnCancelListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnCancelListenerImplementor
AndroidX.AppCompat.App.AlertDialog+IDialogInterfaceOnMultiChoiceClickListenerImplementor, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor
AndroidX.AppCompat.App.AlertDialog.IDialogInterfaceOnMultiChoiceClickListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor
androidx.appcompat.app.AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor;androidx.appcompat.app.AlertDialog_IDialogInterfaceOnMultiChoiceClickListenerImplementor
AndroidX.AppCompat.App.ActionBar+IDisplayOptions, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBar$DisplayOptions
AndroidX.AppCompat.App.ActionBar.IDisplayOptions;androidx.appcompat.app.ActionBar$DisplayOptions
androidx.appcompat.app.ActionBar$DisplayOptions;androidx.appcompat.app.ActionBar$DisplayOptions
AndroidX.AppCompat.App.ActionBar+INavigationMode, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBar$NavigationMode
AndroidX.AppCompat.App.ActionBar.INavigationMode;androidx.appcompat.app.ActionBar$NavigationMode
androidx.appcompat.app.ActionBar$NavigationMode;androidx.appcompat.app.ActionBar$NavigationMode
AndroidX.AppCompat.App.ActionBar+IOnMenuVisibilityListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBar$OnMenuVisibilityListener
AndroidX.AppCompat.App.ActionBar.IOnMenuVisibilityListener;androidx.appcompat.app.ActionBar$OnMenuVisibilityListener
androidx.appcompat.app.ActionBar$OnMenuVisibilityListener;androidx.appcompat.app.ActionBar$OnMenuVisibilityListener
AndroidX.AppCompat.App.ActionBar+IOnMenuVisibilityListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.app.ActionBar_OnMenuVisibilityListenerImplementor
AndroidX.AppCompat.App.ActionBar.IOnMenuVisibilityListenerImplementor;mono.androidx.appcompat.app.ActionBar_OnMenuVisibilityListenerImplementor
mono.androidx.appcompat.app.ActionBar_OnMenuVisibilityListenerImplementor;mono.androidx.appcompat.app.ActionBar_OnMenuVisibilityListenerImplementor
AndroidX.AppCompat.App.ActionBar+IOnNavigationListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBar$OnNavigationListener
AndroidX.AppCompat.App.ActionBar.IOnNavigationListener;androidx.appcompat.app.ActionBar$OnNavigationListener
androidx.appcompat.app.ActionBar$OnNavigationListener;androidx.appcompat.app.ActionBar$OnNavigationListener
AndroidX.AppCompat.App.ActionBar+IOnNavigationListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.app.ActionBar_OnNavigationListenerImplementor
AndroidX.AppCompat.App.ActionBar.IOnNavigationListenerImplementor;mono.androidx.appcompat.app.ActionBar_OnNavigationListenerImplementor
mono.androidx.appcompat.app.ActionBar_OnNavigationListenerImplementor;mono.androidx.appcompat.app.ActionBar_OnNavigationListenerImplementor
AndroidX.AppCompat.App.ActionBar+ITabListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBar$TabListener
AndroidX.AppCompat.App.ActionBar.ITabListener;androidx.appcompat.app.ActionBar$TabListener
androidx.appcompat.app.ActionBar$TabListener;androidx.appcompat.app.ActionBar$TabListener
AndroidX.AppCompat.App.ActionBar+ITabListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.app.ActionBar_TabListenerImplementor
AndroidX.AppCompat.App.ActionBar.ITabListenerImplementor;mono.androidx.appcompat.app.ActionBar_TabListenerImplementor
mono.androidx.appcompat.app.ActionBar_TabListenerImplementor;mono.androidx.appcompat.app.ActionBar_TabListenerImplementor
AndroidX.AppCompat.App.ActionBarDrawerToggle+IDelegate, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBarDrawerToggle$Delegate
AndroidX.AppCompat.App.ActionBarDrawerToggle.IDelegate;androidx.appcompat.app.ActionBarDrawerToggle$Delegate
androidx.appcompat.app.ActionBarDrawerToggle$Delegate;androidx.appcompat.app.ActionBarDrawerToggle$Delegate
AndroidX.AppCompat.App.ActionBarDrawerToggle+IDelegateProvider, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider
AndroidX.AppCompat.App.ActionBarDrawerToggle.IDelegateProvider;androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider
androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider;androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider
AndroidX.AppCompat.App.AppCompatDelegate+INightMode, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.AppCompatDelegate$NightMode
AndroidX.AppCompat.App.AppCompatDelegate.INightMode;androidx.appcompat.app.AppCompatDelegate$NightMode
androidx.appcompat.app.AppCompatDelegate$NightMode;androidx.appcompat.app.AppCompatDelegate$NightMode
AndroidX.AppCompat.App.IAppCompatCallback, Xamarin.AndroidX.AppCompat;androidx.appcompat.app.AppCompatCallback
AndroidX.AppCompat.App.IAppCompatCallback;androidx.appcompat.app.AppCompatCallback
androidx.appcompat.app.AppCompatCallback;androidx.appcompat.app.AppCompatCallback
AndroidX.AppCompat.Widget.ActionBarOverlayLayout+IActionBarVisibilityCallback, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback
AndroidX.AppCompat.Widget.ActionBarOverlayLayout.IActionBarVisibilityCallback;androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback
androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback;androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback
AndroidX.AppCompat.Widget.Toolbar+NavigationOnClickEventDispatcher, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.Toolbar_NavigationOnClickEventDispatcher
AndroidX.AppCompat.Widget.Toolbar.NavigationOnClickEventDispatcher;androidx.appcompat.widget.Toolbar_NavigationOnClickEventDispatcher
androidx.appcompat.widget.Toolbar_NavigationOnClickEventDispatcher;androidx.appcompat.widget.Toolbar_NavigationOnClickEventDispatcher
AndroidX.AppCompat.Widget.Toolbar+IOnMenuItemClickListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.Toolbar$OnMenuItemClickListener
AndroidX.AppCompat.Widget.Toolbar.IOnMenuItemClickListener;androidx.appcompat.widget.Toolbar$OnMenuItemClickListener
androidx.appcompat.widget.Toolbar$OnMenuItemClickListener;androidx.appcompat.widget.Toolbar$OnMenuItemClickListener
AndroidX.AppCompat.Widget.Toolbar+IOnMenuItemClickListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.Toolbar_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.Toolbar.IOnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.Toolbar_OnMenuItemClickListenerImplementor
mono.androidx.appcompat.widget.Toolbar_OnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.Toolbar_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.ActionMenuView+IActionMenuChildView, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
AndroidX.AppCompat.Widget.ActionMenuView.IActionMenuChildView;androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
androidx.appcompat.widget.ActionMenuView$ActionMenuChildView;androidx.appcompat.widget.ActionMenuView$ActionMenuChildView
AndroidX.AppCompat.Widget.ActionMenuView+IOnMenuItemClickListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener
AndroidX.AppCompat.Widget.ActionMenuView.IOnMenuItemClickListener;androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener
androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener;androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener
AndroidX.AppCompat.Widget.ActionMenuView+IOnMenuItemClickListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.ActionMenuView_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.ActionMenuView.IOnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.ActionMenuView_OnMenuItemClickListenerImplementor
mono.androidx.appcompat.widget.ActionMenuView_OnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.ActionMenuView_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.ContentFrameLayout+IOnAttachListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ContentFrameLayout$OnAttachListener
AndroidX.AppCompat.Widget.ContentFrameLayout.IOnAttachListener;androidx.appcompat.widget.ContentFrameLayout$OnAttachListener
androidx.appcompat.widget.ContentFrameLayout$OnAttachListener;androidx.appcompat.widget.ContentFrameLayout$OnAttachListener
AndroidX.AppCompat.Widget.ContentFrameLayout+IOnAttachListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.ContentFrameLayout_OnAttachListenerImplementor
AndroidX.AppCompat.Widget.ContentFrameLayout.IOnAttachListenerImplementor;mono.androidx.appcompat.widget.ContentFrameLayout_OnAttachListenerImplementor
mono.androidx.appcompat.widget.ContentFrameLayout_OnAttachListenerImplementor;mono.androidx.appcompat.widget.ContentFrameLayout_OnAttachListenerImplementor
AndroidX.AppCompat.Widget.IDecorContentParent, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.DecorContentParent
AndroidX.AppCompat.Widget.IDecorContentParent;androidx.appcompat.widget.DecorContentParent
androidx.appcompat.widget.DecorContentParent;androidx.appcompat.widget.DecorContentParent
AndroidX.AppCompat.Widget.IDecorToolbar, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.DecorToolbar
AndroidX.AppCompat.Widget.IDecorToolbar;androidx.appcompat.widget.DecorToolbar
androidx.appcompat.widget.DecorToolbar;androidx.appcompat.widget.DecorToolbar
AndroidX.AppCompat.Widget.IEmojiCompatConfigurationView, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.EmojiCompatConfigurationView
AndroidX.AppCompat.Widget.IEmojiCompatConfigurationView;androidx.appcompat.widget.EmojiCompatConfigurationView
androidx.appcompat.widget.EmojiCompatConfigurationView;androidx.appcompat.widget.EmojiCompatConfigurationView
AndroidX.AppCompat.Widget.IFitWindowsViewGroupOnFitSystemWindowsListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener
AndroidX.AppCompat.Widget.IFitWindowsViewGroupOnFitSystemWindowsListener;androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener
androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener;androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener
AndroidX.AppCompat.Widget.IFitWindowsViewGroupOnFitSystemWindowsListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.FitWindowsViewGroup_OnFitSystemWindowsListenerImplementor
AndroidX.AppCompat.Widget.IFitWindowsViewGroupOnFitSystemWindowsListenerImplementor;mono.androidx.appcompat.widget.FitWindowsViewGroup_OnFitSystemWindowsListenerImplementor
mono.androidx.appcompat.widget.FitWindowsViewGroup_OnFitSystemWindowsListenerImplementor;mono.androidx.appcompat.widget.FitWindowsViewGroup_OnFitSystemWindowsListenerImplementor
AndroidX.AppCompat.Widget.IFitWindowsViewGroup, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.FitWindowsViewGroup
AndroidX.AppCompat.Widget.IFitWindowsViewGroup;androidx.appcompat.widget.FitWindowsViewGroup
androidx.appcompat.widget.FitWindowsViewGroup;androidx.appcompat.widget.FitWindowsViewGroup
AndroidX.AppCompat.Widget.IMenuItemHoverListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.MenuItemHoverListener
AndroidX.AppCompat.Widget.IMenuItemHoverListener;androidx.appcompat.widget.MenuItemHoverListener
androidx.appcompat.widget.MenuItemHoverListener;androidx.appcompat.widget.MenuItemHoverListener
AndroidX.AppCompat.Widget.IMenuItemHoverListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.MenuItemHoverListenerImplementor
AndroidX.AppCompat.Widget.IMenuItemHoverListenerImplementor;mono.androidx.appcompat.widget.MenuItemHoverListenerImplementor
mono.androidx.appcompat.widget.MenuItemHoverListenerImplementor;mono.androidx.appcompat.widget.MenuItemHoverListenerImplementor
AndroidX.AppCompat.Widget.IThemedSpinnerAdapter, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ThemedSpinnerAdapter
AndroidX.AppCompat.Widget.IThemedSpinnerAdapter;androidx.appcompat.widget.ThemedSpinnerAdapter
androidx.appcompat.widget.ThemedSpinnerAdapter;androidx.appcompat.widget.ThemedSpinnerAdapter
AndroidX.AppCompat.Widget.IWithHint, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.WithHint
AndroidX.AppCompat.Widget.IWithHint;androidx.appcompat.widget.WithHint
androidx.appcompat.widget.WithHint;androidx.appcompat.widget.WithHint
AndroidX.AppCompat.Widget.LinearLayoutCompat+IDividerMode, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.LinearLayoutCompat$DividerMode
AndroidX.AppCompat.Widget.LinearLayoutCompat.IDividerMode;androidx.appcompat.widget.LinearLayoutCompat$DividerMode
androidx.appcompat.widget.LinearLayoutCompat$DividerMode;androidx.appcompat.widget.LinearLayoutCompat$DividerMode
AndroidX.AppCompat.Widget.LinearLayoutCompat+IOrientationMode, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.LinearLayoutCompat$OrientationMode
AndroidX.AppCompat.Widget.LinearLayoutCompat.IOrientationMode;androidx.appcompat.widget.LinearLayoutCompat$OrientationMode
androidx.appcompat.widget.LinearLayoutCompat$OrientationMode;androidx.appcompat.widget.LinearLayoutCompat$OrientationMode
AndroidX.AppCompat.Widget.PopupMenu+IOnDismissListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.PopupMenu$OnDismissListener
AndroidX.AppCompat.Widget.PopupMenu.IOnDismissListener;androidx.appcompat.widget.PopupMenu$OnDismissListener
androidx.appcompat.widget.PopupMenu$OnDismissListener;androidx.appcompat.widget.PopupMenu$OnDismissListener
AndroidX.AppCompat.Widget.PopupMenu+IOnDismissListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.PopupMenu_OnDismissListenerImplementor
AndroidX.AppCompat.Widget.PopupMenu.IOnDismissListenerImplementor;mono.androidx.appcompat.widget.PopupMenu_OnDismissListenerImplementor
mono.androidx.appcompat.widget.PopupMenu_OnDismissListenerImplementor;mono.androidx.appcompat.widget.PopupMenu_OnDismissListenerImplementor
AndroidX.AppCompat.Widget.PopupMenu+IOnMenuItemClickListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.PopupMenu$OnMenuItemClickListener
AndroidX.AppCompat.Widget.PopupMenu.IOnMenuItemClickListener;androidx.appcompat.widget.PopupMenu$OnMenuItemClickListener
androidx.appcompat.widget.PopupMenu$OnMenuItemClickListener;androidx.appcompat.widget.PopupMenu$OnMenuItemClickListener
AndroidX.AppCompat.Widget.PopupMenu+IOnMenuItemClickListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.PopupMenu_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.PopupMenu.IOnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.PopupMenu_OnMenuItemClickListenerImplementor
mono.androidx.appcompat.widget.PopupMenu_OnMenuItemClickListenerImplementor;mono.androidx.appcompat.widget.PopupMenu_OnMenuItemClickListenerImplementor
AndroidX.AppCompat.Widget.SearchView+IOnCloseListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.SearchView$OnCloseListener
AndroidX.AppCompat.Widget.SearchView.IOnCloseListener;androidx.appcompat.widget.SearchView$OnCloseListener
androidx.appcompat.widget.SearchView$OnCloseListener;androidx.appcompat.widget.SearchView$OnCloseListener
AndroidX.AppCompat.Widget.SearchView+IOnCloseListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.SearchView_OnCloseListenerImplementor
AndroidX.AppCompat.Widget.SearchView.IOnCloseListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnCloseListenerImplementor
mono.androidx.appcompat.widget.SearchView_OnCloseListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnCloseListenerImplementor
AndroidX.AppCompat.Widget.SearchView+IOnQueryTextListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.SearchView$OnQueryTextListener
AndroidX.AppCompat.Widget.SearchView.IOnQueryTextListener;androidx.appcompat.widget.SearchView$OnQueryTextListener
androidx.appcompat.widget.SearchView$OnQueryTextListener;androidx.appcompat.widget.SearchView$OnQueryTextListener
AndroidX.AppCompat.Widget.SearchView+IOnQueryTextListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.SearchView_OnQueryTextListenerImplementor
AndroidX.AppCompat.Widget.SearchView.IOnQueryTextListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnQueryTextListenerImplementor
mono.androidx.appcompat.widget.SearchView_OnQueryTextListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnQueryTextListenerImplementor
AndroidX.AppCompat.Widget.SearchView+IOnSuggestionListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.SearchView$OnSuggestionListener
AndroidX.AppCompat.Widget.SearchView.IOnSuggestionListener;androidx.appcompat.widget.SearchView$OnSuggestionListener
androidx.appcompat.widget.SearchView$OnSuggestionListener;androidx.appcompat.widget.SearchView$OnSuggestionListener
AndroidX.AppCompat.Widget.SearchView+IOnSuggestionListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.SearchView_OnSuggestionListenerImplementor
AndroidX.AppCompat.Widget.SearchView.IOnSuggestionListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnSuggestionListenerImplementor
mono.androidx.appcompat.widget.SearchView_OnSuggestionListenerImplementor;mono.androidx.appcompat.widget.SearchView_OnSuggestionListenerImplementor
AndroidX.AppCompat.Widget.ShareActionProvider+IOnShareTargetSelectedListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ShareActionProvider$OnShareTargetSelectedListener
AndroidX.AppCompat.Widget.ShareActionProvider.IOnShareTargetSelectedListener;androidx.appcompat.widget.ShareActionProvider$OnShareTargetSelectedListener
androidx.appcompat.widget.ShareActionProvider$OnShareTargetSelectedListener;androidx.appcompat.widget.ShareActionProvider$OnShareTargetSelectedListener
AndroidX.AppCompat.Widget.ShareActionProvider+IOnShareTargetSelectedListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.ShareActionProvider_OnShareTargetSelectedListenerImplementor
AndroidX.AppCompat.Widget.ShareActionProvider.IOnShareTargetSelectedListenerImplementor;mono.androidx.appcompat.widget.ShareActionProvider_OnShareTargetSelectedListenerImplementor
mono.androidx.appcompat.widget.ShareActionProvider_OnShareTargetSelectedListenerImplementor;mono.androidx.appcompat.widget.ShareActionProvider_OnShareTargetSelectedListenerImplementor
AndroidX.AppCompat.Widget.ViewStubCompat+IOnInflateListener, Xamarin.AndroidX.AppCompat;androidx.appcompat.widget.ViewStubCompat$OnInflateListener
AndroidX.AppCompat.Widget.ViewStubCompat.IOnInflateListener;androidx.appcompat.widget.ViewStubCompat$OnInflateListener
androidx.appcompat.widget.ViewStubCompat$OnInflateListener;androidx.appcompat.widget.ViewStubCompat$OnInflateListener
AndroidX.AppCompat.Widget.ViewStubCompat+IOnInflateListenerImplementor, Xamarin.AndroidX.AppCompat;mono.androidx.appcompat.widget.ViewStubCompat_OnInflateListenerImplementor
AndroidX.AppCompat.Widget.ViewStubCompat.IOnInflateListenerImplementor;mono.androidx.appcompat.widget.ViewStubCompat_OnInflateListenerImplementor
mono.androidx.appcompat.widget.ViewStubCompat_OnInflateListenerImplementor;mono.androidx.appcompat.widget.ViewStubCompat_OnInflateListenerImplementor
AndroidX.AppCompat.View.ActionMode+ICallback, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.ActionMode$Callback
AndroidX.AppCompat.View.ActionMode.ICallback;androidx.appcompat.view.ActionMode$Callback
androidx.appcompat.view.ActionMode$Callback;androidx.appcompat.view.ActionMode$Callback
AndroidX.AppCompat.View.ICollapsibleActionView, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.CollapsibleActionView
AndroidX.AppCompat.View.ICollapsibleActionView;androidx.appcompat.view.CollapsibleActionView
androidx.appcompat.view.CollapsibleActionView;androidx.appcompat.view.CollapsibleActionView
AndroidX.AppCompat.View.Menu.MenuBuilder+ICallback, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuBuilder$Callback
AndroidX.AppCompat.View.Menu.MenuBuilder.ICallback;androidx.appcompat.view.menu.MenuBuilder$Callback
androidx.appcompat.view.menu.MenuBuilder$Callback;androidx.appcompat.view.menu.MenuBuilder$Callback
AndroidX.AppCompat.View.Menu.MenuBuilder+IItemInvoker, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuBuilder$ItemInvoker
AndroidX.AppCompat.View.Menu.MenuBuilder.IItemInvoker;androidx.appcompat.view.menu.MenuBuilder$ItemInvoker
androidx.appcompat.view.menu.MenuBuilder$ItemInvoker;androidx.appcompat.view.menu.MenuBuilder$ItemInvoker
AndroidX.AppCompat.View.Menu.IMenuPresenterCallback, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuPresenter$Callback
AndroidX.AppCompat.View.Menu.IMenuPresenterCallback;androidx.appcompat.view.menu.MenuPresenter$Callback
androidx.appcompat.view.menu.MenuPresenter$Callback;androidx.appcompat.view.menu.MenuPresenter$Callback
AndroidX.AppCompat.View.Menu.IMenuPresenter, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuPresenter
AndroidX.AppCompat.View.Menu.IMenuPresenter;androidx.appcompat.view.menu.MenuPresenter
androidx.appcompat.view.menu.MenuPresenter;androidx.appcompat.view.menu.MenuPresenter
AndroidX.AppCompat.View.Menu.IMenuViewItemView, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuView$ItemView
AndroidX.AppCompat.View.Menu.IMenuViewItemView;androidx.appcompat.view.menu.MenuView$ItemView
androidx.appcompat.view.menu.MenuView$ItemView;androidx.appcompat.view.menu.MenuView$ItemView
AndroidX.AppCompat.View.Menu.IMenuView, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.MenuView
AndroidX.AppCompat.View.Menu.IMenuView;androidx.appcompat.view.menu.MenuView
androidx.appcompat.view.menu.MenuView;androidx.appcompat.view.menu.MenuView
AndroidX.AppCompat.View.Menu.IShowableListMenu, Xamarin.AndroidX.AppCompat;androidx.appcompat.view.menu.ShowableListMenu
AndroidX.AppCompat.View.Menu.IShowableListMenu;androidx.appcompat.view.menu.ShowableListMenu
androidx.appcompat.view.menu.ShowableListMenu;androidx.appcompat.view.menu.ShowableListMenu
AndroidX.AppCompat.Widget.ResourceManagerInternal+IResourceManagerHooks, Xamarin.AndroidX.AppCompat.AppCompatResources;androidx.appcompat.widget.ResourceManagerInternal$ResourceManagerHooks
AndroidX.AppCompat.Widget.ResourceManagerInternal.IResourceManagerHooks;androidx.appcompat.widget.ResourceManagerInternal$ResourceManagerHooks
androidx.appcompat.widget.ResourceManagerInternal$ResourceManagerHooks;androidx.appcompat.widget.ResourceManagerInternal$ResourceManagerHooks
AndroidX.AppCompat.Graphics.Drawable.DrawableContainer, Xamarin.AndroidX.AppCompat.AppCompatResources;crc6439358991bbf5dbbd.DrawableContainer
AndroidX.AppCompat.Graphics.Drawable.DrawableContainer;crc6439358991bbf5dbbd.DrawableContainer
androidx.appcompat.graphics.drawable.DrawableContainer;crc6439358991bbf5dbbd.DrawableContainer
AndroidX.AppCompat.Graphics.Drawable.DrawableWrapper, Xamarin.AndroidX.AppCompat.AppCompatResources;crc6439358991bbf5dbbd.DrawableWrapper
AndroidX.AppCompat.Graphics.Drawable.DrawableWrapper;crc6439358991bbf5dbbd.DrawableWrapper
androidx.appcompat.graphics.drawable.DrawableWrapper;crc6439358991bbf5dbbd.DrawableWrapper
AndroidX.Arch.Core.Util.IFunction, Xamarin.AndroidX.Arch.Core.Common;androidx.arch.core.util.Function
AndroidX.Arch.Core.Util.IFunction;androidx.arch.core.util.Function
androidx.arch.core.util.Function;androidx.arch.core.util.Function
Android.Support.Customtabs.Trusted.ITrustedWebActivityCallback, Xamarin.AndroidX.Browser;android.support.customtabs.trusted.ITrustedWebActivityCallback
Android.Support.Customtabs.Trusted.ITrustedWebActivityCallback;android.support.customtabs.trusted.ITrustedWebActivityCallback
android.support.customtabs.trusted.ITrustedWebActivityCallback;android.support.customtabs.trusted.ITrustedWebActivityCallback
Android.Support.Customtabs.Trusted.ITrustedWebActivityService, Xamarin.AndroidX.Browser;android.support.customtabs.trusted.ITrustedWebActivityService
Android.Support.Customtabs.Trusted.ITrustedWebActivityService;android.support.customtabs.trusted.ITrustedWebActivityService
android.support.customtabs.trusted.ITrustedWebActivityService;android.support.customtabs.trusted.ITrustedWebActivityService
Android.Support.CustomTabs.ICustomTabsCallback, Xamarin.AndroidX.Browser;android.support.customtabs.ICustomTabsCallback
Android.Support.CustomTabs.ICustomTabsCallback;android.support.customtabs.ICustomTabsCallback
android.support.customtabs.ICustomTabsCallback;android.support.customtabs.ICustomTabsCallback
Android.Support.CustomTabs.ICustomTabsService, Xamarin.AndroidX.Browser;android.support.customtabs.ICustomTabsService
Android.Support.CustomTabs.ICustomTabsService;android.support.customtabs.ICustomTabsService
android.support.customtabs.ICustomTabsService;android.support.customtabs.ICustomTabsService
Android.Support.CustomTabs.IEngagementSignalsCallback, Xamarin.AndroidX.Browser;android.support.customtabs.IEngagementSignalsCallback
Android.Support.CustomTabs.IEngagementSignalsCallback;android.support.customtabs.IEngagementSignalsCallback
android.support.customtabs.IEngagementSignalsCallback;android.support.customtabs.IEngagementSignalsCallback
Android.Support.CustomTabs.IPostMessageService, Xamarin.AndroidX.Browser;android.support.customtabs.IPostMessageService
Android.Support.CustomTabs.IPostMessageService;android.support.customtabs.IPostMessageService
android.support.customtabs.IPostMessageService;android.support.customtabs.IPostMessageService
AndroidX.Browser.Trusted.ITokenStore, Xamarin.AndroidX.Browser;androidx.browser.trusted.TokenStore
AndroidX.Browser.Trusted.ITokenStore;androidx.browser.trusted.TokenStore
androidx.browser.trusted.TokenStore;androidx.browser.trusted.TokenStore
AndroidX.Browser.Trusted.ITrustedWebActivityDisplayMode, Xamarin.AndroidX.Browser;androidx.browser.trusted.TrustedWebActivityDisplayMode
AndroidX.Browser.Trusted.ITrustedWebActivityDisplayMode;androidx.browser.trusted.TrustedWebActivityDisplayMode
androidx.browser.trusted.TrustedWebActivityDisplayMode;androidx.browser.trusted.TrustedWebActivityDisplayMode
AndroidX.Browser.Trusted.ScreenOrientation+ILockType, Xamarin.AndroidX.Browser;androidx.browser.trusted.ScreenOrientation$LockType
AndroidX.Browser.Trusted.ScreenOrientation.ILockType;androidx.browser.trusted.ScreenOrientation$LockType
androidx.browser.trusted.ScreenOrientation$LockType;androidx.browser.trusted.ScreenOrientation$LockType
AndroidX.Browser.Trusted.Sharing.ShareTarget+IEncodingType, Xamarin.AndroidX.Browser;androidx.browser.trusted.sharing.ShareTarget$EncodingType
AndroidX.Browser.Trusted.Sharing.ShareTarget.IEncodingType;androidx.browser.trusted.sharing.ShareTarget$EncodingType
androidx.browser.trusted.sharing.ShareTarget$EncodingType;androidx.browser.trusted.sharing.ShareTarget$EncodingType
AndroidX.Browser.Trusted.Sharing.ShareTarget+IRequestMethod, Xamarin.AndroidX.Browser;androidx.browser.trusted.sharing.ShareTarget$RequestMethod
AndroidX.Browser.Trusted.Sharing.ShareTarget.IRequestMethod;androidx.browser.trusted.sharing.ShareTarget$RequestMethod
androidx.browser.trusted.sharing.ShareTarget$RequestMethod;androidx.browser.trusted.sharing.ShareTarget$RequestMethod
AndroidX.Browser.BrowserActions.BrowserActionsIntent+IBrowserActionsItemId, Xamarin.AndroidX.Browser;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsItemId
AndroidX.Browser.BrowserActions.BrowserActionsIntent.IBrowserActionsItemId;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsItemId
androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsItemId;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsItemId
AndroidX.Browser.BrowserActions.BrowserActionsIntent+IBrowserActionsUrlType, Xamarin.AndroidX.Browser;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsUrlType
AndroidX.Browser.BrowserActions.BrowserActionsIntent.IBrowserActionsUrlType;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsUrlType
androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsUrlType;androidx.browser.browseractions.BrowserActionsIntent$BrowserActionsUrlType
AndroidX.Browser.CustomTabs.CustomTabsClient+CustomTabsCallbackImpl, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsClient_CustomTabsCallbackImpl
AndroidX.Browser.CustomTabs.CustomTabsClient.CustomTabsCallbackImpl;androidx.browser.customtabs.CustomTabsClient_CustomTabsCallbackImpl
androidx.browser.customtabs.CustomTabsClient_CustomTabsCallbackImpl;androidx.browser.customtabs.CustomTabsClient_CustomTabsCallbackImpl
AndroidX.Browser.CustomTabs.CustomTabsServiceConnectionImpl, Xamarin.AndroidX.Browser;crc64396a3fe5f8138e3f.CustomTabsServiceConnectionImpl
AndroidX.Browser.CustomTabs.CustomTabsServiceConnectionImpl;crc64396a3fe5f8138e3f.CustomTabsServiceConnectionImpl
androidx.browser.customtabs.CustomTabsServiceConnectionImpl;crc64396a3fe5f8138e3f.CustomTabsServiceConnectionImpl
AndroidX.Browser.CustomTabs.KeepAliveService, Xamarin.AndroidX.Browser;crc64396a3fe5f8138e3f.KeepAliveService
AndroidX.Browser.CustomTabs.KeepAliveService;crc64396a3fe5f8138e3f.KeepAliveService
androidx.browser.customtabs.KeepAliveService;crc64396a3fe5f8138e3f.KeepAliveService
AndroidX.Browser.CustomTabs.CustomTabsCallback+IActivityLayoutState, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsCallback$ActivityLayoutState
AndroidX.Browser.CustomTabs.CustomTabsCallback.IActivityLayoutState;androidx.browser.customtabs.CustomTabsCallback$ActivityLayoutState
androidx.browser.customtabs.CustomTabsCallback$ActivityLayoutState;androidx.browser.customtabs.CustomTabsCallback$ActivityLayoutState
AndroidX.Browser.CustomTabs.CustomTabsFeatures+ICustomTabsFeature, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsFeatures$CustomTabsFeature
AndroidX.Browser.CustomTabs.CustomTabsFeatures.ICustomTabsFeature;androidx.browser.customtabs.CustomTabsFeatures$CustomTabsFeature
androidx.browser.customtabs.CustomTabsFeatures$CustomTabsFeature;androidx.browser.customtabs.CustomTabsFeatures$CustomTabsFeature
AndroidX.Browser.CustomTabs.CustomTabsIntent+IActivityHeightResizeBehavior, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ActivityHeightResizeBehavior
AndroidX.Browser.CustomTabs.CustomTabsIntent.IActivityHeightResizeBehavior;androidx.browser.customtabs.CustomTabsIntent$ActivityHeightResizeBehavior
androidx.browser.customtabs.CustomTabsIntent$ActivityHeightResizeBehavior;androidx.browser.customtabs.CustomTabsIntent$ActivityHeightResizeBehavior
AndroidX.Browser.CustomTabs.CustomTabsIntent+IActivitySideSheetDecorationType, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetDecorationType
AndroidX.Browser.CustomTabs.CustomTabsIntent.IActivitySideSheetDecorationType;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetDecorationType
androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetDecorationType;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetDecorationType
AndroidX.Browser.CustomTabs.CustomTabsIntent+IActivitySideSheetPosition, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent.IActivitySideSheetPosition;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetPosition
androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetPosition;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent+IActivitySideSheetRoundedCornersPosition, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetRoundedCornersPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent.IActivitySideSheetRoundedCornersPosition;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetRoundedCornersPosition
androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetRoundedCornersPosition;androidx.browser.customtabs.CustomTabsIntent$ActivitySideSheetRoundedCornersPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent+ICloseButtonPosition, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$CloseButtonPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent.ICloseButtonPosition;androidx.browser.customtabs.CustomTabsIntent$CloseButtonPosition
androidx.browser.customtabs.CustomTabsIntent$CloseButtonPosition;androidx.browser.customtabs.CustomTabsIntent$CloseButtonPosition
AndroidX.Browser.CustomTabs.CustomTabsIntent+IColorScheme, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ColorScheme
AndroidX.Browser.CustomTabs.CustomTabsIntent.IColorScheme;androidx.browser.customtabs.CustomTabsIntent$ColorScheme
androidx.browser.customtabs.CustomTabsIntent$ColorScheme;androidx.browser.customtabs.CustomTabsIntent$ColorScheme
AndroidX.Browser.CustomTabs.CustomTabsIntent+IShareState, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsIntent$ShareState
AndroidX.Browser.CustomTabs.CustomTabsIntent.IShareState;androidx.browser.customtabs.CustomTabsIntent$ShareState
androidx.browser.customtabs.CustomTabsIntent$ShareState;androidx.browser.customtabs.CustomTabsIntent$ShareState
AndroidX.Browser.CustomTabs.CustomTabsService+IFilePurpose, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsService$FilePurpose
AndroidX.Browser.CustomTabs.CustomTabsService.IFilePurpose;androidx.browser.customtabs.CustomTabsService$FilePurpose
androidx.browser.customtabs.CustomTabsService$FilePurpose;androidx.browser.customtabs.CustomTabsService$FilePurpose
AndroidX.Browser.CustomTabs.CustomTabsService+IRelation, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsService$Relation
AndroidX.Browser.CustomTabs.CustomTabsService.IRelation;androidx.browser.customtabs.CustomTabsService$Relation
androidx.browser.customtabs.CustomTabsService$Relation;androidx.browser.customtabs.CustomTabsService$Relation
AndroidX.Browser.CustomTabs.CustomTabsService+IResult, Xamarin.AndroidX.Browser;androidx.browser.customtabs.CustomTabsService$Result
AndroidX.Browser.CustomTabs.CustomTabsService.IResult;androidx.browser.customtabs.CustomTabsService$Result
androidx.browser.customtabs.CustomTabsService$Result;androidx.browser.customtabs.CustomTabsService$Result
AndroidX.Browser.CustomTabs.IEngagementSignalsCallback, Xamarin.AndroidX.Browser;androidx.browser.customtabs.EngagementSignalsCallback
AndroidX.Browser.CustomTabs.IEngagementSignalsCallback;androidx.browser.customtabs.EngagementSignalsCallback
androidx.browser.customtabs.EngagementSignalsCallback;androidx.browser.customtabs.EngagementSignalsCallback
AndroidX.Browser.CustomTabs.IExperimentalMinimizationCallback, Xamarin.AndroidX.Browser;androidx.browser.customtabs.ExperimentalMinimizationCallback
AndroidX.Browser.CustomTabs.IExperimentalMinimizationCallback;androidx.browser.customtabs.ExperimentalMinimizationCallback
androidx.browser.customtabs.ExperimentalMinimizationCallback;androidx.browser.customtabs.ExperimentalMinimizationCallback
AndroidX.Browser.CustomTabs.IPostMessageBackend, Xamarin.AndroidX.Browser;androidx.browser.customtabs.PostMessageBackend
AndroidX.Browser.CustomTabs.IPostMessageBackend;androidx.browser.customtabs.PostMessageBackend
androidx.browser.customtabs.PostMessageBackend;androidx.browser.customtabs.PostMessageBackend
AndroidX.Concurrent.Futures.CallbackToFutureAdapter+IResolver, Xamarin.AndroidX.Concurrent.Futures;androidx.concurrent.futures.CallbackToFutureAdapter$Resolver
AndroidX.Concurrent.Futures.CallbackToFutureAdapter.IResolver;androidx.concurrent.futures.CallbackToFutureAdapter$Resolver
androidx.concurrent.futures.CallbackToFutureAdapter$Resolver;androidx.concurrent.futures.CallbackToFutureAdapter$Resolver
AndroidX.ConstraintLayout.Widget.SharedValues+ISharedValuesListener, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.widget.SharedValues$SharedValuesListener
AndroidX.ConstraintLayout.Widget.SharedValues.ISharedValuesListener;androidx.constraintlayout.widget.SharedValues$SharedValuesListener
androidx.constraintlayout.widget.SharedValues$SharedValuesListener;androidx.constraintlayout.widget.SharedValues$SharedValuesListener
AndroidX.ConstraintLayout.Widget.SharedValues+ISharedValuesListenerImplementor, Xamarin.AndroidX.ConstraintLayout;mono.androidx.constraintlayout.widget.SharedValues_SharedValuesListenerImplementor
AndroidX.ConstraintLayout.Widget.SharedValues.ISharedValuesListenerImplementor;mono.androidx.constraintlayout.widget.SharedValues_SharedValuesListenerImplementor
mono.androidx.constraintlayout.widget.SharedValues_SharedValuesListenerImplementor;mono.androidx.constraintlayout.widget.SharedValues_SharedValuesListenerImplementor
AndroidX.ConstraintLayout.Helper.Widget.Carousel+IAdapter, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.helper.widget.Carousel$Adapter
AndroidX.ConstraintLayout.Helper.Widget.Carousel.IAdapter;androidx.constraintlayout.helper.widget.Carousel$Adapter
androidx.constraintlayout.helper.widget.Carousel$Adapter;androidx.constraintlayout.helper.widget.Carousel$Adapter
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout+IMotionTracker, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.MotionLayout$MotionTracker
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout.IMotionTracker;androidx.constraintlayout.motion.widget.MotionLayout$MotionTracker
androidx.constraintlayout.motion.widget.MotionLayout$MotionTracker;androidx.constraintlayout.motion.widget.MotionLayout$MotionTracker
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout+ITransitionListener, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.MotionLayout$TransitionListener
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout.ITransitionListener;androidx.constraintlayout.motion.widget.MotionLayout$TransitionListener
androidx.constraintlayout.motion.widget.MotionLayout$TransitionListener;androidx.constraintlayout.motion.widget.MotionLayout$TransitionListener
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout+ITransitionListenerImplementor, Xamarin.AndroidX.ConstraintLayout;mono.androidx.constraintlayout.motion.widget.MotionLayout_TransitionListenerImplementor
AndroidX.ConstraintLayout.Motion.Widget.MotionLayout.ITransitionListenerImplementor;mono.androidx.constraintlayout.motion.widget.MotionLayout_TransitionListenerImplementor
mono.androidx.constraintlayout.motion.widget.MotionLayout_TransitionListenerImplementor;mono.androidx.constraintlayout.motion.widget.MotionLayout_TransitionListenerImplementor
AndroidX.ConstraintLayout.Motion.Widget.IAnimatable, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.Animatable
AndroidX.ConstraintLayout.Motion.Widget.IAnimatable;androidx.constraintlayout.motion.widget.Animatable
androidx.constraintlayout.motion.widget.Animatable;androidx.constraintlayout.motion.widget.Animatable
AndroidX.ConstraintLayout.Motion.Widget.ICustomFloatAttributes, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.CustomFloatAttributes
AndroidX.ConstraintLayout.Motion.Widget.ICustomFloatAttributes;androidx.constraintlayout.motion.widget.CustomFloatAttributes
androidx.constraintlayout.motion.widget.CustomFloatAttributes;androidx.constraintlayout.motion.widget.CustomFloatAttributes
AndroidX.ConstraintLayout.Motion.Widget.IFloatLayout, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.FloatLayout
AndroidX.ConstraintLayout.Motion.Widget.IFloatLayout;androidx.constraintlayout.motion.widget.FloatLayout
androidx.constraintlayout.motion.widget.FloatLayout;androidx.constraintlayout.motion.widget.FloatLayout
AndroidX.ConstraintLayout.Motion.Widget.IMotionHelperInterface, Xamarin.AndroidX.ConstraintLayout;androidx.constraintlayout.motion.widget.MotionHelperInterface
AndroidX.ConstraintLayout.Motion.Widget.IMotionHelperInterface;androidx.constraintlayout.motion.widget.MotionHelperInterface
androidx.constraintlayout.motion.widget.MotionHelperInterface;androidx.constraintlayout.motion.widget.MotionHelperInterface
AndroidX.ConstraintLayout.Core.ArrayRow+IArrayRowVariables, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.ArrayRow$ArrayRowVariables
AndroidX.ConstraintLayout.Core.ArrayRow.IArrayRowVariables;androidx.constraintlayout.core.ArrayRow$ArrayRowVariables
androidx.constraintlayout.core.ArrayRow$ArrayRowVariables;androidx.constraintlayout.core.ArrayRow$ArrayRowVariables
AndroidX.ConstraintLayout.Core.Widgets.IHelper, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.widgets.Helper
AndroidX.ConstraintLayout.Core.Widgets.IHelper;androidx.constraintlayout.core.widgets.Helper
androidx.constraintlayout.core.widgets.Helper;androidx.constraintlayout.core.widgets.Helper
AndroidX.ConstraintLayout.Core.Widgets.Analyzer.BasicMeasure+IMeasurer, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.widgets.analyzer.BasicMeasure$Measurer
AndroidX.ConstraintLayout.Core.Widgets.Analyzer.BasicMeasure.IMeasurer;androidx.constraintlayout.core.widgets.analyzer.BasicMeasure$Measurer
androidx.constraintlayout.core.widgets.analyzer.BasicMeasure$Measurer;androidx.constraintlayout.core.widgets.analyzer.BasicMeasure$Measurer
AndroidX.ConstraintLayout.Core.Widgets.Analyzer.IDependency, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.widgets.analyzer.Dependency
AndroidX.ConstraintLayout.Core.Widgets.Analyzer.IDependency;androidx.constraintlayout.core.widgets.analyzer.Dependency
androidx.constraintlayout.core.widgets.analyzer.Dependency;androidx.constraintlayout.core.widgets.analyzer.Dependency
AndroidX.ConstraintLayout.Core.State.ConstraintReference+IConstraintReferenceFactory, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.state.ConstraintReference$ConstraintReferenceFactory
AndroidX.ConstraintLayout.Core.State.ConstraintReference.IConstraintReferenceFactory;androidx.constraintlayout.core.state.ConstraintReference$ConstraintReferenceFactory
androidx.constraintlayout.core.state.ConstraintReference$ConstraintReferenceFactory;androidx.constraintlayout.core.state.ConstraintReference$ConstraintReferenceFactory
AndroidX.ConstraintLayout.Core.State.IInterpolator, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.state.Interpolator
AndroidX.ConstraintLayout.Core.State.IInterpolator;androidx.constraintlayout.core.state.Interpolator
androidx.constraintlayout.core.state.Interpolator;androidx.constraintlayout.core.state.Interpolator
AndroidX.ConstraintLayout.Core.State.IReference, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.state.Reference
AndroidX.ConstraintLayout.Core.State.IReference;androidx.constraintlayout.core.state.Reference
androidx.constraintlayout.core.state.Reference;androidx.constraintlayout.core.state.Reference
AndroidX.ConstraintLayout.Core.State.IRegistryCallback, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.state.RegistryCallback
AndroidX.ConstraintLayout.Core.State.IRegistryCallback;androidx.constraintlayout.core.state.RegistryCallback
androidx.constraintlayout.core.state.RegistryCallback;androidx.constraintlayout.core.state.RegistryCallback
AndroidX.ConstraintLayout.Core.State.Helpers.IFacade, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.state.helpers.Facade
AndroidX.ConstraintLayout.Core.State.Helpers.IFacade;androidx.constraintlayout.core.state.helpers.Facade
androidx.constraintlayout.core.state.helpers.Facade;androidx.constraintlayout.core.state.helpers.Facade
AndroidX.ConstraintLayout.Core.Motion.Utils.IDifferentialInterpolator, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.DifferentialInterpolator
AndroidX.ConstraintLayout.Core.Motion.Utils.IDifferentialInterpolator;androidx.constraintlayout.core.motion.utils.DifferentialInterpolator
androidx.constraintlayout.core.motion.utils.DifferentialInterpolator;androidx.constraintlayout.core.motion.utils.DifferentialInterpolator
AndroidX.ConstraintLayout.Core.Motion.Utils.IStopEngine, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.StopEngine
AndroidX.ConstraintLayout.Core.Motion.Utils.IStopEngine;androidx.constraintlayout.core.motion.utils.StopEngine
androidx.constraintlayout.core.motion.utils.StopEngine;androidx.constraintlayout.core.motion.utils.StopEngine
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesAttributesType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$AttributesType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesAttributesType;androidx.constraintlayout.core.motion.utils.TypedValues$AttributesType
androidx.constraintlayout.core.motion.utils.TypedValues$AttributesType;androidx.constraintlayout.core.motion.utils.TypedValues$AttributesType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesCustom, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$Custom
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesCustom;androidx.constraintlayout.core.motion.utils.TypedValues$Custom
androidx.constraintlayout.core.motion.utils.TypedValues$Custom;androidx.constraintlayout.core.motion.utils.TypedValues$Custom
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesCycleType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$CycleType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesCycleType;androidx.constraintlayout.core.motion.utils.TypedValues$CycleType
androidx.constraintlayout.core.motion.utils.TypedValues$CycleType;androidx.constraintlayout.core.motion.utils.TypedValues$CycleType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesMotionScene, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$MotionScene
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesMotionScene;androidx.constraintlayout.core.motion.utils.TypedValues$MotionScene
androidx.constraintlayout.core.motion.utils.TypedValues$MotionScene;androidx.constraintlayout.core.motion.utils.TypedValues$MotionScene
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesMotionType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$MotionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesMotionType;androidx.constraintlayout.core.motion.utils.TypedValues$MotionType
androidx.constraintlayout.core.motion.utils.TypedValues$MotionType;androidx.constraintlayout.core.motion.utils.TypedValues$MotionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesOnSwipe, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$OnSwipe
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesOnSwipe;androidx.constraintlayout.core.motion.utils.TypedValues$OnSwipe
androidx.constraintlayout.core.motion.utils.TypedValues$OnSwipe;androidx.constraintlayout.core.motion.utils.TypedValues$OnSwipe
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesPositionType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$PositionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesPositionType;androidx.constraintlayout.core.motion.utils.TypedValues$PositionType
androidx.constraintlayout.core.motion.utils.TypedValues$PositionType;androidx.constraintlayout.core.motion.utils.TypedValues$PositionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesTransitionType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesTransitionType;androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType
androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType;androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesTriggerType, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues$TriggerType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValuesTriggerType;androidx.constraintlayout.core.motion.utils.TypedValues$TriggerType
androidx.constraintlayout.core.motion.utils.TypedValues$TriggerType;androidx.constraintlayout.core.motion.utils.TypedValues$TriggerType
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValues, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.TypedValues
AndroidX.ConstraintLayout.Core.Motion.Utils.ITypedValues;androidx.constraintlayout.core.motion.utils.TypedValues
androidx.constraintlayout.core.motion.utils.TypedValues;androidx.constraintlayout.core.motion.utils.TypedValues
AndroidX.ConstraintLayout.Core.Motion.Utils.Utils+IDebugHandle, Xamarin.AndroidX.ConstraintLayout.Core;androidx.constraintlayout.core.motion.utils.Utils$DebugHandle
AndroidX.ConstraintLayout.Core.Motion.Utils.Utils.IDebugHandle;androidx.constraintlayout.core.motion.utils.Utils$DebugHandle
androidx.constraintlayout.core.motion.utils.Utils$DebugHandle;androidx.constraintlayout.core.motion.utils.Utils$DebugHandle
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout+IAttachedBehavior, Xamarin.AndroidX.CoordinatorLayout;androidx.coordinatorlayout.widget.CoordinatorLayout$AttachedBehavior
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout.IAttachedBehavior;androidx.coordinatorlayout.widget.CoordinatorLayout$AttachedBehavior
androidx.coordinatorlayout.widget.CoordinatorLayout$AttachedBehavior;androidx.coordinatorlayout.widget.CoordinatorLayout$AttachedBehavior
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout+IDefaultBehavior, Xamarin.AndroidX.CoordinatorLayout;androidx.coordinatorlayout.widget.CoordinatorLayout$DefaultBehavior
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout.IDefaultBehavior;androidx.coordinatorlayout.widget.CoordinatorLayout$DefaultBehavior
androidx.coordinatorlayout.widget.CoordinatorLayout$DefaultBehavior;androidx.coordinatorlayout.widget.CoordinatorLayout$DefaultBehavior
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout+IDispatchChangeEvent, Xamarin.AndroidX.CoordinatorLayout;androidx.coordinatorlayout.widget.CoordinatorLayout$DispatchChangeEvent
AndroidX.CoordinatorLayout.Widget.CoordinatorLayout.IDispatchChangeEvent;androidx.coordinatorlayout.widget.CoordinatorLayout$DispatchChangeEvent
androidx.coordinatorlayout.widget.CoordinatorLayout$DispatchChangeEvent;androidx.coordinatorlayout.widget.CoordinatorLayout$DispatchChangeEvent
Android.Support.V4.OS.IResultReceiver, Xamarin.AndroidX.Core;android.support.v4.os.IResultReceiver
Android.Support.V4.OS.IResultReceiver;android.support.v4.os.IResultReceiver
android.support.v4.os.IResultReceiver;android.support.v4.os.IResultReceiver
Android.Support.V4.OS.IResultReceiver2, Xamarin.AndroidX.Core;android.support.v4.os.IResultReceiver2
Android.Support.V4.OS.IResultReceiver2;android.support.v4.os.IResultReceiver2
android.support.v4.os.IResultReceiver2;android.support.v4.os.IResultReceiver2
Android.Support.V4.App.INotificationSideChannel, Xamarin.AndroidX.Core;android.support.v4.app.INotificationSideChannel
Android.Support.V4.App.INotificationSideChannel;android.support.v4.app.INotificationSideChannel
android.support.v4.app.INotificationSideChannel;android.support.v4.app.INotificationSideChannel
AndroidX.Core.Util.IConsumer, Xamarin.AndroidX.Core;androidx.core.util.Consumer
AndroidX.Core.Util.IConsumer;androidx.core.util.Consumer
androidx.core.util.Consumer;androidx.core.util.Consumer
AndroidX.Core.Util.IFunction, Xamarin.AndroidX.Core;androidx.core.util.Function
AndroidX.Core.Util.IFunction;androidx.core.util.Function
androidx.core.util.Function;androidx.core.util.Function
AndroidX.Core.Util.IPredicate, Xamarin.AndroidX.Core;androidx.core.util.Predicate
AndroidX.Core.Util.IPredicate;androidx.core.util.Predicate
androidx.core.util.Predicate;androidx.core.util.Predicate
AndroidX.Core.Util.ISupplier, Xamarin.AndroidX.Core;androidx.core.util.Supplier
AndroidX.Core.Util.ISupplier;androidx.core.util.Supplier
androidx.core.util.Supplier;androidx.core.util.Supplier
AndroidX.Core.Util.Pools+IPool, Xamarin.AndroidX.Core;androidx.core.util.Pools$Pool
AndroidX.Core.Util.Pools.IPool;androidx.core.util.Pools$Pool
androidx.core.util.Pools$Pool;androidx.core.util.Pools$Pool
AndroidX.Core.Util.TypedValueCompat+IComplexDimensionUnit, Xamarin.AndroidX.Core;androidx.core.util.TypedValueCompat$ComplexDimensionUnit
AndroidX.Core.Util.TypedValueCompat.IComplexDimensionUnit;androidx.core.util.TypedValueCompat$ComplexDimensionUnit
androidx.core.util.TypedValueCompat$ComplexDimensionUnit;androidx.core.util.TypedValueCompat$ComplexDimensionUnit
AndroidX.Core.Provider.FontsContractCompat+FontRequestCallback+IFontRequestFailReason, Xamarin.AndroidX.Core;androidx.core.provider.FontsContractCompat$FontRequestCallback$FontRequestFailReason
AndroidX.Core.Provider.FontsContractCompat.FontRequestCallback.IFontRequestFailReason;androidx.core.provider.FontsContractCompat$FontRequestCallback$FontRequestFailReason
androidx.core.provider.FontsContractCompat$FontRequestCallback$FontRequestFailReason;androidx.core.provider.FontsContractCompat$FontRequestCallback$FontRequestFailReason
AndroidX.Core.Provider.SelfDestructiveThread+IReplyCallback, Xamarin.AndroidX.Core;androidx.core.provider.SelfDestructiveThread$ReplyCallback
AndroidX.Core.Provider.SelfDestructiveThread.IReplyCallback;androidx.core.provider.SelfDestructiveThread$ReplyCallback
androidx.core.provider.SelfDestructiveThread$ReplyCallback;androidx.core.provider.SelfDestructiveThread$ReplyCallback
AndroidX.Core.OS.BuildCompat+IPrereleaseSdkCheck, Xamarin.AndroidX.Core;androidx.core.os.BuildCompat$PrereleaseSdkCheck
AndroidX.Core.OS.BuildCompat.IPrereleaseSdkCheck;androidx.core.os.BuildCompat$PrereleaseSdkCheck
androidx.core.os.BuildCompat$PrereleaseSdkCheck;androidx.core.os.BuildCompat$PrereleaseSdkCheck
AndroidX.Core.OS.CancellationSignal+IOnCancelListener, Xamarin.AndroidX.Core;androidx.core.os.CancellationSignal$OnCancelListener
AndroidX.Core.OS.CancellationSignal.IOnCancelListener;androidx.core.os.CancellationSignal$OnCancelListener
androidx.core.os.CancellationSignal$OnCancelListener;androidx.core.os.CancellationSignal$OnCancelListener
AndroidX.Core.OS.CancellationSignal+IOnCancelListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.os.CancellationSignal_OnCancelListenerImplementor
AndroidX.Core.OS.CancellationSignal.IOnCancelListenerImplementor;mono.androidx.core.os.CancellationSignal_OnCancelListenerImplementor
mono.androidx.core.os.CancellationSignal_OnCancelListenerImplementor;mono.androidx.core.os.CancellationSignal_OnCancelListenerImplementor
AndroidX.Core.OS.IParcelableCompatCreatorCallbacks, Xamarin.AndroidX.Core;androidx.core.os.ParcelableCompatCreatorCallbacks
AndroidX.Core.OS.IParcelableCompatCreatorCallbacks;androidx.core.os.ParcelableCompatCreatorCallbacks
androidx.core.os.ParcelableCompatCreatorCallbacks;androidx.core.os.ParcelableCompatCreatorCallbacks
AndroidX.Core.Net.ConnectivityManagerCompat+IRestrictBackgroundStatus, Xamarin.AndroidX.Core;androidx.core.net.ConnectivityManagerCompat$RestrictBackgroundStatus
AndroidX.Core.Net.ConnectivityManagerCompat.IRestrictBackgroundStatus;androidx.core.net.ConnectivityManagerCompat$RestrictBackgroundStatus
androidx.core.net.ConnectivityManagerCompat$RestrictBackgroundStatus;androidx.core.net.ConnectivityManagerCompat$RestrictBackgroundStatus
AndroidX.Core.Location.GnssStatusCompat+IConstellationType, Xamarin.AndroidX.Core;androidx.core.location.GnssStatusCompat$ConstellationType
AndroidX.Core.Location.GnssStatusCompat.IConstellationType;androidx.core.location.GnssStatusCompat$ConstellationType
androidx.core.location.GnssStatusCompat$ConstellationType;androidx.core.location.GnssStatusCompat$ConstellationType
AndroidX.Core.Location.ILocationListenerCompat, Xamarin.AndroidX.Core;androidx.core.location.LocationListenerCompat
AndroidX.Core.Location.ILocationListenerCompat;androidx.core.location.LocationListenerCompat
androidx.core.location.LocationListenerCompat;androidx.core.location.LocationListenerCompat
AndroidX.Core.Location.LocationRequestCompat+IQuality, Xamarin.AndroidX.Core;androidx.core.location.LocationRequestCompat$Quality
AndroidX.Core.Location.LocationRequestCompat.IQuality;androidx.core.location.LocationRequestCompat$Quality
androidx.core.location.LocationRequestCompat$Quality;androidx.core.location.LocationRequestCompat$Quality
AndroidX.Core.Internal.View.ISupportMenu, Xamarin.AndroidX.Core;androidx.core.internal.view.SupportMenu
AndroidX.Core.Internal.View.ISupportMenu;androidx.core.internal.view.SupportMenu
androidx.core.internal.view.SupportMenu;androidx.core.internal.view.SupportMenu
AndroidX.Core.Internal.View.ISupportMenuItem, Xamarin.AndroidX.Core;androidx.core.internal.view.SupportMenuItem
AndroidX.Core.Internal.View.ISupportMenuItem;androidx.core.internal.view.SupportMenuItem
androidx.core.internal.view.SupportMenuItem;androidx.core.internal.view.SupportMenuItem
AndroidX.Core.Internal.View.ISupportSubMenu, Xamarin.AndroidX.Core;androidx.core.internal.view.SupportSubMenu
AndroidX.Core.Internal.View.ISupportSubMenu;androidx.core.internal.view.SupportSubMenu
androidx.core.internal.view.SupportSubMenu;androidx.core.internal.view.SupportSubMenu
AndroidX.Core.Graphics.Drawable.IconCompat+IIconType, Xamarin.AndroidX.Core;androidx.core.graphics.drawable.IconCompat$IconType
AndroidX.Core.Graphics.Drawable.IconCompat.IIconType;androidx.core.graphics.drawable.IconCompat$IconType
androidx.core.graphics.drawable.IconCompat$IconType;androidx.core.graphics.drawable.IconCompat$IconType
AndroidX.Core.Graphics.Drawable.ITintAwareDrawable, Xamarin.AndroidX.Core;androidx.core.graphics.drawable.TintAwareDrawable
AndroidX.Core.Graphics.Drawable.ITintAwareDrawable;androidx.core.graphics.drawable.TintAwareDrawable
androidx.core.graphics.drawable.TintAwareDrawable;androidx.core.graphics.drawable.TintAwareDrawable
AndroidX.Core.Graphics.Drawable.IWrappedDrawable, Xamarin.AndroidX.Core;androidx.core.graphics.drawable.WrappedDrawable
AndroidX.Core.Graphics.Drawable.IWrappedDrawable;androidx.core.graphics.drawable.WrappedDrawable
androidx.core.graphics.drawable.WrappedDrawable;androidx.core.graphics.drawable.WrappedDrawable
AndroidX.Core.Content.ContextCompat+IRegisterReceiverFlags, Xamarin.AndroidX.Core;androidx.core.content.ContextCompat$RegisterReceiverFlags
AndroidX.Core.Content.ContextCompat.IRegisterReceiverFlags;androidx.core.content.ContextCompat$RegisterReceiverFlags
androidx.core.content.ContextCompat$RegisterReceiverFlags;androidx.core.content.ContextCompat$RegisterReceiverFlags
AndroidX.Core.Content.IOnConfigurationChangedProvider, Xamarin.AndroidX.Core;androidx.core.content.OnConfigurationChangedProvider
AndroidX.Core.Content.IOnConfigurationChangedProvider;androidx.core.content.OnConfigurationChangedProvider
androidx.core.content.OnConfigurationChangedProvider;androidx.core.content.OnConfigurationChangedProvider
AndroidX.Core.Content.IOnTrimMemoryProvider, Xamarin.AndroidX.Core;androidx.core.content.OnTrimMemoryProvider
AndroidX.Core.Content.IOnTrimMemoryProvider;androidx.core.content.OnTrimMemoryProvider
androidx.core.content.OnTrimMemoryProvider;androidx.core.content.OnTrimMemoryProvider
AndroidX.Core.Content.PackageManagerCompat+IUnusedAppRestrictionsStatus, Xamarin.AndroidX.Core;androidx.core.content.PackageManagerCompat$UnusedAppRestrictionsStatus
AndroidX.Core.Content.PackageManagerCompat.IUnusedAppRestrictionsStatus;androidx.core.content.PackageManagerCompat$UnusedAppRestrictionsStatus
androidx.core.content.PackageManagerCompat$UnusedAppRestrictionsStatus;androidx.core.content.PackageManagerCompat$UnusedAppRestrictionsStatus
AndroidX.Core.Content.PermissionChecker+IPermissionResult, Xamarin.AndroidX.Core;androidx.core.content.PermissionChecker$PermissionResult
AndroidX.Core.Content.PermissionChecker.IPermissionResult;androidx.core.content.PermissionChecker$PermissionResult
androidx.core.content.PermissionChecker$PermissionResult;androidx.core.content.PermissionChecker$PermissionResult
AndroidX.Core.Content.Resources.FontResourcesParserCompat+IFamilyResourceEntry, Xamarin.AndroidX.Core;androidx.core.content.res.FontResourcesParserCompat$FamilyResourceEntry
AndroidX.Core.Content.Resources.FontResourcesParserCompat.IFamilyResourceEntry;androidx.core.content.res.FontResourcesParserCompat$FamilyResourceEntry
androidx.core.content.res.FontResourcesParserCompat$FamilyResourceEntry;androidx.core.content.res.FontResourcesParserCompat$FamilyResourceEntry
AndroidX.Core.Content.Resources.FontResourcesParserCompat+IFetchStrategy, Xamarin.AndroidX.Core;androidx.core.content.res.FontResourcesParserCompat$FetchStrategy
AndroidX.Core.Content.Resources.FontResourcesParserCompat.IFetchStrategy;androidx.core.content.res.FontResourcesParserCompat$FetchStrategy
androidx.core.content.res.FontResourcesParserCompat$FetchStrategy;androidx.core.content.res.FontResourcesParserCompat$FetchStrategy
AndroidX.Core.Content.PM.PermissionInfoCompat+IProtection, Xamarin.AndroidX.Core;androidx.core.content.pm.PermissionInfoCompat$Protection
AndroidX.Core.Content.PM.PermissionInfoCompat.IProtection;androidx.core.content.pm.PermissionInfoCompat$Protection
androidx.core.content.pm.PermissionInfoCompat$Protection;androidx.core.content.pm.PermissionInfoCompat$Protection
AndroidX.Core.Content.PM.PermissionInfoCompat+IProtectionFlags, Xamarin.AndroidX.Core;androidx.core.content.pm.PermissionInfoCompat$ProtectionFlags
AndroidX.Core.Content.PM.PermissionInfoCompat.IProtectionFlags;androidx.core.content.pm.PermissionInfoCompat$ProtectionFlags
androidx.core.content.pm.PermissionInfoCompat$ProtectionFlags;androidx.core.content.pm.PermissionInfoCompat$ProtectionFlags
AndroidX.Core.Content.PM.ShortcutInfoCompat+ISurface, Xamarin.AndroidX.Core;androidx.core.content.pm.ShortcutInfoCompat$Surface
AndroidX.Core.Content.PM.ShortcutInfoCompat.ISurface;androidx.core.content.pm.ShortcutInfoCompat$Surface
androidx.core.content.pm.ShortcutInfoCompat$Surface;androidx.core.content.pm.ShortcutInfoCompat$Surface
AndroidX.Core.Content.PM.ShortcutManagerCompat+IShortcutMatchFlags, Xamarin.AndroidX.Core;androidx.core.content.pm.ShortcutManagerCompat$ShortcutMatchFlags
AndroidX.Core.Content.PM.ShortcutManagerCompat.IShortcutMatchFlags;androidx.core.content.pm.ShortcutManagerCompat$ShortcutMatchFlags
androidx.core.content.pm.ShortcutManagerCompat$ShortcutMatchFlags;androidx.core.content.pm.ShortcutManagerCompat$ShortcutMatchFlags
AndroidX.Core.App.ActivityCompat+IOnRequestPermissionsResultCallback, Xamarin.AndroidX.Core;androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback
AndroidX.Core.App.ActivityCompat.IOnRequestPermissionsResultCallback;androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback
androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback;androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback
AndroidX.Core.App.ActivityCompat+IPermissionCompatDelegate, Xamarin.AndroidX.Core;androidx.core.app.ActivityCompat$PermissionCompatDelegate
AndroidX.Core.App.ActivityCompat.IPermissionCompatDelegate;androidx.core.app.ActivityCompat$PermissionCompatDelegate
androidx.core.app.ActivityCompat$PermissionCompatDelegate;androidx.core.app.ActivityCompat$PermissionCompatDelegate
AndroidX.Core.App.ActivityCompat+IRequestPermissionsRequestCodeValidator, Xamarin.AndroidX.Core;androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator
AndroidX.Core.App.ActivityCompat.IRequestPermissionsRequestCodeValidator;androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator
androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator;androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator
AndroidX.Core.App.CoreComponentFactory+ICompatWrapped, Xamarin.AndroidX.Core;androidx.core.app.CoreComponentFactory$CompatWrapped
AndroidX.Core.App.CoreComponentFactory.ICompatWrapped;androidx.core.app.CoreComponentFactory$CompatWrapped
androidx.core.app.CoreComponentFactory$CompatWrapped;androidx.core.app.CoreComponentFactory$CompatWrapped
AndroidX.Core.App.FrameMetricsAggregator+IMetricType, Xamarin.AndroidX.Core;androidx.core.app.FrameMetricsAggregator$MetricType
AndroidX.Core.App.FrameMetricsAggregator.IMetricType;androidx.core.app.FrameMetricsAggregator$MetricType
androidx.core.app.FrameMetricsAggregator$MetricType;androidx.core.app.FrameMetricsAggregator$MetricType
AndroidX.Core.App.GrammaticalInflectionManagerCompat+IGrammaticalGender, Xamarin.AndroidX.Core;androidx.core.app.GrammaticalInflectionManagerCompat$GrammaticalGender
AndroidX.Core.App.GrammaticalInflectionManagerCompat.IGrammaticalGender;androidx.core.app.GrammaticalInflectionManagerCompat$GrammaticalGender
androidx.core.app.GrammaticalInflectionManagerCompat$GrammaticalGender;androidx.core.app.GrammaticalInflectionManagerCompat$GrammaticalGender
AndroidX.Core.App.INotificationBuilderWithBuilderAccessor, Xamarin.AndroidX.Core;androidx.core.app.NotificationBuilderWithBuilderAccessor
AndroidX.Core.App.INotificationBuilderWithBuilderAccessor;androidx.core.app.NotificationBuilderWithBuilderAccessor
androidx.core.app.NotificationBuilderWithBuilderAccessor;androidx.core.app.NotificationBuilderWithBuilderAccessor
AndroidX.Core.App.IOnMultiWindowModeChangedProvider, Xamarin.AndroidX.Core;androidx.core.app.OnMultiWindowModeChangedProvider
AndroidX.Core.App.IOnMultiWindowModeChangedProvider;androidx.core.app.OnMultiWindowModeChangedProvider
androidx.core.app.OnMultiWindowModeChangedProvider;androidx.core.app.OnMultiWindowModeChangedProvider
AndroidX.Core.App.IOnNewIntentProvider, Xamarin.AndroidX.Core;androidx.core.app.OnNewIntentProvider
AndroidX.Core.App.IOnNewIntentProvider;androidx.core.app.OnNewIntentProvider
androidx.core.app.OnNewIntentProvider;androidx.core.app.OnNewIntentProvider
AndroidX.Core.App.IOnPictureInPictureModeChangedProvider, Xamarin.AndroidX.Core;androidx.core.app.OnPictureInPictureModeChangedProvider
AndroidX.Core.App.IOnPictureInPictureModeChangedProvider;androidx.core.app.OnPictureInPictureModeChangedProvider
androidx.core.app.OnPictureInPictureModeChangedProvider;androidx.core.app.OnPictureInPictureModeChangedProvider
AndroidX.Core.App.IOnUserLeaveHintProvider, Xamarin.AndroidX.Core;androidx.core.app.OnUserLeaveHintProvider
AndroidX.Core.App.IOnUserLeaveHintProvider;androidx.core.app.OnUserLeaveHintProvider
androidx.core.app.OnUserLeaveHintProvider;androidx.core.app.OnUserLeaveHintProvider
AndroidX.Core.App.NotificationCompat+Action+IExtender, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$Action$Extender
AndroidX.Core.App.NotificationCompat.Action.IExtender;androidx.core.app.NotificationCompat$Action$Extender
androidx.core.app.NotificationCompat$Action$Extender;androidx.core.app.NotificationCompat$Action$Extender
AndroidX.Core.App.NotificationCompat+Action+ISemanticAction, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$Action$SemanticAction
AndroidX.Core.App.NotificationCompat.Action.ISemanticAction;androidx.core.app.NotificationCompat$Action$SemanticAction
androidx.core.app.NotificationCompat$Action$SemanticAction;androidx.core.app.NotificationCompat$Action$SemanticAction
AndroidX.Core.App.NotificationCompat+IBadgeIconType, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$BadgeIconType
AndroidX.Core.App.NotificationCompat.IBadgeIconType;androidx.core.app.NotificationCompat$BadgeIconType
androidx.core.app.NotificationCompat$BadgeIconType;androidx.core.app.NotificationCompat$BadgeIconType
AndroidX.Core.App.NotificationCompat+CallStyle+ICallType, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$CallStyle$CallType
AndroidX.Core.App.NotificationCompat.CallStyle.ICallType;androidx.core.app.NotificationCompat$CallStyle$CallType
androidx.core.app.NotificationCompat$CallStyle$CallType;androidx.core.app.NotificationCompat$CallStyle$CallType
AndroidX.Core.App.NotificationCompat+IExtender, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$Extender
AndroidX.Core.App.NotificationCompat.IExtender;androidx.core.app.NotificationCompat$Extender
androidx.core.app.NotificationCompat$Extender;androidx.core.app.NotificationCompat$Extender
AndroidX.Core.App.NotificationCompat+IGroupAlertBehavior, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$GroupAlertBehavior
AndroidX.Core.App.NotificationCompat.IGroupAlertBehavior;androidx.core.app.NotificationCompat$GroupAlertBehavior
androidx.core.app.NotificationCompat$GroupAlertBehavior;androidx.core.app.NotificationCompat$GroupAlertBehavior
AndroidX.Core.App.NotificationCompat+INotificationVisibility, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$NotificationVisibility
AndroidX.Core.App.NotificationCompat.INotificationVisibility;androidx.core.app.NotificationCompat$NotificationVisibility
androidx.core.app.NotificationCompat$NotificationVisibility;androidx.core.app.NotificationCompat$NotificationVisibility
AndroidX.Core.App.NotificationCompat+IServiceNotificationBehavior, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$ServiceNotificationBehavior
AndroidX.Core.App.NotificationCompat.IServiceNotificationBehavior;androidx.core.app.NotificationCompat$ServiceNotificationBehavior
androidx.core.app.NotificationCompat$ServiceNotificationBehavior;androidx.core.app.NotificationCompat$ServiceNotificationBehavior
AndroidX.Core.App.NotificationCompat+IStreamType, Xamarin.AndroidX.Core;androidx.core.app.NotificationCompat$StreamType
AndroidX.Core.App.NotificationCompat.IStreamType;androidx.core.app.NotificationCompat$StreamType
androidx.core.app.NotificationCompat$StreamType;androidx.core.app.NotificationCompat$StreamType
AndroidX.Core.App.NotificationManagerCompat+IInterruptionFilter, Xamarin.AndroidX.Core;androidx.core.app.NotificationManagerCompat$InterruptionFilter
AndroidX.Core.App.NotificationManagerCompat.IInterruptionFilter;androidx.core.app.NotificationManagerCompat$InterruptionFilter
androidx.core.app.NotificationManagerCompat$InterruptionFilter;androidx.core.app.NotificationManagerCompat$InterruptionFilter
AndroidX.Core.App.PendingIntentCompat+IFlags, Xamarin.AndroidX.Core;androidx.core.app.PendingIntentCompat$Flags
AndroidX.Core.App.PendingIntentCompat.IFlags;androidx.core.app.PendingIntentCompat$Flags
androidx.core.app.PendingIntentCompat$Flags;androidx.core.app.PendingIntentCompat$Flags
AndroidX.Core.App.RemoteInput+IEditChoicesBeforeSending, Xamarin.AndroidX.Core;androidx.core.app.RemoteInput$EditChoicesBeforeSending
AndroidX.Core.App.RemoteInput.IEditChoicesBeforeSending;androidx.core.app.RemoteInput$EditChoicesBeforeSending
androidx.core.app.RemoteInput$EditChoicesBeforeSending;androidx.core.app.RemoteInput$EditChoicesBeforeSending
AndroidX.Core.App.RemoteInput+ISource, Xamarin.AndroidX.Core;androidx.core.app.RemoteInput$Source
AndroidX.Core.App.RemoteInput.ISource;androidx.core.app.RemoteInput$Source
androidx.core.app.RemoteInput$Source;androidx.core.app.RemoteInput$Source
AndroidX.Core.App.ServiceCompat+IStopForegroundFlags, Xamarin.AndroidX.Core;androidx.core.app.ServiceCompat$StopForegroundFlags
AndroidX.Core.App.ServiceCompat.IStopForegroundFlags;androidx.core.app.ServiceCompat$StopForegroundFlags
androidx.core.app.ServiceCompat$StopForegroundFlags;androidx.core.app.ServiceCompat$StopForegroundFlags
AndroidX.Core.App.SharedElementCallback+IOnSharedElementsReadyListener, Xamarin.AndroidX.Core;androidx.core.app.SharedElementCallback$OnSharedElementsReadyListener
AndroidX.Core.App.SharedElementCallback.IOnSharedElementsReadyListener;androidx.core.app.SharedElementCallback$OnSharedElementsReadyListener
androidx.core.app.SharedElementCallback$OnSharedElementsReadyListener;androidx.core.app.SharedElementCallback$OnSharedElementsReadyListener
AndroidX.Core.App.SharedElementCallback+IOnSharedElementsReadyListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.app.SharedElementCallback_OnSharedElementsReadyListenerImplementor
AndroidX.Core.App.SharedElementCallback.IOnSharedElementsReadyListenerImplementor;mono.androidx.core.app.SharedElementCallback_OnSharedElementsReadyListenerImplementor
mono.androidx.core.app.SharedElementCallback_OnSharedElementsReadyListenerImplementor;mono.androidx.core.app.SharedElementCallback_OnSharedElementsReadyListenerImplementor
AndroidX.Core.App.TaskStackBuilder+ISupportParentable, Xamarin.AndroidX.Core;androidx.core.app.TaskStackBuilder$SupportParentable
AndroidX.Core.App.TaskStackBuilder.ISupportParentable;androidx.core.app.TaskStackBuilder$SupportParentable
androidx.core.app.TaskStackBuilder$SupportParentable;androidx.core.app.TaskStackBuilder$SupportParentable
AndroidX.Core.App.UnusedAppRestrictions.IUnusedAppRestrictionsBackportCallback, Xamarin.AndroidX.Core;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback
AndroidX.Core.App.UnusedAppRestrictions.IUnusedAppRestrictionsBackportCallback;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback
AndroidX.Core.App.UnusedAppRestrictions.IUnusedAppRestrictionsBackportService, Xamarin.AndroidX.Core;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService
AndroidX.Core.App.UnusedAppRestrictions.IUnusedAppRestrictionsBackportService;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService;androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService
AndroidX.Core.Widget.NestedScrollView+IOnScrollChangeListener, Xamarin.AndroidX.Core;androidx.core.widget.NestedScrollView$OnScrollChangeListener
AndroidX.Core.Widget.NestedScrollView.IOnScrollChangeListener;androidx.core.widget.NestedScrollView$OnScrollChangeListener
androidx.core.widget.NestedScrollView$OnScrollChangeListener;androidx.core.widget.NestedScrollView$OnScrollChangeListener
AndroidX.Core.Widget.NestedScrollView+IOnScrollChangeListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.widget.NestedScrollView_OnScrollChangeListenerImplementor
AndroidX.Core.Widget.NestedScrollView.IOnScrollChangeListenerImplementor;mono.androidx.core.widget.NestedScrollView_OnScrollChangeListenerImplementor
mono.androidx.core.widget.NestedScrollView_OnScrollChangeListenerImplementor;mono.androidx.core.widget.NestedScrollView_OnScrollChangeListenerImplementor
AndroidX.Core.Widget.IAutoSizeableTextView, Xamarin.AndroidX.Core;androidx.core.widget.AutoSizeableTextView
AndroidX.Core.Widget.IAutoSizeableTextView;androidx.core.widget.AutoSizeableTextView
androidx.core.widget.AutoSizeableTextView;androidx.core.widget.AutoSizeableTextView
AndroidX.Core.Widget.ITintableCheckedTextView, Xamarin.AndroidX.Core;androidx.core.widget.TintableCheckedTextView
AndroidX.Core.Widget.ITintableCheckedTextView;androidx.core.widget.TintableCheckedTextView
androidx.core.widget.TintableCheckedTextView;androidx.core.widget.TintableCheckedTextView
AndroidX.Core.Widget.ITintableCompoundButton, Xamarin.AndroidX.Core;androidx.core.widget.TintableCompoundButton
AndroidX.Core.Widget.ITintableCompoundButton;androidx.core.widget.TintableCompoundButton
androidx.core.widget.TintableCompoundButton;androidx.core.widget.TintableCompoundButton
AndroidX.Core.Widget.ITintableCompoundDrawablesView, Xamarin.AndroidX.Core;androidx.core.widget.TintableCompoundDrawablesView
AndroidX.Core.Widget.ITintableCompoundDrawablesView;androidx.core.widget.TintableCompoundDrawablesView
androidx.core.widget.TintableCompoundDrawablesView;androidx.core.widget.TintableCompoundDrawablesView
AndroidX.Core.Widget.ITintableImageSourceView, Xamarin.AndroidX.Core;androidx.core.widget.TintableImageSourceView
AndroidX.Core.Widget.ITintableImageSourceView;androidx.core.widget.TintableImageSourceView
androidx.core.widget.TintableImageSourceView;androidx.core.widget.TintableImageSourceView
AndroidX.Core.Widget.TextViewCompat+IAutoSizeTextType, Xamarin.AndroidX.Core;androidx.core.widget.TextViewCompat$AutoSizeTextType
AndroidX.Core.Widget.TextViewCompat.IAutoSizeTextType;androidx.core.widget.TextViewCompat$AutoSizeTextType
androidx.core.widget.TextViewCompat$AutoSizeTextType;androidx.core.widget.TextViewCompat$AutoSizeTextType
AndroidX.Core.View.INestedScrollingParent2, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingParent2
AndroidX.Core.View.INestedScrollingParent2;androidx.core.view.NestedScrollingParent2
androidx.core.view.NestedScrollingParent2;androidx.core.view.NestedScrollingParent2
AndroidX.Core.View.ActionProvider+ISubUiVisibilityListener, Xamarin.AndroidX.Core;androidx.core.view.ActionProvider$SubUiVisibilityListener
AndroidX.Core.View.ActionProvider.ISubUiVisibilityListener;androidx.core.view.ActionProvider$SubUiVisibilityListener
androidx.core.view.ActionProvider$SubUiVisibilityListener;androidx.core.view.ActionProvider$SubUiVisibilityListener
AndroidX.Core.View.ActionProvider+ISubUiVisibilityListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.ActionProvider_SubUiVisibilityListenerImplementor
AndroidX.Core.View.ActionProvider.ISubUiVisibilityListenerImplementor;mono.androidx.core.view.ActionProvider_SubUiVisibilityListenerImplementor
mono.androidx.core.view.ActionProvider_SubUiVisibilityListenerImplementor;mono.androidx.core.view.ActionProvider_SubUiVisibilityListenerImplementor
AndroidX.Core.View.ActionProvider+IVisibilityListener, Xamarin.AndroidX.Core;androidx.core.view.ActionProvider$VisibilityListener
AndroidX.Core.View.ActionProvider.IVisibilityListener;androidx.core.view.ActionProvider$VisibilityListener
androidx.core.view.ActionProvider$VisibilityListener;androidx.core.view.ActionProvider$VisibilityListener
AndroidX.Core.View.ActionProvider+IVisibilityListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.ActionProvider_VisibilityListenerImplementor
AndroidX.Core.View.ActionProvider.IVisibilityListenerImplementor;mono.androidx.core.view.ActionProvider_VisibilityListenerImplementor
mono.androidx.core.view.ActionProvider_VisibilityListenerImplementor;mono.androidx.core.view.ActionProvider_VisibilityListenerImplementor
AndroidX.Core.View.ContentInfoCompat+IFlags, Xamarin.AndroidX.Core;androidx.core.view.ContentInfoCompat$Flags
AndroidX.Core.View.ContentInfoCompat.IFlags;androidx.core.view.ContentInfoCompat$Flags
androidx.core.view.ContentInfoCompat$Flags;androidx.core.view.ContentInfoCompat$Flags
AndroidX.Core.View.ContentInfoCompat+ISource, Xamarin.AndroidX.Core;androidx.core.view.ContentInfoCompat$Source
AndroidX.Core.View.ContentInfoCompat.ISource;androidx.core.view.ContentInfoCompat$Source
androidx.core.view.ContentInfoCompat$Source;androidx.core.view.ContentInfoCompat$Source
AndroidX.Core.View.DragStartHelper+IOnDragStartListener, Xamarin.AndroidX.Core;androidx.core.view.DragStartHelper$OnDragStartListener
AndroidX.Core.View.DragStartHelper.IOnDragStartListener;androidx.core.view.DragStartHelper$OnDragStartListener
androidx.core.view.DragStartHelper$OnDragStartListener;androidx.core.view.DragStartHelper$OnDragStartListener
AndroidX.Core.View.DragStartHelper+IOnDragStartListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.DragStartHelper_OnDragStartListenerImplementor
AndroidX.Core.View.DragStartHelper.IOnDragStartListenerImplementor;mono.androidx.core.view.DragStartHelper_OnDragStartListenerImplementor
mono.androidx.core.view.DragStartHelper_OnDragStartListenerImplementor;mono.androidx.core.view.DragStartHelper_OnDragStartListenerImplementor
AndroidX.Core.View.HapticFeedbackConstantsCompat+IHapticFeedbackFlags, Xamarin.AndroidX.Core;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackFlags
AndroidX.Core.View.HapticFeedbackConstantsCompat.IHapticFeedbackFlags;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackFlags
androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackFlags;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackFlags
AndroidX.Core.View.HapticFeedbackConstantsCompat+IHapticFeedbackType, Xamarin.AndroidX.Core;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackType
AndroidX.Core.View.HapticFeedbackConstantsCompat.IHapticFeedbackType;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackType
androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackType;androidx.core.view.HapticFeedbackConstantsCompat$HapticFeedbackType
AndroidX.Core.View.IDifferentialMotionFlingTarget, Xamarin.AndroidX.Core;androidx.core.view.DifferentialMotionFlingTarget
AndroidX.Core.View.IDifferentialMotionFlingTarget;androidx.core.view.DifferentialMotionFlingTarget
androidx.core.view.DifferentialMotionFlingTarget;androidx.core.view.DifferentialMotionFlingTarget
AndroidX.Core.View.ILayoutInflaterFactory, Xamarin.AndroidX.Core;androidx.core.view.LayoutInflaterFactory
AndroidX.Core.View.ILayoutInflaterFactory;androidx.core.view.LayoutInflaterFactory
androidx.core.view.LayoutInflaterFactory;androidx.core.view.LayoutInflaterFactory
AndroidX.Core.View.IMenuHost, Xamarin.AndroidX.Core;androidx.core.view.MenuHost
AndroidX.Core.View.IMenuHost;androidx.core.view.MenuHost
androidx.core.view.MenuHost;androidx.core.view.MenuHost
AndroidX.Core.View.IMenuProvider, Xamarin.AndroidX.Core;androidx.core.view.MenuProvider
AndroidX.Core.View.IMenuProvider;androidx.core.view.MenuProvider
androidx.core.view.MenuProvider;androidx.core.view.MenuProvider
AndroidX.Core.View.INestedScrollingChild, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingChild
AndroidX.Core.View.INestedScrollingChild;androidx.core.view.NestedScrollingChild
androidx.core.view.NestedScrollingChild;androidx.core.view.NestedScrollingChild
AndroidX.Core.View.INestedScrollingChild2, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingChild2
AndroidX.Core.View.INestedScrollingChild2;androidx.core.view.NestedScrollingChild2
androidx.core.view.NestedScrollingChild2;androidx.core.view.NestedScrollingChild2
AndroidX.Core.View.INestedScrollingChild3, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingChild3
AndroidX.Core.View.INestedScrollingChild3;androidx.core.view.NestedScrollingChild3
androidx.core.view.NestedScrollingChild3;androidx.core.view.NestedScrollingChild3
AndroidX.Core.View.INestedScrollingParent, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingParent
AndroidX.Core.View.INestedScrollingParent;androidx.core.view.NestedScrollingParent
androidx.core.view.NestedScrollingParent;androidx.core.view.NestedScrollingParent
AndroidX.Core.View.INestedScrollingParent3, Xamarin.AndroidX.Core;androidx.core.view.NestedScrollingParent3
AndroidX.Core.View.INestedScrollingParent3;androidx.core.view.NestedScrollingParent3
androidx.core.view.NestedScrollingParent3;androidx.core.view.NestedScrollingParent3
AndroidX.Core.View.IOnApplyWindowInsetsListener, Xamarin.AndroidX.Core;androidx.core.view.OnApplyWindowInsetsListener
AndroidX.Core.View.IOnApplyWindowInsetsListener;androidx.core.view.OnApplyWindowInsetsListener
androidx.core.view.OnApplyWindowInsetsListener;androidx.core.view.OnApplyWindowInsetsListener
AndroidX.Core.View.IOnApplyWindowInsetsListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.OnApplyWindowInsetsListenerImplementor
AndroidX.Core.View.IOnApplyWindowInsetsListenerImplementor;mono.androidx.core.view.OnApplyWindowInsetsListenerImplementor
mono.androidx.core.view.OnApplyWindowInsetsListenerImplementor;mono.androidx.core.view.OnApplyWindowInsetsListenerImplementor
AndroidX.Core.View.IOnReceiveContentListener, Xamarin.AndroidX.Core;androidx.core.view.OnReceiveContentListener
AndroidX.Core.View.IOnReceiveContentListener;androidx.core.view.OnReceiveContentListener
androidx.core.view.OnReceiveContentListener;androidx.core.view.OnReceiveContentListener
AndroidX.Core.View.IOnReceiveContentListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.OnReceiveContentListenerImplementor
AndroidX.Core.View.IOnReceiveContentListenerImplementor;mono.androidx.core.view.OnReceiveContentListenerImplementor
mono.androidx.core.view.OnReceiveContentListenerImplementor;mono.androidx.core.view.OnReceiveContentListenerImplementor
AndroidX.Core.View.IOnReceiveContentViewBehavior, Xamarin.AndroidX.Core;androidx.core.view.OnReceiveContentViewBehavior
AndroidX.Core.View.IOnReceiveContentViewBehavior;androidx.core.view.OnReceiveContentViewBehavior
androidx.core.view.OnReceiveContentViewBehavior;androidx.core.view.OnReceiveContentViewBehavior
AndroidX.Core.View.IScrollingView, Xamarin.AndroidX.Core;androidx.core.view.ScrollingView
AndroidX.Core.View.IScrollingView;androidx.core.view.ScrollingView
androidx.core.view.ScrollingView;androidx.core.view.ScrollingView
AndroidX.Core.View.ITintableBackgroundView, Xamarin.AndroidX.Core;androidx.core.view.TintableBackgroundView
AndroidX.Core.View.ITintableBackgroundView;androidx.core.view.TintableBackgroundView
androidx.core.view.TintableBackgroundView;androidx.core.view.TintableBackgroundView
AndroidX.Core.View.IViewPropertyAnimatorListener, Xamarin.AndroidX.Core;androidx.core.view.ViewPropertyAnimatorListener
AndroidX.Core.View.IViewPropertyAnimatorListener;androidx.core.view.ViewPropertyAnimatorListener
androidx.core.view.ViewPropertyAnimatorListener;androidx.core.view.ViewPropertyAnimatorListener
AndroidX.Core.View.IViewPropertyAnimatorListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.ViewPropertyAnimatorListenerImplementor
AndroidX.Core.View.IViewPropertyAnimatorListenerImplementor;mono.androidx.core.view.ViewPropertyAnimatorListenerImplementor
mono.androidx.core.view.ViewPropertyAnimatorListenerImplementor;mono.androidx.core.view.ViewPropertyAnimatorListenerImplementor
AndroidX.Core.View.IViewPropertyAnimatorUpdateListener, Xamarin.AndroidX.Core;androidx.core.view.ViewPropertyAnimatorUpdateListener
AndroidX.Core.View.IViewPropertyAnimatorUpdateListener;androidx.core.view.ViewPropertyAnimatorUpdateListener
androidx.core.view.ViewPropertyAnimatorUpdateListener;androidx.core.view.ViewPropertyAnimatorUpdateListener
AndroidX.Core.View.IViewPropertyAnimatorUpdateListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.ViewPropertyAnimatorUpdateListenerImplementor
AndroidX.Core.View.IViewPropertyAnimatorUpdateListenerImplementor;mono.androidx.core.view.ViewPropertyAnimatorUpdateListenerImplementor
mono.androidx.core.view.ViewPropertyAnimatorUpdateListenerImplementor;mono.androidx.core.view.ViewPropertyAnimatorUpdateListenerImplementor
AndroidX.Core.View.IWindowInsetsAnimationControlListenerCompat, Xamarin.AndroidX.Core;androidx.core.view.WindowInsetsAnimationControlListenerCompat
AndroidX.Core.View.IWindowInsetsAnimationControlListenerCompat;androidx.core.view.WindowInsetsAnimationControlListenerCompat
androidx.core.view.WindowInsetsAnimationControlListenerCompat;androidx.core.view.WindowInsetsAnimationControlListenerCompat
AndroidX.Core.View.KeyEventDispatcher+IComponent, Xamarin.AndroidX.Core;androidx.core.view.KeyEventDispatcher$Component
AndroidX.Core.View.KeyEventDispatcher.IComponent;androidx.core.view.KeyEventDispatcher$Component
androidx.core.view.KeyEventDispatcher$Component;androidx.core.view.KeyEventDispatcher$Component
AndroidX.Core.View.MenuItemCompat+IOnActionExpandListener, Xamarin.AndroidX.Core;androidx.core.view.MenuItemCompat$OnActionExpandListener
AndroidX.Core.View.MenuItemCompat.IOnActionExpandListener;androidx.core.view.MenuItemCompat$OnActionExpandListener
androidx.core.view.MenuItemCompat$OnActionExpandListener;androidx.core.view.MenuItemCompat$OnActionExpandListener
AndroidX.Core.View.MenuItemCompat+IOnActionExpandListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.MenuItemCompat_OnActionExpandListenerImplementor
AndroidX.Core.View.MenuItemCompat.IOnActionExpandListenerImplementor;mono.androidx.core.view.MenuItemCompat_OnActionExpandListenerImplementor
mono.androidx.core.view.MenuItemCompat_OnActionExpandListenerImplementor;mono.androidx.core.view.MenuItemCompat_OnActionExpandListenerImplementor
AndroidX.Core.View.VelocityTrackerCompat+IVelocityTrackableMotionEventAxis, Xamarin.AndroidX.Core;androidx.core.view.VelocityTrackerCompat$VelocityTrackableMotionEventAxis
AndroidX.Core.View.VelocityTrackerCompat.IVelocityTrackableMotionEventAxis;androidx.core.view.VelocityTrackerCompat$VelocityTrackableMotionEventAxis
androidx.core.view.VelocityTrackerCompat$VelocityTrackableMotionEventAxis;androidx.core.view.VelocityTrackerCompat$VelocityTrackableMotionEventAxis
AndroidX.Core.View.ViewCompat+IFocusDirection, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$FocusDirection
AndroidX.Core.View.ViewCompat.IFocusDirection;androidx.core.view.ViewCompat$FocusDirection
androidx.core.view.ViewCompat$FocusDirection;androidx.core.view.ViewCompat$FocusDirection
AndroidX.Core.View.ViewCompat+IFocusRealDirection, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$FocusRealDirection
AndroidX.Core.View.ViewCompat.IFocusRealDirection;androidx.core.view.ViewCompat$FocusRealDirection
androidx.core.view.ViewCompat$FocusRealDirection;androidx.core.view.ViewCompat$FocusRealDirection
AndroidX.Core.View.ViewCompat+IFocusRelativeDirection, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$FocusRelativeDirection
AndroidX.Core.View.ViewCompat.IFocusRelativeDirection;androidx.core.view.ViewCompat$FocusRelativeDirection
androidx.core.view.ViewCompat$FocusRelativeDirection;androidx.core.view.ViewCompat$FocusRelativeDirection
AndroidX.Core.View.ViewCompat+INestedScrollType, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$NestedScrollType
AndroidX.Core.View.ViewCompat.INestedScrollType;androidx.core.view.ViewCompat$NestedScrollType
androidx.core.view.ViewCompat$NestedScrollType;androidx.core.view.ViewCompat$NestedScrollType
AndroidX.Core.View.ViewCompat+IOnUnhandledKeyEventListenerCompat, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat
AndroidX.Core.View.ViewCompat.IOnUnhandledKeyEventListenerCompat;androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat
androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat;androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat
AndroidX.Core.View.ViewCompat+IScrollAxis, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$ScrollAxis
AndroidX.Core.View.ViewCompat.IScrollAxis;androidx.core.view.ViewCompat$ScrollAxis
androidx.core.view.ViewCompat$ScrollAxis;androidx.core.view.ViewCompat$ScrollAxis
AndroidX.Core.View.ViewCompat+IScrollIndicators, Xamarin.AndroidX.Core;androidx.core.view.ViewCompat$ScrollIndicators
AndroidX.Core.View.ViewCompat.IScrollIndicators;androidx.core.view.ViewCompat$ScrollIndicators
androidx.core.view.ViewCompat$ScrollIndicators;androidx.core.view.ViewCompat$ScrollIndicators
AndroidX.Core.View.WindowInsetsAnimationCompat+Callback+IDispatchMode, Xamarin.AndroidX.Core;androidx.core.view.WindowInsetsAnimationCompat$Callback$DispatchMode
AndroidX.Core.View.WindowInsetsAnimationCompat.Callback.IDispatchMode;androidx.core.view.WindowInsetsAnimationCompat$Callback$DispatchMode
androidx.core.view.WindowInsetsAnimationCompat$Callback$DispatchMode;androidx.core.view.WindowInsetsAnimationCompat$Callback$DispatchMode
AndroidX.Core.View.WindowInsetsCompat+Type+IInsetsType, Xamarin.AndroidX.Core;androidx.core.view.WindowInsetsCompat$Type$InsetsType
AndroidX.Core.View.WindowInsetsCompat.Type.IInsetsType;androidx.core.view.WindowInsetsCompat$Type$InsetsType
androidx.core.view.WindowInsetsCompat$Type$InsetsType;androidx.core.view.WindowInsetsCompat$Type$InsetsType
AndroidX.Core.View.WindowInsetsControllerCompat+IOnControllableInsetsChangedListener, Xamarin.AndroidX.Core;androidx.core.view.WindowInsetsControllerCompat$OnControllableInsetsChangedListener
AndroidX.Core.View.WindowInsetsControllerCompat.IOnControllableInsetsChangedListener;androidx.core.view.WindowInsetsControllerCompat$OnControllableInsetsChangedListener
androidx.core.view.WindowInsetsControllerCompat$OnControllableInsetsChangedListener;androidx.core.view.WindowInsetsControllerCompat$OnControllableInsetsChangedListener
AndroidX.Core.View.WindowInsetsControllerCompat+IOnControllableInsetsChangedListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor
AndroidX.Core.View.WindowInsetsControllerCompat.IOnControllableInsetsChangedListenerImplementor;mono.androidx.core.view.WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor
mono.androidx.core.view.WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor;mono.androidx.core.view.WindowInsetsControllerCompat_OnControllableInsetsChangedListenerImplementor
AndroidX.Core.View.InputMethod.InputConnectionCompat+IOnCommitContentListener, Xamarin.AndroidX.Core;androidx.core.view.inputmethod.InputConnectionCompat$OnCommitContentListener
AndroidX.Core.View.InputMethod.InputConnectionCompat.IOnCommitContentListener;androidx.core.view.inputmethod.InputConnectionCompat$OnCommitContentListener
androidx.core.view.inputmethod.InputConnectionCompat$OnCommitContentListener;androidx.core.view.inputmethod.InputConnectionCompat$OnCommitContentListener
AndroidX.Core.View.InputMethod.InputConnectionCompat+IOnCommitContentListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.inputmethod.InputConnectionCompat_OnCommitContentListenerImplementor
AndroidX.Core.View.InputMethod.InputConnectionCompat.IOnCommitContentListenerImplementor;mono.androidx.core.view.inputmethod.InputConnectionCompat_OnCommitContentListenerImplementor
mono.androidx.core.view.inputmethod.InputConnectionCompat_OnCommitContentListenerImplementor;mono.androidx.core.view.inputmethod.InputConnectionCompat_OnCommitContentListenerImplementor
AndroidX.Core.View.Accessibility.AccessibilityEventCompat+IContentChangeType, Xamarin.AndroidX.Core;androidx.core.view.accessibility.AccessibilityEventCompat$ContentChangeType
AndroidX.Core.View.Accessibility.AccessibilityEventCompat.IContentChangeType;androidx.core.view.accessibility.AccessibilityEventCompat$ContentChangeType
androidx.core.view.accessibility.AccessibilityEventCompat$ContentChangeType;androidx.core.view.accessibility.AccessibilityEventCompat$ContentChangeType
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat+IAccessibilityStateChangeListener, Xamarin.AndroidX.Core;androidx.core.view.accessibility.AccessibilityManagerCompat$AccessibilityStateChangeListener
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat.IAccessibilityStateChangeListener;androidx.core.view.accessibility.AccessibilityManagerCompat$AccessibilityStateChangeListener
androidx.core.view.accessibility.AccessibilityManagerCompat$AccessibilityStateChangeListener;androidx.core.view.accessibility.AccessibilityManagerCompat$AccessibilityStateChangeListener
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat+IAccessibilityStateChangeListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_AccessibilityStateChangeListenerImplementor
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat.IAccessibilityStateChangeListenerImplementor;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_AccessibilityStateChangeListenerImplementor
mono.androidx.core.view.accessibility.AccessibilityManagerCompat_AccessibilityStateChangeListenerImplementor;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_AccessibilityStateChangeListenerImplementor
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat+ITouchExplorationStateChangeListener, Xamarin.AndroidX.Core;androidx.core.view.accessibility.AccessibilityManagerCompat$TouchExplorationStateChangeListener
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat.ITouchExplorationStateChangeListener;androidx.core.view.accessibility.AccessibilityManagerCompat$TouchExplorationStateChangeListener
androidx.core.view.accessibility.AccessibilityManagerCompat$TouchExplorationStateChangeListener;androidx.core.view.accessibility.AccessibilityManagerCompat$TouchExplorationStateChangeListener
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat+ITouchExplorationStateChangeListenerImplementor, Xamarin.AndroidX.Core;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_TouchExplorationStateChangeListenerImplementor
AndroidX.Core.View.Accessibility.AccessibilityManagerCompat.ITouchExplorationStateChangeListenerImplementor;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_TouchExplorationStateChangeListenerImplementor
mono.androidx.core.view.accessibility.AccessibilityManagerCompat_TouchExplorationStateChangeListenerImplementor;mono.androidx.core.view.accessibility.AccessibilityManagerCompat_TouchExplorationStateChangeListenerImplementor
AndroidX.Core.View.Accessibility.IAccessibilityViewCommand, Xamarin.AndroidX.Core;androidx.core.view.accessibility.AccessibilityViewCommand
AndroidX.Core.View.Accessibility.IAccessibilityViewCommand;androidx.core.view.accessibility.AccessibilityViewCommand
androidx.core.view.accessibility.AccessibilityViewCommand;androidx.core.view.accessibility.AccessibilityViewCommand
AndroidX.Core.Text.ITextDirectionHeuristicCompat, Xamarin.AndroidX.Core;androidx.core.text.TextDirectionHeuristicCompat
AndroidX.Core.Text.ITextDirectionHeuristicCompat;androidx.core.text.TextDirectionHeuristicCompat
androidx.core.text.TextDirectionHeuristicCompat;androidx.core.text.TextDirectionHeuristicCompat
AndroidX.Core.Text.Util.LinkifyCompat+ILinkifyMask, Xamarin.AndroidX.Core;androidx.core.text.util.LinkifyCompat$LinkifyMask
AndroidX.Core.Text.Util.LinkifyCompat.ILinkifyMask;androidx.core.text.util.LinkifyCompat$LinkifyMask
androidx.core.text.util.LinkifyCompat$LinkifyMask;androidx.core.text.util.LinkifyCompat$LinkifyMask
AndroidX.Core.Text.Util.LocalePreferences+CalendarType+ICalendarTypes, Xamarin.AndroidX.Core;androidx.core.text.util.LocalePreferences$CalendarType$CalendarTypes
AndroidX.Core.Text.Util.LocalePreferences.CalendarType.ICalendarTypes;androidx.core.text.util.LocalePreferences$CalendarType$CalendarTypes
androidx.core.text.util.LocalePreferences$CalendarType$CalendarTypes;androidx.core.text.util.LocalePreferences$CalendarType$CalendarTypes
AndroidX.Core.Text.Util.LocalePreferences+FirstDayOfWeek+IDays, Xamarin.AndroidX.Core;androidx.core.text.util.LocalePreferences$FirstDayOfWeek$Days
AndroidX.Core.Text.Util.LocalePreferences.FirstDayOfWeek.IDays;androidx.core.text.util.LocalePreferences$FirstDayOfWeek$Days
androidx.core.text.util.LocalePreferences$FirstDayOfWeek$Days;androidx.core.text.util.LocalePreferences$FirstDayOfWeek$Days
AndroidX.Core.Text.Util.LocalePreferences+HourCycle+IHourCycleTypes, Xamarin.AndroidX.Core;androidx.core.text.util.LocalePreferences$HourCycle$HourCycleTypes
AndroidX.Core.Text.Util.LocalePreferences.HourCycle.IHourCycleTypes;androidx.core.text.util.LocalePreferences$HourCycle$HourCycleTypes
androidx.core.text.util.LocalePreferences$HourCycle$HourCycleTypes;androidx.core.text.util.LocalePreferences$HourCycle$HourCycleTypes
AndroidX.Core.Text.Util.LocalePreferences+TemperatureUnit+ITemperatureUnits, Xamarin.AndroidX.Core;androidx.core.text.util.LocalePreferences$TemperatureUnit$TemperatureUnits
AndroidX.Core.Text.Util.LocalePreferences.TemperatureUnit.ITemperatureUnits;androidx.core.text.util.LocalePreferences$TemperatureUnit$TemperatureUnits
androidx.core.text.util.LocalePreferences$TemperatureUnit$TemperatureUnits;androidx.core.text.util.LocalePreferences$TemperatureUnit$TemperatureUnits
AndroidX.CursorAdapter.Widget.SimpleCursorAdapter+ICursorToStringConverter, Xamarin.AndroidX.CursorAdapter;androidx.cursoradapter.widget.SimpleCursorAdapter$CursorToStringConverter
AndroidX.CursorAdapter.Widget.SimpleCursorAdapter.ICursorToStringConverter;androidx.cursoradapter.widget.SimpleCursorAdapter$CursorToStringConverter
androidx.cursoradapter.widget.SimpleCursorAdapter$CursorToStringConverter;androidx.cursoradapter.widget.SimpleCursorAdapter$CursorToStringConverter
AndroidX.CursorAdapter.Widget.SimpleCursorAdapter+IViewBinder, Xamarin.AndroidX.CursorAdapter;androidx.cursoradapter.widget.SimpleCursorAdapter$ViewBinder
AndroidX.CursorAdapter.Widget.SimpleCursorAdapter.IViewBinder;androidx.cursoradapter.widget.SimpleCursorAdapter$ViewBinder
androidx.cursoradapter.widget.SimpleCursorAdapter$ViewBinder;androidx.cursoradapter.widget.SimpleCursorAdapter$ViewBinder
AndroidX.CustomView.Widget.IOpenable, Xamarin.AndroidX.CustomView;androidx.customview.widget.Openable
AndroidX.CustomView.Widget.IOpenable;androidx.customview.widget.Openable
androidx.customview.widget.Openable;androidx.customview.widget.Openable
AndroidX.CustomView.PoolingContainer.IPoolingContainerListener, Xamarin.AndroidX.CustomView.PoolingContainer;androidx.customview.poolingcontainer.PoolingContainerListener
AndroidX.CustomView.PoolingContainer.IPoolingContainerListener;androidx.customview.poolingcontainer.PoolingContainerListener
androidx.customview.poolingcontainer.PoolingContainerListener;androidx.customview.poolingcontainer.PoolingContainerListener
AndroidX.CustomView.PoolingContainer.IPoolingContainerListenerImplementor, Xamarin.AndroidX.CustomView.PoolingContainer;mono.androidx.customview.poolingcontainer.PoolingContainerListenerImplementor
AndroidX.CustomView.PoolingContainer.IPoolingContainerListenerImplementor;mono.androidx.customview.poolingcontainer.PoolingContainerListenerImplementor
mono.androidx.customview.poolingcontainer.PoolingContainerListenerImplementor;mono.androidx.customview.poolingcontainer.PoolingContainerListenerImplementor
AndroidX.DrawerLayout.Widget.DrawerLayout+IDrawerListener, Xamarin.AndroidX.DrawerLayout;androidx.drawerlayout.widget.DrawerLayout$DrawerListener
AndroidX.DrawerLayout.Widget.DrawerLayout.IDrawerListener;androidx.drawerlayout.widget.DrawerLayout$DrawerListener
androidx.drawerlayout.widget.DrawerLayout$DrawerListener;androidx.drawerlayout.widget.DrawerLayout$DrawerListener
AndroidX.DrawerLayout.Widget.DrawerLayout+IDrawerListenerImplementor, Xamarin.AndroidX.DrawerLayout;mono.androidx.drawerlayout.widget.DrawerLayout_DrawerListenerImplementor
AndroidX.DrawerLayout.Widget.DrawerLayout.IDrawerListenerImplementor;mono.androidx.drawerlayout.widget.DrawerLayout_DrawerListenerImplementor
mono.androidx.drawerlayout.widget.DrawerLayout_DrawerListenerImplementor;mono.androidx.drawerlayout.widget.DrawerLayout_DrawerListenerImplementor
AndroidX.DynamicAnimation.DynamicAnimation+IOnAnimationEndListener, Xamarin.AndroidX.DynamicAnimation;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationEndListener
AndroidX.DynamicAnimation.DynamicAnimation.IOnAnimationEndListener;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationEndListener
androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationEndListener;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationEndListener
AndroidX.DynamicAnimation.DynamicAnimation+IOnAnimationEndListenerImplementor, Xamarin.AndroidX.DynamicAnimation;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationEndListenerImplementor
AndroidX.DynamicAnimation.DynamicAnimation.IOnAnimationEndListenerImplementor;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationEndListenerImplementor
mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationEndListenerImplementor;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationEndListenerImplementor
AndroidX.DynamicAnimation.DynamicAnimation+IOnAnimationUpdateListener, Xamarin.AndroidX.DynamicAnimation;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationUpdateListener
AndroidX.DynamicAnimation.DynamicAnimation.IOnAnimationUpdateListener;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationUpdateListener
androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationUpdateListener;androidx.dynamicanimation.animation.DynamicAnimation$OnAnimationUpdateListener
AndroidX.DynamicAnimation.DynamicAnimation+IOnAnimationUpdateListenerImplementor, Xamarin.AndroidX.DynamicAnimation;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationUpdateListenerImplementor
AndroidX.DynamicAnimation.DynamicAnimation.IOnAnimationUpdateListenerImplementor;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationUpdateListenerImplementor
mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationUpdateListenerImplementor;mono.androidx.dynamicanimation.animation.DynamicAnimation_OnAnimationUpdateListenerImplementor
AndroidX.Emoji2.Text.EmojiCompat+ICodepointSequenceMatchResult, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$CodepointSequenceMatchResult
AndroidX.Emoji2.Text.EmojiCompat.ICodepointSequenceMatchResult;androidx.emoji2.text.EmojiCompat$CodepointSequenceMatchResult
androidx.emoji2.text.EmojiCompat$CodepointSequenceMatchResult;androidx.emoji2.text.EmojiCompat$CodepointSequenceMatchResult
AndroidX.Emoji2.Text.EmojiCompat+IGlyphChecker, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$GlyphChecker
AndroidX.Emoji2.Text.EmojiCompat.IGlyphChecker;androidx.emoji2.text.EmojiCompat$GlyphChecker
androidx.emoji2.text.EmojiCompat$GlyphChecker;androidx.emoji2.text.EmojiCompat$GlyphChecker
AndroidX.Emoji2.Text.EmojiCompat+ILoadStrategy, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$LoadStrategy
AndroidX.Emoji2.Text.EmojiCompat.ILoadStrategy;androidx.emoji2.text.EmojiCompat$LoadStrategy
androidx.emoji2.text.EmojiCompat$LoadStrategy;androidx.emoji2.text.EmojiCompat$LoadStrategy
AndroidX.Emoji2.Text.EmojiCompat+IMetadataRepoLoader, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$MetadataRepoLoader
AndroidX.Emoji2.Text.EmojiCompat.IMetadataRepoLoader;androidx.emoji2.text.EmojiCompat$MetadataRepoLoader
androidx.emoji2.text.EmojiCompat$MetadataRepoLoader;androidx.emoji2.text.EmojiCompat$MetadataRepoLoader
AndroidX.Emoji2.Text.EmojiCompat+IReplaceStrategy, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$ReplaceStrategy
AndroidX.Emoji2.Text.EmojiCompat.IReplaceStrategy;androidx.emoji2.text.EmojiCompat$ReplaceStrategy
androidx.emoji2.text.EmojiCompat$ReplaceStrategy;androidx.emoji2.text.EmojiCompat$ReplaceStrategy
AndroidX.Emoji2.Text.EmojiCompat+ISpanFactory, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.EmojiCompat$SpanFactory
AndroidX.Emoji2.Text.EmojiCompat.ISpanFactory;androidx.emoji2.text.EmojiCompat$SpanFactory
androidx.emoji2.text.EmojiCompat$SpanFactory;androidx.emoji2.text.EmojiCompat$SpanFactory
AndroidX.Emoji2.Text.TypefaceEmojiRasterizer+IHasGlyph, Xamarin.AndroidX.Emoji2;androidx.emoji2.text.TypefaceEmojiRasterizer$HasGlyph
AndroidX.Emoji2.Text.TypefaceEmojiRasterizer.IHasGlyph;androidx.emoji2.text.TypefaceEmojiRasterizer$HasGlyph
androidx.emoji2.text.TypefaceEmojiRasterizer$HasGlyph;androidx.emoji2.text.TypefaceEmojiRasterizer$HasGlyph
AndroidX.ExifInterface.Media.ExifInterface+IExifStreamType, Xamarin.AndroidX.ExifInterface;androidx.exifinterface.media.ExifInterface$ExifStreamType
AndroidX.ExifInterface.Media.ExifInterface.IExifStreamType;androidx.exifinterface.media.ExifInterface$ExifStreamType
androidx.exifinterface.media.ExifInterface$ExifStreamType;androidx.exifinterface.media.ExifInterface$ExifStreamType
AndroidX.ExifInterface.Media.ExifInterface+IIfdType, Xamarin.AndroidX.ExifInterface;androidx.exifinterface.media.ExifInterface$IfdType
AndroidX.ExifInterface.Media.ExifInterface.IIfdType;androidx.exifinterface.media.ExifInterface$IfdType
androidx.exifinterface.media.ExifInterface$IfdType;androidx.exifinterface.media.ExifInterface$IfdType
AndroidX.Fragment.App.FragmentManager+IBackStackEntry, Xamarin.AndroidX.Fragment;androidx.fragment.app.FragmentManager$BackStackEntry
AndroidX.Fragment.App.FragmentManager.IBackStackEntry;androidx.fragment.app.FragmentManager$BackStackEntry
androidx.fragment.app.FragmentManager$BackStackEntry;androidx.fragment.app.FragmentManager$BackStackEntry
AndroidX.Fragment.App.FragmentManager+IOnBackStackChangedListener, Xamarin.AndroidX.Fragment;androidx.fragment.app.FragmentManager$OnBackStackChangedListener
AndroidX.Fragment.App.FragmentManager.IOnBackStackChangedListener;androidx.fragment.app.FragmentManager$OnBackStackChangedListener
androidx.fragment.app.FragmentManager$OnBackStackChangedListener;androidx.fragment.app.FragmentManager$OnBackStackChangedListener
AndroidX.Fragment.App.FragmentManager+IOnBackStackChangedListenerImplementor, Xamarin.AndroidX.Fragment;mono.androidx.fragment.app.FragmentManager_OnBackStackChangedListenerImplementor
AndroidX.Fragment.App.FragmentManager.IOnBackStackChangedListenerImplementor;mono.androidx.fragment.app.FragmentManager_OnBackStackChangedListenerImplementor
mono.androidx.fragment.app.FragmentManager_OnBackStackChangedListenerImplementor;mono.androidx.fragment.app.FragmentManager_OnBackStackChangedListenerImplementor
AndroidX.Fragment.App.IFragmentOnAttachListener, Xamarin.AndroidX.Fragment;androidx.fragment.app.FragmentOnAttachListener
AndroidX.Fragment.App.IFragmentOnAttachListener;androidx.fragment.app.FragmentOnAttachListener
androidx.fragment.app.FragmentOnAttachListener;androidx.fragment.app.FragmentOnAttachListener
AndroidX.Fragment.App.IFragmentOnAttachListenerImplementor, Xamarin.AndroidX.Fragment;mono.androidx.fragment.app.FragmentOnAttachListenerImplementor
AndroidX.Fragment.App.IFragmentOnAttachListenerImplementor;mono.androidx.fragment.app.FragmentOnAttachListenerImplementor
mono.androidx.fragment.app.FragmentOnAttachListenerImplementor;mono.androidx.fragment.app.FragmentOnAttachListenerImplementor
AndroidX.Fragment.App.IFragmentResultListener, Xamarin.AndroidX.Fragment;androidx.fragment.app.FragmentResultListener
AndroidX.Fragment.App.IFragmentResultListener;androidx.fragment.app.FragmentResultListener
androidx.fragment.app.FragmentResultListener;androidx.fragment.app.FragmentResultListener
AndroidX.Fragment.App.IFragmentResultListenerImplementor, Xamarin.AndroidX.Fragment;mono.androidx.fragment.app.FragmentResultListenerImplementor
AndroidX.Fragment.App.IFragmentResultListenerImplementor;mono.androidx.fragment.app.FragmentResultListenerImplementor
mono.androidx.fragment.app.FragmentResultListenerImplementor;mono.androidx.fragment.app.FragmentResultListenerImplementor
AndroidX.Fragment.App.IFragmentResultOwner, Xamarin.AndroidX.Fragment;androidx.fragment.app.FragmentResultOwner
AndroidX.Fragment.App.IFragmentResultOwner;androidx.fragment.app.FragmentResultOwner
androidx.fragment.app.FragmentResultOwner;androidx.fragment.app.FragmentResultOwner
AndroidX.Fragment.App.IPredictiveBackControl, Xamarin.AndroidX.Fragment;androidx.fragment.app.PredictiveBackControl
AndroidX.Fragment.App.IPredictiveBackControl;androidx.fragment.app.PredictiveBackControl
androidx.fragment.app.PredictiveBackControl;androidx.fragment.app.PredictiveBackControl
AndroidX.Fragment.App.StrictMode.FragmentStrictMode+IOnViolationListener, Xamarin.AndroidX.Fragment;androidx.fragment.app.strictmode.FragmentStrictMode$OnViolationListener
AndroidX.Fragment.App.StrictMode.FragmentStrictMode.IOnViolationListener;androidx.fragment.app.strictmode.FragmentStrictMode$OnViolationListener
androidx.fragment.app.strictmode.FragmentStrictMode$OnViolationListener;androidx.fragment.app.strictmode.FragmentStrictMode$OnViolationListener
AndroidX.Fragment.App.StrictMode.FragmentStrictMode+IOnViolationListenerImplementor, Xamarin.AndroidX.Fragment;mono.androidx.fragment.app.strictmode.FragmentStrictMode_OnViolationListenerImplementor
AndroidX.Fragment.App.StrictMode.FragmentStrictMode.IOnViolationListenerImplementor;mono.androidx.fragment.app.strictmode.FragmentStrictMode_OnViolationListenerImplementor
mono.androidx.fragment.app.strictmode.FragmentStrictMode_OnViolationListenerImplementor;mono.androidx.fragment.app.strictmode.FragmentStrictMode_OnViolationListenerImplementor
AndroidX.Lifecycle.IDefaultLifecycleObserver, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.DefaultLifecycleObserver
AndroidX.Lifecycle.IDefaultLifecycleObserver;androidx.lifecycle.DefaultLifecycleObserver
androidx.lifecycle.DefaultLifecycleObserver;androidx.lifecycle.DefaultLifecycleObserver
AndroidX.Lifecycle.IGeneratedAdapter, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.GeneratedAdapter
AndroidX.Lifecycle.IGeneratedAdapter;androidx.lifecycle.GeneratedAdapter
androidx.lifecycle.GeneratedAdapter;androidx.lifecycle.GeneratedAdapter
AndroidX.Lifecycle.IGenericLifecycleObserver, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.GenericLifecycleObserver
AndroidX.Lifecycle.IGenericLifecycleObserver;androidx.lifecycle.GenericLifecycleObserver
androidx.lifecycle.GenericLifecycleObserver;androidx.lifecycle.GenericLifecycleObserver
AndroidX.Lifecycle.ILifecycleEventObserver, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.LifecycleEventObserver
AndroidX.Lifecycle.ILifecycleEventObserver;androidx.lifecycle.LifecycleEventObserver
androidx.lifecycle.LifecycleEventObserver;androidx.lifecycle.LifecycleEventObserver
AndroidX.Lifecycle.ILifecycleObserver, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.LifecycleObserver
AndroidX.Lifecycle.ILifecycleObserver;androidx.lifecycle.LifecycleObserver
androidx.lifecycle.LifecycleObserver;androidx.lifecycle.LifecycleObserver
AndroidX.Lifecycle.ILifecycleOwner, Xamarin.AndroidX.Lifecycle.Common.Jvm;androidx.lifecycle.LifecycleOwner
AndroidX.Lifecycle.ILifecycleOwner;androidx.lifecycle.LifecycleOwner
androidx.lifecycle.LifecycleOwner;androidx.lifecycle.LifecycleOwner
AndroidX.Lifecycle.ILiveDataScope, Xamarin.AndroidX.Lifecycle.LiveData;androidx.lifecycle.LiveDataScope
AndroidX.Lifecycle.ILiveDataScope;androidx.lifecycle.LiveDataScope
androidx.lifecycle.LiveDataScope;androidx.lifecycle.LiveDataScope
AndroidX.Lifecycle.IObserver, Xamarin.AndroidX.Lifecycle.LiveData.Core;androidx.lifecycle.Observer
AndroidX.Lifecycle.IObserver;androidx.lifecycle.Observer
androidx.lifecycle.Observer;androidx.lifecycle.Observer
AndroidX.Lifecycle.ILifecycleRegistryOwner, Xamarin.AndroidX.Lifecycle.Runtime.Android;androidx.lifecycle.LifecycleRegistryOwner
AndroidX.Lifecycle.ILifecycleRegistryOwner;androidx.lifecycle.LifecycleRegistryOwner
androidx.lifecycle.LifecycleRegistryOwner;androidx.lifecycle.LifecycleRegistryOwner
AndroidX.Lifecycle.ReportFragment+IActivityInitializationListener, Xamarin.AndroidX.Lifecycle.Runtime.Android;androidx.lifecycle.ReportFragment$ActivityInitializationListener
AndroidX.Lifecycle.ReportFragment.IActivityInitializationListener;androidx.lifecycle.ReportFragment$ActivityInitializationListener
androidx.lifecycle.ReportFragment$ActivityInitializationListener;androidx.lifecycle.ReportFragment$ActivityInitializationListener
AndroidX.Lifecycle.ReportFragment+IActivityInitializationListenerImplementor, Xamarin.AndroidX.Lifecycle.Runtime.Android;mono.androidx.lifecycle.ReportFragment_ActivityInitializationListenerImplementor
AndroidX.Lifecycle.ReportFragment.IActivityInitializationListenerImplementor;mono.androidx.lifecycle.ReportFragment_ActivityInitializationListenerImplementor
mono.androidx.lifecycle.ReportFragment_ActivityInitializationListenerImplementor;mono.androidx.lifecycle.ReportFragment_ActivityInitializationListenerImplementor
AndroidX.Lifecycle.IHasDefaultViewModelProviderFactory, Xamarin.AndroidX.Lifecycle.ViewModel.Android;androidx.lifecycle.HasDefaultViewModelProviderFactory
AndroidX.Lifecycle.IHasDefaultViewModelProviderFactory;androidx.lifecycle.HasDefaultViewModelProviderFactory
androidx.lifecycle.HasDefaultViewModelProviderFactory;androidx.lifecycle.HasDefaultViewModelProviderFactory
AndroidX.Lifecycle.IViewModelStoreOwner, Xamarin.AndroidX.Lifecycle.ViewModel.Android;androidx.lifecycle.ViewModelStoreOwner
AndroidX.Lifecycle.IViewModelStoreOwner;androidx.lifecycle.ViewModelStoreOwner
androidx.lifecycle.ViewModelStoreOwner;androidx.lifecycle.ViewModelStoreOwner
AndroidX.Lifecycle.ViewModelProvider+IFactory, Xamarin.AndroidX.Lifecycle.ViewModel.Android;androidx.lifecycle.ViewModelProvider$Factory
AndroidX.Lifecycle.ViewModelProvider.IFactory;androidx.lifecycle.ViewModelProvider$Factory
androidx.lifecycle.ViewModelProvider$Factory;androidx.lifecycle.ViewModelProvider$Factory
AndroidX.Lifecycle.ViewModels.CreationExtras+IKey, Xamarin.AndroidX.Lifecycle.ViewModel.Android;androidx.lifecycle.viewmodel.CreationExtras$Key
AndroidX.Lifecycle.ViewModels.CreationExtras.IKey;androidx.lifecycle.viewmodel.CreationExtras$Key
androidx.lifecycle.viewmodel.CreationExtras$Key;androidx.lifecycle.viewmodel.CreationExtras$Key
AndroidX.Lifecycle.ViewModels.IViewModelFactoryDsl, Xamarin.AndroidX.Lifecycle.ViewModel.Android;androidx.lifecycle.viewmodel.ViewModelFactoryDsl
AndroidX.Lifecycle.ViewModels.IViewModelFactoryDsl;androidx.lifecycle.viewmodel.ViewModelFactoryDsl
androidx.lifecycle.viewmodel.ViewModelFactoryDsl;androidx.lifecycle.viewmodel.ViewModelFactoryDsl
AndroidX.Loader.Content.Loader+IOnLoadCanceledListener, Xamarin.AndroidX.Loader;androidx.loader.content.Loader$OnLoadCanceledListener
AndroidX.Loader.Content.Loader.IOnLoadCanceledListener;androidx.loader.content.Loader$OnLoadCanceledListener
androidx.loader.content.Loader$OnLoadCanceledListener;androidx.loader.content.Loader$OnLoadCanceledListener
AndroidX.Loader.Content.Loader+IOnLoadCanceledListenerImplementor, Xamarin.AndroidX.Loader;mono.androidx.loader.content.Loader_OnLoadCanceledListenerImplementor
AndroidX.Loader.Content.Loader.IOnLoadCanceledListenerImplementor;mono.androidx.loader.content.Loader_OnLoadCanceledListenerImplementor
mono.androidx.loader.content.Loader_OnLoadCanceledListenerImplementor;mono.androidx.loader.content.Loader_OnLoadCanceledListenerImplementor
AndroidX.Loader.Content.Loader+IOnLoadCompleteListener, Xamarin.AndroidX.Loader;androidx.loader.content.Loader$OnLoadCompleteListener
AndroidX.Loader.Content.Loader.IOnLoadCompleteListener;androidx.loader.content.Loader$OnLoadCompleteListener
androidx.loader.content.Loader$OnLoadCompleteListener;androidx.loader.content.Loader$OnLoadCompleteListener
AndroidX.Loader.Content.Loader+IOnLoadCompleteListenerImplementor, Xamarin.AndroidX.Loader;mono.androidx.loader.content.Loader_OnLoadCompleteListenerImplementor
AndroidX.Loader.Content.Loader.IOnLoadCompleteListenerImplementor;mono.androidx.loader.content.Loader_OnLoadCompleteListenerImplementor
mono.androidx.loader.content.Loader_OnLoadCompleteListenerImplementor;mono.androidx.loader.content.Loader_OnLoadCompleteListenerImplementor
AndroidX.Loader.App.LoaderManager+ILoaderCallbacks, Xamarin.AndroidX.Loader;androidx.loader.app.LoaderManager$LoaderCallbacks
AndroidX.Loader.App.LoaderManager.ILoaderCallbacks;androidx.loader.app.LoaderManager$LoaderCallbacks
androidx.loader.app.LoaderManager$LoaderCallbacks;androidx.loader.app.LoaderManager$LoaderCallbacks
AndroidX.Navigation.IFloatingWindow, Xamarin.AndroidX.Navigation.Common;androidx.navigation.FloatingWindow
AndroidX.Navigation.IFloatingWindow;androidx.navigation.FloatingWindow
androidx.navigation.FloatingWindow;androidx.navigation.FloatingWindow
AndroidX.Navigation.INavArgs, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavArgs
AndroidX.Navigation.INavArgs;androidx.navigation.NavArgs
androidx.navigation.NavArgs;androidx.navigation.NavArgs
AndroidX.Navigation.INavDeepLinkDsl, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavDeepLinkDsl
AndroidX.Navigation.INavDeepLinkDsl;androidx.navigation.NavDeepLinkDsl
androidx.navigation.NavDeepLinkDsl;androidx.navigation.NavDeepLinkDsl
AndroidX.Navigation.INavDestinationDsl, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavDestinationDsl
AndroidX.Navigation.INavDestinationDsl;androidx.navigation.NavDestinationDsl
androidx.navigation.NavDestinationDsl;androidx.navigation.NavDestinationDsl
AndroidX.Navigation.INavDirections, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavDirections
AndroidX.Navigation.INavDirections;androidx.navigation.NavDirections
androidx.navigation.NavDirections;androidx.navigation.NavDirections
AndroidX.Navigation.INavOptionsDsl, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavOptionsDsl
AndroidX.Navigation.INavOptionsDsl;androidx.navigation.NavOptionsDsl
androidx.navigation.NavOptionsDsl;androidx.navigation.NavOptionsDsl
AndroidX.Navigation.INavViewModelStoreProvider, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavViewModelStoreProvider
AndroidX.Navigation.INavViewModelStoreProvider;androidx.navigation.NavViewModelStoreProvider
androidx.navigation.NavViewModelStoreProvider;androidx.navigation.NavViewModelStoreProvider
AndroidX.Navigation.NavDestination+IClassType, Xamarin.AndroidX.Navigation.Common;androidx.navigation.NavDestination$ClassType
AndroidX.Navigation.NavDestination.IClassType;androidx.navigation.NavDestination$ClassType
androidx.navigation.NavDestination$ClassType;androidx.navigation.NavDestination$ClassType
AndroidX.Navigation.Navigator+IExtras, Xamarin.AndroidX.Navigation.Common;androidx.navigation.Navigator$Extras
AndroidX.Navigation.Navigator.IExtras;androidx.navigation.Navigator$Extras
androidx.navigation.Navigator$Extras;androidx.navigation.Navigator$Extras
AndroidX.Navigation.Navigator+IName, Xamarin.AndroidX.Navigation.Common;androidx.navigation.Navigator$Name
AndroidX.Navigation.Navigator.IName;androidx.navigation.Navigator$Name
androidx.navigation.Navigator$Name;androidx.navigation.Navigator$Name
AndroidX.Navigation.INavDeepLinkSaveStateControl, Xamarin.AndroidX.Navigation.Runtime;androidx.navigation.NavDeepLinkSaveStateControl
AndroidX.Navigation.INavDeepLinkSaveStateControl;androidx.navigation.NavDeepLinkSaveStateControl
androidx.navigation.NavDeepLinkSaveStateControl;androidx.navigation.NavDeepLinkSaveStateControl
AndroidX.Navigation.INavHost, Xamarin.AndroidX.Navigation.Runtime;androidx.navigation.NavHost
AndroidX.Navigation.INavHost;androidx.navigation.NavHost
androidx.navigation.NavHost;androidx.navigation.NavHost
AndroidX.Navigation.NavController+IOnDestinationChangedListener, Xamarin.AndroidX.Navigation.Runtime;androidx.navigation.NavController$OnDestinationChangedListener
AndroidX.Navigation.NavController.IOnDestinationChangedListener;androidx.navigation.NavController$OnDestinationChangedListener
androidx.navigation.NavController$OnDestinationChangedListener;androidx.navigation.NavController$OnDestinationChangedListener
AndroidX.Navigation.NavController+IOnDestinationChangedListenerImplementor, Xamarin.AndroidX.Navigation.Runtime;mono.androidx.navigation.NavController_OnDestinationChangedListenerImplementor
AndroidX.Navigation.NavController.IOnDestinationChangedListenerImplementor;mono.androidx.navigation.NavController_OnDestinationChangedListenerImplementor
mono.androidx.navigation.NavController_OnDestinationChangedListenerImplementor;mono.androidx.navigation.NavController_OnDestinationChangedListenerImplementor
AndroidX.Navigation.UI.AppBarConfiguration+IOnNavigateUpListener, Xamarin.AndroidX.Navigation.UI;androidx.navigation.ui.AppBarConfiguration$OnNavigateUpListener
AndroidX.Navigation.UI.AppBarConfiguration.IOnNavigateUpListener;androidx.navigation.ui.AppBarConfiguration$OnNavigateUpListener
androidx.navigation.ui.AppBarConfiguration$OnNavigateUpListener;androidx.navigation.ui.AppBarConfiguration$OnNavigateUpListener
AndroidX.Navigation.UI.AppBarConfiguration+IOnNavigateUpListenerImplementor, Xamarin.AndroidX.Navigation.UI;mono.androidx.navigation.ui.AppBarConfiguration_OnNavigateUpListenerImplementor
AndroidX.Navigation.UI.AppBarConfiguration.IOnNavigateUpListenerImplementor;mono.androidx.navigation.ui.AppBarConfiguration_OnNavigateUpListenerImplementor
mono.androidx.navigation.ui.AppBarConfiguration_OnNavigateUpListenerImplementor;mono.androidx.navigation.ui.AppBarConfiguration_OnNavigateUpListenerImplementor
AndroidX.Navigation.UI.INavigationUiSaveStateControl, Xamarin.AndroidX.Navigation.UI;androidx.navigation.ui.NavigationUiSaveStateControl
AndroidX.Navigation.UI.INavigationUiSaveStateControl;androidx.navigation.ui.NavigationUiSaveStateControl
androidx.navigation.ui.NavigationUiSaveStateControl;androidx.navigation.ui.NavigationUiSaveStateControl
AndroidX.Print.PrintHelper+IOnPrintFinishCallback, Xamarin.AndroidX.Print;androidx.print.PrintHelper$OnPrintFinishCallback
AndroidX.Print.PrintHelper.IOnPrintFinishCallback;androidx.print.PrintHelper$OnPrintFinishCallback
androidx.print.PrintHelper$OnPrintFinishCallback;androidx.print.PrintHelper$OnPrintFinishCallback
AndroidX.ProfileInstallers.ProfileInstaller+IDiagnosticCode, Xamarin.AndroidX.ProfileInstaller.ProfileInstaller;androidx.profileinstaller.ProfileInstaller$DiagnosticCode
AndroidX.ProfileInstallers.ProfileInstaller.IDiagnosticCode;androidx.profileinstaller.ProfileInstaller$DiagnosticCode
androidx.profileinstaller.ProfileInstaller$DiagnosticCode;androidx.profileinstaller.ProfileInstaller$DiagnosticCode
AndroidX.ProfileInstallers.ProfileInstaller+IDiagnosticsCallback, Xamarin.AndroidX.ProfileInstaller.ProfileInstaller;androidx.profileinstaller.ProfileInstaller$DiagnosticsCallback
AndroidX.ProfileInstallers.ProfileInstaller.IDiagnosticsCallback;androidx.profileinstaller.ProfileInstaller$DiagnosticsCallback
androidx.profileinstaller.ProfileInstaller$DiagnosticsCallback;androidx.profileinstaller.ProfileInstaller$DiagnosticsCallback
AndroidX.ProfileInstallers.ProfileInstaller+IResultCode, Xamarin.AndroidX.ProfileInstaller.ProfileInstaller;androidx.profileinstaller.ProfileInstaller$ResultCode
AndroidX.ProfileInstallers.ProfileInstaller.IResultCode;androidx.profileinstaller.ProfileInstaller$ResultCode
androidx.profileinstaller.ProfileInstaller$ResultCode;androidx.profileinstaller.ProfileInstaller$ResultCode
AndroidX.ProfileInstallers.ProfileVerifier+CompilationStatus+IResultCode, Xamarin.AndroidX.ProfileInstaller.ProfileInstaller;androidx.profileinstaller.ProfileVerifier$CompilationStatus$ResultCode
AndroidX.ProfileInstallers.ProfileVerifier.CompilationStatus.IResultCode;androidx.profileinstaller.ProfileVerifier$CompilationStatus$ResultCode
androidx.profileinstaller.ProfileVerifier$CompilationStatus$ResultCode;androidx.profileinstaller.ProfileVerifier$CompilationStatus$ResultCode
AndroidX.RecyclerView.Widget.AsyncListDiffer+IListListener, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.AsyncListDiffer$ListListener
AndroidX.RecyclerView.Widget.AsyncListDiffer.IListListener;androidx.recyclerview.widget.AsyncListDiffer$ListListener
androidx.recyclerview.widget.AsyncListDiffer$ListListener;androidx.recyclerview.widget.AsyncListDiffer$ListListener
AndroidX.RecyclerView.Widget.AsyncListDiffer+IListListenerImplementor, Xamarin.AndroidX.RecyclerView;mono.androidx.recyclerview.widget.AsyncListDiffer_ListListenerImplementor
AndroidX.RecyclerView.Widget.AsyncListDiffer.IListListenerImplementor;mono.androidx.recyclerview.widget.AsyncListDiffer_ListListenerImplementor
mono.androidx.recyclerview.widget.AsyncListDiffer_ListListenerImplementor;mono.androidx.recyclerview.widget.AsyncListDiffer_ListListenerImplementor
AndroidX.RecyclerView.Widget.IItemTouchUIUtil, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.ItemTouchUIUtil
AndroidX.RecyclerView.Widget.IItemTouchUIUtil;androidx.recyclerview.widget.ItemTouchUIUtil
androidx.recyclerview.widget.ItemTouchUIUtil;androidx.recyclerview.widget.ItemTouchUIUtil
AndroidX.RecyclerView.Widget.IListUpdateCallback, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.ListUpdateCallback
AndroidX.RecyclerView.Widget.IListUpdateCallback;androidx.recyclerview.widget.ListUpdateCallback
androidx.recyclerview.widget.ListUpdateCallback;androidx.recyclerview.widget.ListUpdateCallback
AndroidX.RecyclerView.Widget.ItemTouchHelper+IViewDropHandler, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.ItemTouchHelper$ViewDropHandler
AndroidX.RecyclerView.Widget.ItemTouchHelper.IViewDropHandler;androidx.recyclerview.widget.ItemTouchHelper$ViewDropHandler
androidx.recyclerview.widget.ItemTouchHelper$ViewDropHandler;androidx.recyclerview.widget.ItemTouchHelper$ViewDropHandler
AndroidX.RecyclerView.Widget.RecyclerView+IChildDrawingOrderCallback, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback
AndroidX.RecyclerView.Widget.RecyclerView.IChildDrawingOrderCallback;androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback
androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback;androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback
AndroidX.RecyclerView.Widget.RecyclerView+EdgeEffectFactory+IEdgeDirection, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory$EdgeDirection
AndroidX.RecyclerView.Widget.RecyclerView.EdgeEffectFactory.IEdgeDirection;androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory$EdgeDirection
androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory$EdgeDirection;androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory$EdgeDirection
AndroidX.RecyclerView.Widget.RecyclerView+ItemAnimator+IAdapterChanges, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$ItemAnimator$AdapterChanges
AndroidX.RecyclerView.Widget.RecyclerView.ItemAnimator.IAdapterChanges;androidx.recyclerview.widget.RecyclerView$ItemAnimator$AdapterChanges
androidx.recyclerview.widget.RecyclerView$ItemAnimator$AdapterChanges;androidx.recyclerview.widget.RecyclerView$ItemAnimator$AdapterChanges
AndroidX.RecyclerView.Widget.RecyclerView+ItemAnimator+IItemAnimatorFinishedListener, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$ItemAnimator$ItemAnimatorFinishedListener
AndroidX.RecyclerView.Widget.RecyclerView.ItemAnimator.IItemAnimatorFinishedListener;androidx.recyclerview.widget.RecyclerView$ItemAnimator$ItemAnimatorFinishedListener
androidx.recyclerview.widget.RecyclerView$ItemAnimator$ItemAnimatorFinishedListener;androidx.recyclerview.widget.RecyclerView$ItemAnimator$ItemAnimatorFinishedListener
AndroidX.RecyclerView.Widget.RecyclerView+ItemAnimator+IItemAnimatorFinishedListenerImplementor, Xamarin.AndroidX.RecyclerView;mono.androidx.recyclerview.widget.RecyclerView_ItemAnimator_ItemAnimatorFinishedListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView.ItemAnimator.IItemAnimatorFinishedListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_ItemAnimator_ItemAnimatorFinishedListenerImplementor
mono.androidx.recyclerview.widget.RecyclerView_ItemAnimator_ItemAnimatorFinishedListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_ItemAnimator_ItemAnimatorFinishedListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView+LayoutManager+ILayoutPrefetchRegistry, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$LayoutManager$LayoutPrefetchRegistry
AndroidX.RecyclerView.Widget.RecyclerView.LayoutManager.ILayoutPrefetchRegistry;androidx.recyclerview.widget.RecyclerView$LayoutManager$LayoutPrefetchRegistry
androidx.recyclerview.widget.RecyclerView$LayoutManager$LayoutPrefetchRegistry;androidx.recyclerview.widget.RecyclerView$LayoutManager$LayoutPrefetchRegistry
AndroidX.RecyclerView.Widget.RecyclerView+IOnChildAttachStateChangeListener, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$OnChildAttachStateChangeListener
AndroidX.RecyclerView.Widget.RecyclerView.IOnChildAttachStateChangeListener;androidx.recyclerview.widget.RecyclerView$OnChildAttachStateChangeListener
androidx.recyclerview.widget.RecyclerView$OnChildAttachStateChangeListener;androidx.recyclerview.widget.RecyclerView$OnChildAttachStateChangeListener
AndroidX.RecyclerView.Widget.RecyclerView+IOnChildAttachStateChangeListenerImplementor, Xamarin.AndroidX.RecyclerView;mono.androidx.recyclerview.widget.RecyclerView_OnChildAttachStateChangeListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView.IOnChildAttachStateChangeListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_OnChildAttachStateChangeListenerImplementor
mono.androidx.recyclerview.widget.RecyclerView_OnChildAttachStateChangeListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_OnChildAttachStateChangeListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView+IOnItemTouchListener, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$OnItemTouchListener
AndroidX.RecyclerView.Widget.RecyclerView.IOnItemTouchListener;androidx.recyclerview.widget.RecyclerView$OnItemTouchListener
androidx.recyclerview.widget.RecyclerView$OnItemTouchListener;androidx.recyclerview.widget.RecyclerView$OnItemTouchListener
AndroidX.RecyclerView.Widget.RecyclerView+IOnItemTouchListenerImplementor, Xamarin.AndroidX.RecyclerView;mono.androidx.recyclerview.widget.RecyclerView_OnItemTouchListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView.IOnItemTouchListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_OnItemTouchListenerImplementor
mono.androidx.recyclerview.widget.RecyclerView_OnItemTouchListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_OnItemTouchListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView+IOrientation, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$Orientation
AndroidX.RecyclerView.Widget.RecyclerView.IOrientation;androidx.recyclerview.widget.RecyclerView$Orientation
androidx.recyclerview.widget.RecyclerView$Orientation;androidx.recyclerview.widget.RecyclerView$Orientation
AndroidX.RecyclerView.Widget.RecyclerView+IRecyclerListener, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$RecyclerListener
AndroidX.RecyclerView.Widget.RecyclerView.IRecyclerListener;androidx.recyclerview.widget.RecyclerView$RecyclerListener
androidx.recyclerview.widget.RecyclerView$RecyclerListener;androidx.recyclerview.widget.RecyclerView$RecyclerListener
AndroidX.RecyclerView.Widget.RecyclerView+IRecyclerListenerImplementor, Xamarin.AndroidX.RecyclerView;mono.androidx.recyclerview.widget.RecyclerView_RecyclerListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView.IRecyclerListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_RecyclerListenerImplementor
mono.androidx.recyclerview.widget.RecyclerView_RecyclerListenerImplementor;mono.androidx.recyclerview.widget.RecyclerView_RecyclerListenerImplementor
AndroidX.RecyclerView.Widget.RecyclerView+SmoothScroller+IScrollVectorProvider, Xamarin.AndroidX.RecyclerView;androidx.recyclerview.widget.RecyclerView$SmoothScroller$ScrollVectorProvider
AndroidX.RecyclerView.Widget.RecyclerView.SmoothScroller.IScrollVectorProvider;androidx.recyclerview.widget.RecyclerView$SmoothScroller$ScrollVectorProvider
androidx.recyclerview.widget.RecyclerView$SmoothScroller$ScrollVectorProvider;androidx.recyclerview.widget.RecyclerView$SmoothScroller$ScrollVectorProvider
AndroidX.ResourceInspection.Annotation.IAppCompatShadowedAttributes, Xamarin.AndroidX.ResourceInspection.Annotation;androidx.resourceinspection.annotation.AppCompatShadowedAttributes
AndroidX.ResourceInspection.Annotation.IAppCompatShadowedAttributes;androidx.resourceinspection.annotation.AppCompatShadowedAttributes
androidx.resourceinspection.annotation.AppCompatShadowedAttributes;androidx.resourceinspection.annotation.AppCompatShadowedAttributes
AndroidX.ResourceInspection.Annotation.IAttributeIntMap, Xamarin.AndroidX.ResourceInspection.Annotation;androidx.resourceinspection.annotation.Attribute$IntMap
AndroidX.ResourceInspection.Annotation.IAttributeIntMap;androidx.resourceinspection.annotation.Attribute$IntMap
androidx.resourceinspection.annotation.Attribute$IntMap;androidx.resourceinspection.annotation.Attribute$IntMap
AndroidX.ResourceInspection.Annotation.IAttribute, Xamarin.AndroidX.ResourceInspection.Annotation;androidx.resourceinspection.annotation.Attribute
AndroidX.ResourceInspection.Annotation.IAttribute;androidx.resourceinspection.annotation.Attribute
androidx.resourceinspection.annotation.Attribute;androidx.resourceinspection.annotation.Attribute
AndroidX.SavedState.ISavedStateRegistryOwner, Xamarin.AndroidX.SavedState;androidx.savedstate.SavedStateRegistryOwner
AndroidX.SavedState.ISavedStateRegistryOwner;androidx.savedstate.SavedStateRegistryOwner
androidx.savedstate.SavedStateRegistryOwner;androidx.savedstate.SavedStateRegistryOwner
AndroidX.SavedState.SavedStateRegistry+IAutoRecreated, Xamarin.AndroidX.SavedState;androidx.savedstate.SavedStateRegistry$AutoRecreated
AndroidX.SavedState.SavedStateRegistry.IAutoRecreated;androidx.savedstate.SavedStateRegistry$AutoRecreated
androidx.savedstate.SavedStateRegistry$AutoRecreated;androidx.savedstate.SavedStateRegistry$AutoRecreated
AndroidX.SavedState.SavedStateRegistry+ISavedStateProvider, Xamarin.AndroidX.SavedState;androidx.savedstate.SavedStateRegistry$SavedStateProvider
AndroidX.SavedState.SavedStateRegistry.ISavedStateProvider;androidx.savedstate.SavedStateRegistry$SavedStateProvider
androidx.savedstate.SavedStateRegistry$SavedStateProvider;androidx.savedstate.SavedStateRegistry$SavedStateProvider
AndroidX.SlidingPaneLayout.Widget.SlidingPaneLayout+IPanelSlideListener, Xamarin.AndroidX.SlidingPaneLayout;androidx.slidingpanelayout.widget.SlidingPaneLayout$PanelSlideListener
AndroidX.SlidingPaneLayout.Widget.SlidingPaneLayout.IPanelSlideListener;androidx.slidingpanelayout.widget.SlidingPaneLayout$PanelSlideListener
androidx.slidingpanelayout.widget.SlidingPaneLayout$PanelSlideListener;androidx.slidingpanelayout.widget.SlidingPaneLayout$PanelSlideListener
AndroidX.SlidingPaneLayout.Widget.SlidingPaneLayout+IPanelSlideListenerImplementor, Xamarin.AndroidX.SlidingPaneLayout;mono.androidx.slidingpanelayout.widget.SlidingPaneLayout_PanelSlideListenerImplementor
AndroidX.SlidingPaneLayout.Widget.SlidingPaneLayout.IPanelSlideListenerImplementor;mono.androidx.slidingpanelayout.widget.SlidingPaneLayout_PanelSlideListenerImplementor
mono.androidx.slidingpanelayout.widget.SlidingPaneLayout_PanelSlideListenerImplementor;mono.androidx.slidingpanelayout.widget.SlidingPaneLayout_PanelSlideListenerImplementor
AndroidX.Startup.IInitializer, Xamarin.AndroidX.Startup.StartupRuntime;androidx.startup.Initializer
AndroidX.Startup.IInitializer;androidx.startup.Initializer
androidx.startup.Initializer;androidx.startup.Initializer
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout+IOnChildScrollUpCallback, Xamarin.AndroidX.SwipeRefreshLayout;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnChildScrollUpCallback
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout.IOnChildScrollUpCallback;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnChildScrollUpCallback
androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnChildScrollUpCallback;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnChildScrollUpCallback
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout+IOnRefreshListener, Xamarin.AndroidX.SwipeRefreshLayout;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnRefreshListener
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout.IOnRefreshListener;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnRefreshListener
androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnRefreshListener;androidx.swiperefreshlayout.widget.SwipeRefreshLayout$OnRefreshListener
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout+IOnRefreshListenerImplementor, Xamarin.AndroidX.SwipeRefreshLayout;mono.androidx.swiperefreshlayout.widget.SwipeRefreshLayout_OnRefreshListenerImplementor
AndroidX.SwipeRefreshLayout.Widget.SwipeRefreshLayout.IOnRefreshListenerImplementor;mono.androidx.swiperefreshlayout.widget.SwipeRefreshLayout_OnRefreshListenerImplementor
mono.androidx.swiperefreshlayout.widget.SwipeRefreshLayout_OnRefreshListenerImplementor;mono.androidx.swiperefreshlayout.widget.SwipeRefreshLayout_OnRefreshListenerImplementor
AndroidX.SwipeRefreshLayout.Widget.CircularProgressDrawable+IProgressDrawableSize, Xamarin.AndroidX.SwipeRefreshLayout;androidx.swiperefreshlayout.widget.CircularProgressDrawable$ProgressDrawableSize
AndroidX.SwipeRefreshLayout.Widget.CircularProgressDrawable.IProgressDrawableSize;androidx.swiperefreshlayout.widget.CircularProgressDrawable$ProgressDrawableSize
androidx.swiperefreshlayout.widget.CircularProgressDrawable$ProgressDrawableSize;androidx.swiperefreshlayout.widget.CircularProgressDrawable$ProgressDrawableSize
AndroidX.Transitions.FloatArrayEvaluator, Xamarin.AndroidX.Transition;crc64a25b61d9f8ee364f.FloatArrayEvaluator
AndroidX.Transitions.FloatArrayEvaluator;crc64a25b61d9f8ee364f.FloatArrayEvaluator
androidx.transitions.FloatArrayEvaluator;crc64a25b61d9f8ee364f.FloatArrayEvaluator
AndroidX.Transitions.TransitionUtils, Xamarin.AndroidX.Transition;crc64a25b61d9f8ee364f.TransitionUtils
AndroidX.Transitions.TransitionUtils;crc64a25b61d9f8ee364f.TransitionUtils
androidx.transitions.TransitionUtils;crc64a25b61d9f8ee364f.TransitionUtils
AndroidX.Transitions.TransitionUtils+MatrixEvaluator, Xamarin.AndroidX.Transition;crc64a25b61d9f8ee364f.TransitionUtils_MatrixEvaluator
AndroidX.Transitions.TransitionUtils.MatrixEvaluator;crc64a25b61d9f8ee364f.TransitionUtils_MatrixEvaluator
androidx.transitions.TransitionUtils_MatrixEvaluator;crc64a25b61d9f8ee364f.TransitionUtils_MatrixEvaluator
AndroidX.Transitions.RectEvaluator, Xamarin.AndroidX.Transition;crc64a25b61d9f8ee364f.RectEvaluator
AndroidX.Transitions.RectEvaluator;crc64a25b61d9f8ee364f.RectEvaluator
androidx.transitions.RectEvaluator;crc64a25b61d9f8ee364f.RectEvaluator
AndroidX.Transitions.ITransitionSeekController, Xamarin.AndroidX.Transition;androidx.transition.TransitionSeekController
AndroidX.Transitions.ITransitionSeekController;androidx.transition.TransitionSeekController
androidx.transition.TransitionSeekController;androidx.transition.TransitionSeekController
AndroidX.Transitions.Slide+IGravityFlag, Xamarin.AndroidX.Transition;androidx.transition.Slide$GravityFlag
AndroidX.Transitions.Slide.IGravityFlag;androidx.transition.Slide$GravityFlag
androidx.transition.Slide$GravityFlag;androidx.transition.Slide$GravityFlag
AndroidX.Transitions.Transition+IMatchOrder, Xamarin.AndroidX.Transition;androidx.transition.Transition$MatchOrder
AndroidX.Transitions.Transition.IMatchOrder;androidx.transition.Transition$MatchOrder
androidx.transition.Transition$MatchOrder;androidx.transition.Transition$MatchOrder
AndroidX.Transitions.Transition+ITransitionListener, Xamarin.AndroidX.Transition;androidx.transition.Transition$TransitionListener
AndroidX.Transitions.Transition.ITransitionListener;androidx.transition.Transition$TransitionListener
androidx.transition.Transition$TransitionListener;androidx.transition.Transition$TransitionListener
AndroidX.Transitions.Transition+ITransitionListenerImplementor, Xamarin.AndroidX.Transition;mono.androidx.transition.Transition_TransitionListenerImplementor
AndroidX.Transitions.Transition.ITransitionListenerImplementor;mono.androidx.transition.Transition_TransitionListenerImplementor
mono.androidx.transition.Transition_TransitionListenerImplementor;mono.androidx.transition.Transition_TransitionListenerImplementor
AndroidX.Transitions.Visibility+IMode, Xamarin.AndroidX.Transition;androidx.transition.Visibility$Mode
AndroidX.Transitions.Visibility.IMode;androidx.transition.Visibility$Mode
androidx.transition.Visibility$Mode;androidx.transition.Visibility$Mode
AndroidX.VectorDrawable.Graphics.Drawable.IAnimatable2Compat, Xamarin.AndroidX.VectorDrawable.Animated;androidx.vectordrawable.graphics.drawable.Animatable2Compat
AndroidX.VectorDrawable.Graphics.Drawable.IAnimatable2Compat;androidx.vectordrawable.graphics.drawable.Animatable2Compat
androidx.vectordrawable.graphics.drawable.Animatable2Compat;androidx.vectordrawable.graphics.drawable.Animatable2Compat
AndroidX.VersionedParcelable.INonParcelField, Xamarin.AndroidX.VersionedParcelable;androidx.versionedparcelable.NonParcelField
AndroidX.VersionedParcelable.INonParcelField;androidx.versionedparcelable.NonParcelField
androidx.versionedparcelable.NonParcelField;androidx.versionedparcelable.NonParcelField
AndroidX.VersionedParcelable.IParcelField, Xamarin.AndroidX.VersionedParcelable;androidx.versionedparcelable.ParcelField
AndroidX.VersionedParcelable.IParcelField;androidx.versionedparcelable.ParcelField
androidx.versionedparcelable.ParcelField;androidx.versionedparcelable.ParcelField
AndroidX.VersionedParcelable.IVersionedParcelable, Xamarin.AndroidX.VersionedParcelable;androidx.versionedparcelable.VersionedParcelable
AndroidX.VersionedParcelable.IVersionedParcelable;androidx.versionedparcelable.VersionedParcelable
androidx.versionedparcelable.VersionedParcelable;androidx.versionedparcelable.VersionedParcelable
AndroidX.VersionedParcelable.IVersionedParcelize, Xamarin.AndroidX.VersionedParcelable;androidx.versionedparcelable.VersionedParcelize
AndroidX.VersionedParcelable.IVersionedParcelize;androidx.versionedparcelable.VersionedParcelize
androidx.versionedparcelable.VersionedParcelize;androidx.versionedparcelable.VersionedParcelize
AndroidX.ViewPager.Widget.ViewPager+IDecorView, Xamarin.AndroidX.ViewPager;androidx.viewpager.widget.ViewPager$DecorView
AndroidX.ViewPager.Widget.ViewPager.IDecorView;androidx.viewpager.widget.ViewPager$DecorView
androidx.viewpager.widget.ViewPager$DecorView;androidx.viewpager.widget.ViewPager$DecorView
AndroidX.ViewPager.Widget.ViewPager+IOnAdapterChangeListener, Xamarin.AndroidX.ViewPager;androidx.viewpager.widget.ViewPager$OnAdapterChangeListener
AndroidX.ViewPager.Widget.ViewPager.IOnAdapterChangeListener;androidx.viewpager.widget.ViewPager$OnAdapterChangeListener
androidx.viewpager.widget.ViewPager$OnAdapterChangeListener;androidx.viewpager.widget.ViewPager$OnAdapterChangeListener
AndroidX.ViewPager.Widget.ViewPager+IOnAdapterChangeListenerImplementor, Xamarin.AndroidX.ViewPager;mono.androidx.viewpager.widget.ViewPager_OnAdapterChangeListenerImplementor
AndroidX.ViewPager.Widget.ViewPager.IOnAdapterChangeListenerImplementor;mono.androidx.viewpager.widget.ViewPager_OnAdapterChangeListenerImplementor
mono.androidx.viewpager.widget.ViewPager_OnAdapterChangeListenerImplementor;mono.androidx.viewpager.widget.ViewPager_OnAdapterChangeListenerImplementor
AndroidX.ViewPager.Widget.ViewPager+IOnPageChangeListener, Xamarin.AndroidX.ViewPager;androidx.viewpager.widget.ViewPager$OnPageChangeListener
AndroidX.ViewPager.Widget.ViewPager.IOnPageChangeListener;androidx.viewpager.widget.ViewPager$OnPageChangeListener
androidx.viewpager.widget.ViewPager$OnPageChangeListener;androidx.viewpager.widget.ViewPager$OnPageChangeListener
AndroidX.ViewPager.Widget.ViewPager+IOnPageChangeListenerImplementor, Xamarin.AndroidX.ViewPager;mono.androidx.viewpager.widget.ViewPager_OnPageChangeListenerImplementor
AndroidX.ViewPager.Widget.ViewPager.IOnPageChangeListenerImplementor;mono.androidx.viewpager.widget.ViewPager_OnPageChangeListenerImplementor
mono.androidx.viewpager.widget.ViewPager_OnPageChangeListenerImplementor;mono.androidx.viewpager.widget.ViewPager_OnPageChangeListenerImplementor
AndroidX.ViewPager.Widget.ViewPager+IPageTransformer, Xamarin.AndroidX.ViewPager;androidx.viewpager.widget.ViewPager$PageTransformer
AndroidX.ViewPager.Widget.ViewPager.IPageTransformer;androidx.viewpager.widget.ViewPager$PageTransformer
androidx.viewpager.widget.ViewPager$PageTransformer;androidx.viewpager.widget.ViewPager$PageTransformer
AndroidX.ViewPager2.Widget.ViewPager2+IOffscreenPageLimit, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.widget.ViewPager2$OffscreenPageLimit
AndroidX.ViewPager2.Widget.ViewPager2.IOffscreenPageLimit;androidx.viewpager2.widget.ViewPager2$OffscreenPageLimit
androidx.viewpager2.widget.ViewPager2$OffscreenPageLimit;androidx.viewpager2.widget.ViewPager2$OffscreenPageLimit
AndroidX.ViewPager2.Widget.ViewPager2+IOrientation, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.widget.ViewPager2$Orientation
AndroidX.ViewPager2.Widget.ViewPager2.IOrientation;androidx.viewpager2.widget.ViewPager2$Orientation
androidx.viewpager2.widget.ViewPager2$Orientation;androidx.viewpager2.widget.ViewPager2$Orientation
AndroidX.ViewPager2.Widget.ViewPager2+IPageTransformer, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.widget.ViewPager2$PageTransformer
AndroidX.ViewPager2.Widget.ViewPager2.IPageTransformer;androidx.viewpager2.widget.ViewPager2$PageTransformer
androidx.viewpager2.widget.ViewPager2$PageTransformer;androidx.viewpager2.widget.ViewPager2$PageTransformer
AndroidX.ViewPager2.Widget.ViewPager2+IScrollState, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.widget.ViewPager2$ScrollState
AndroidX.ViewPager2.Widget.ViewPager2.IScrollState;androidx.viewpager2.widget.ViewPager2$ScrollState
androidx.viewpager2.widget.ViewPager2$ScrollState;androidx.viewpager2.widget.ViewPager2$ScrollState
AndroidX.ViewPager2.Adapter.FragmentStateAdapter+IExperimentalFragmentStateAdapterApi, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.adapter.FragmentStateAdapter$ExperimentalFragmentStateAdapterApi
AndroidX.ViewPager2.Adapter.FragmentStateAdapter.IExperimentalFragmentStateAdapterApi;androidx.viewpager2.adapter.FragmentStateAdapter$ExperimentalFragmentStateAdapterApi
androidx.viewpager2.adapter.FragmentStateAdapter$ExperimentalFragmentStateAdapterApi;androidx.viewpager2.adapter.FragmentStateAdapter$ExperimentalFragmentStateAdapterApi
AndroidX.ViewPager2.Adapter.FragmentStateAdapter+FragmentTransactionCallback+IOnPostEventListener, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.adapter.FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener
AndroidX.ViewPager2.Adapter.FragmentStateAdapter.FragmentTransactionCallback.IOnPostEventListener;androidx.viewpager2.adapter.FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener
androidx.viewpager2.adapter.FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener;androidx.viewpager2.adapter.FragmentStateAdapter$FragmentTransactionCallback$OnPostEventListener
AndroidX.ViewPager2.Adapter.FragmentStateAdapter+FragmentTransactionCallback+IOnPostEventListenerImplementor, Xamarin.AndroidX.ViewPager2;mono.androidx.viewpager2.adapter.FragmentStateAdapter_FragmentTransactionCallback_OnPostEventListenerImplementor
AndroidX.ViewPager2.Adapter.FragmentStateAdapter.FragmentTransactionCallback.IOnPostEventListenerImplementor;mono.androidx.viewpager2.adapter.FragmentStateAdapter_FragmentTransactionCallback_OnPostEventListenerImplementor
mono.androidx.viewpager2.adapter.FragmentStateAdapter_FragmentTransactionCallback_OnPostEventListenerImplementor;mono.androidx.viewpager2.adapter.FragmentStateAdapter_FragmentTransactionCallback_OnPostEventListenerImplementor
AndroidX.ViewPager2.Adapter.IStatefulAdapter, Xamarin.AndroidX.ViewPager2;androidx.viewpager2.adapter.StatefulAdapter
AndroidX.ViewPager2.Adapter.IStatefulAdapter;androidx.viewpager2.adapter.StatefulAdapter
androidx.viewpager2.adapter.StatefulAdapter;androidx.viewpager2.adapter.StatefulAdapter
AndroidX.Window.IRequiresWindowSdkExtension, Xamarin.AndroidX.Window;androidx.window.RequiresWindowSdkExtension
AndroidX.Window.IRequiresWindowSdkExtension;androidx.window.RequiresWindowSdkExtension
androidx.window.RequiresWindowSdkExtension;androidx.window.RequiresWindowSdkExtension
AndroidX.Window.Layout.IDisplayFeature, Xamarin.AndroidX.Window;androidx.window.layout.DisplayFeature
AndroidX.Window.Layout.IDisplayFeature;androidx.window.layout.DisplayFeature
androidx.window.layout.DisplayFeature;androidx.window.layout.DisplayFeature
AndroidX.Window.Layout.IFoldingFeature, Xamarin.AndroidX.Window;androidx.window.layout.FoldingFeature
AndroidX.Window.Layout.IFoldingFeature;androidx.window.layout.FoldingFeature
androidx.window.layout.FoldingFeature;androidx.window.layout.FoldingFeature
AndroidX.Window.Layout.IWindowInfoTracker, Xamarin.AndroidX.Window;androidx.window.layout.WindowInfoTracker
AndroidX.Window.Layout.IWindowInfoTracker;androidx.window.layout.WindowInfoTracker
androidx.window.layout.WindowInfoTracker;androidx.window.layout.WindowInfoTracker
AndroidX.Window.Layout.IWindowInfoTrackerDecorator, Xamarin.AndroidX.Window;androidx.window.layout.WindowInfoTrackerDecorator
AndroidX.Window.Layout.IWindowInfoTrackerDecorator;androidx.window.layout.WindowInfoTrackerDecorator
androidx.window.layout.WindowInfoTrackerDecorator;androidx.window.layout.WindowInfoTrackerDecorator
AndroidX.Window.Layout.IWindowMetricsCalculator, Xamarin.AndroidX.Window;androidx.window.layout.WindowMetricsCalculator
AndroidX.Window.Layout.IWindowMetricsCalculator;androidx.window.layout.WindowMetricsCalculator
androidx.window.layout.WindowMetricsCalculator;androidx.window.layout.WindowMetricsCalculator
AndroidX.Window.Layout.IWindowMetricsCalculatorDecorator, Xamarin.AndroidX.Window;androidx.window.layout.WindowMetricsCalculatorDecorator
AndroidX.Window.Layout.IWindowMetricsCalculatorDecorator;androidx.window.layout.WindowMetricsCalculatorDecorator
androidx.window.layout.WindowMetricsCalculatorDecorator;androidx.window.layout.WindowMetricsCalculatorDecorator
AndroidX.Window.Embedding.IEmbeddingBackend, Xamarin.AndroidX.Window;androidx.window.embedding.EmbeddingBackend
AndroidX.Window.Embedding.IEmbeddingBackend;androidx.window.embedding.EmbeddingBackend
androidx.window.embedding.EmbeddingBackend;androidx.window.embedding.EmbeddingBackend
AndroidX.Window.Embedding.IEmbeddingBackendDecorator, Xamarin.AndroidX.Window;androidx.window.embedding.EmbeddingBackendDecorator
AndroidX.Window.Embedding.IEmbeddingBackendDecorator;androidx.window.embedding.EmbeddingBackendDecorator
androidx.window.embedding.EmbeddingBackendDecorator;androidx.window.embedding.EmbeddingBackendDecorator
AndroidX.Window.Core.IExperimentalWindowApi, Xamarin.AndroidX.Window;androidx.window.core.ExperimentalWindowApi
AndroidX.Window.Core.IExperimentalWindowApi;androidx.window.core.ExperimentalWindowApi
androidx.window.core.ExperimentalWindowApi;androidx.window.core.ExperimentalWindowApi
AndroidX.Window.Area.IWindowAreaController, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaController
AndroidX.Window.Area.IWindowAreaController;androidx.window.area.WindowAreaController
androidx.window.area.WindowAreaController;androidx.window.area.WindowAreaController
AndroidX.Window.Area.IWindowAreaControllerDecorator, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaControllerDecorator
AndroidX.Window.Area.IWindowAreaControllerDecorator;androidx.window.area.WindowAreaControllerDecorator
androidx.window.area.WindowAreaControllerDecorator;androidx.window.area.WindowAreaControllerDecorator
AndroidX.Window.Area.IWindowAreaPresentationSessionCallback, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaPresentationSessionCallback
AndroidX.Window.Area.IWindowAreaPresentationSessionCallback;androidx.window.area.WindowAreaPresentationSessionCallback
androidx.window.area.WindowAreaPresentationSessionCallback;androidx.window.area.WindowAreaPresentationSessionCallback
AndroidX.Window.Area.IWindowAreaSession, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaSession
AndroidX.Window.Area.IWindowAreaSession;androidx.window.area.WindowAreaSession
androidx.window.area.WindowAreaSession;androidx.window.area.WindowAreaSession
AndroidX.Window.Area.IWindowAreaSessionCallback, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaSessionCallback
AndroidX.Window.Area.IWindowAreaSessionCallback;androidx.window.area.WindowAreaSessionCallback
androidx.window.area.WindowAreaSessionCallback;androidx.window.area.WindowAreaSessionCallback
AndroidX.Window.Area.IWindowAreaSessionPresenter, Xamarin.AndroidX.Window;androidx.window.area.WindowAreaSessionPresenter
AndroidX.Window.Area.IWindowAreaSessionPresenter;androidx.window.area.WindowAreaSessionPresenter
androidx.window.area.WindowAreaSessionPresenter;androidx.window.area.WindowAreaSessionPresenter
AndroidX.Window.Area.ReflectionGuard.IExtensionWindowAreaPresentationRequirements, Xamarin.AndroidX.Window;androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
AndroidX.Window.Area.ReflectionGuard.IExtensionWindowAreaPresentationRequirements;androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements;androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
AndroidX.Window.Area.ReflectionGuard.IExtensionWindowAreaStatusRequirements, Xamarin.AndroidX.Window;androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
AndroidX.Window.Area.ReflectionGuard.IExtensionWindowAreaStatusRequirements;androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements;androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
AndroidX.Window.Area.ReflectionGuard.IWindowAreaComponentApi2Requirements, Xamarin.AndroidX.Window;androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
AndroidX.Window.Area.ReflectionGuard.IWindowAreaComponentApi2Requirements;androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements;androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
AndroidX.Window.Area.ReflectionGuard.IWindowAreaComponentApi3Requirements, Xamarin.AndroidX.Window;androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
AndroidX.Window.Area.ReflectionGuard.IWindowAreaComponentApi3Requirements;androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements;androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
AndroidX.Window.Extensions.Core.Util.Function.IConsumer, Xamarin.AndroidX.Window.Extensions.Core.Core;androidx.window.extensions.core.util.function.Consumer
AndroidX.Window.Extensions.Core.Util.Function.IConsumer;androidx.window.extensions.core.util.function.Consumer
androidx.window.extensions.core.util.function.Consumer;androidx.window.extensions.core.util.function.Consumer
AndroidX.Window.Extensions.Core.Util.Function.IFunction, Xamarin.AndroidX.Window.Extensions.Core.Core;androidx.window.extensions.core.util.function.Function
AndroidX.Window.Extensions.Core.Util.Function.IFunction;androidx.window.extensions.core.util.function.Function
androidx.window.extensions.core.util.function.Function;androidx.window.extensions.core.util.function.Function
AndroidX.Window.Extensions.Core.Util.Function.IPredicate, Xamarin.AndroidX.Window.Extensions.Core.Core;androidx.window.extensions.core.util.function.Predicate
AndroidX.Window.Extensions.Core.Util.Function.IPredicate;androidx.window.extensions.core.util.function.Predicate
androidx.window.extensions.core.util.function.Predicate;androidx.window.extensions.core.util.function.Predicate
Google.Android.Material.Transition.IVisibilityAnimatorProvider, Xamarin.Google.Android.Material;com.google.android.material.transition.VisibilityAnimatorProvider
Google.Android.Material.Transition.IVisibilityAnimatorProvider;com.google.android.material.transition.VisibilityAnimatorProvider
com.google.android.material.transition.VisibilityAnimatorProvider;com.google.android.material.transition.VisibilityAnimatorProvider
Google.Android.Material.Transition.MaterialContainerTransform+IFadeMode, Xamarin.Google.Android.Material;com.google.android.material.transition.MaterialContainerTransform$FadeMode
Google.Android.Material.Transition.MaterialContainerTransform.IFadeMode;com.google.android.material.transition.MaterialContainerTransform$FadeMode
com.google.android.material.transition.MaterialContainerTransform$FadeMode;com.google.android.material.transition.MaterialContainerTransform$FadeMode
Google.Android.Material.Transition.MaterialContainerTransform+IFitMode, Xamarin.Google.Android.Material;com.google.android.material.transition.MaterialContainerTransform$FitMode
Google.Android.Material.Transition.MaterialContainerTransform.IFitMode;com.google.android.material.transition.MaterialContainerTransform$FitMode
com.google.android.material.transition.MaterialContainerTransform$FitMode;com.google.android.material.transition.MaterialContainerTransform$FitMode
Google.Android.Material.Transition.MaterialContainerTransform+ITransitionDirection, Xamarin.Google.Android.Material;com.google.android.material.transition.MaterialContainerTransform$TransitionDirection
Google.Android.Material.Transition.MaterialContainerTransform.ITransitionDirection;com.google.android.material.transition.MaterialContainerTransform$TransitionDirection
com.google.android.material.transition.MaterialContainerTransform$TransitionDirection;com.google.android.material.transition.MaterialContainerTransform$TransitionDirection
Google.Android.Material.Transition.MaterialSharedAxis+IAxis, Xamarin.Google.Android.Material;com.google.android.material.transition.MaterialSharedAxis$Axis
Google.Android.Material.Transition.MaterialSharedAxis.IAxis;com.google.android.material.transition.MaterialSharedAxis$Axis
com.google.android.material.transition.MaterialSharedAxis$Axis;com.google.android.material.transition.MaterialSharedAxis$Axis
Google.Android.Material.Transition.SlideDistanceProvider+IGravityFlag, Xamarin.Google.Android.Material;com.google.android.material.transition.SlideDistanceProvider$GravityFlag
Google.Android.Material.Transition.SlideDistanceProvider.IGravityFlag;com.google.android.material.transition.SlideDistanceProvider$GravityFlag
com.google.android.material.transition.SlideDistanceProvider$GravityFlag;com.google.android.material.transition.SlideDistanceProvider$GravityFlag
Google.Android.Material.Transition.Platform.IVisibilityAnimatorProvider, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.VisibilityAnimatorProvider
Google.Android.Material.Transition.Platform.IVisibilityAnimatorProvider;com.google.android.material.transition.platform.VisibilityAnimatorProvider
com.google.android.material.transition.platform.VisibilityAnimatorProvider;com.google.android.material.transition.platform.VisibilityAnimatorProvider
Google.Android.Material.Transition.Platform.MaterialContainerTransform+IFadeMode, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.MaterialContainerTransform$FadeMode
Google.Android.Material.Transition.Platform.MaterialContainerTransform.IFadeMode;com.google.android.material.transition.platform.MaterialContainerTransform$FadeMode
com.google.android.material.transition.platform.MaterialContainerTransform$FadeMode;com.google.android.material.transition.platform.MaterialContainerTransform$FadeMode
Google.Android.Material.Transition.Platform.MaterialContainerTransform+IFitMode, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.MaterialContainerTransform$FitMode
Google.Android.Material.Transition.Platform.MaterialContainerTransform.IFitMode;com.google.android.material.transition.platform.MaterialContainerTransform$FitMode
com.google.android.material.transition.platform.MaterialContainerTransform$FitMode;com.google.android.material.transition.platform.MaterialContainerTransform$FitMode
Google.Android.Material.Transition.Platform.MaterialContainerTransform+ITransitionDirection, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.MaterialContainerTransform$TransitionDirection
Google.Android.Material.Transition.Platform.MaterialContainerTransform.ITransitionDirection;com.google.android.material.transition.platform.MaterialContainerTransform$TransitionDirection
com.google.android.material.transition.platform.MaterialContainerTransform$TransitionDirection;com.google.android.material.transition.platform.MaterialContainerTransform$TransitionDirection
Google.Android.Material.Transition.Platform.MaterialContainerTransformSharedElementCallback+IShapeProvider, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.MaterialContainerTransformSharedElementCallback$ShapeProvider
Google.Android.Material.Transition.Platform.MaterialContainerTransformSharedElementCallback.IShapeProvider;com.google.android.material.transition.platform.MaterialContainerTransformSharedElementCallback$ShapeProvider
com.google.android.material.transition.platform.MaterialContainerTransformSharedElementCallback$ShapeProvider;com.google.android.material.transition.platform.MaterialContainerTransformSharedElementCallback$ShapeProvider
Google.Android.Material.Transition.Platform.MaterialSharedAxis+IAxis, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.MaterialSharedAxis$Axis
Google.Android.Material.Transition.Platform.MaterialSharedAxis.IAxis;com.google.android.material.transition.platform.MaterialSharedAxis$Axis
com.google.android.material.transition.platform.MaterialSharedAxis$Axis;com.google.android.material.transition.platform.MaterialSharedAxis$Axis
Google.Android.Material.Transition.Platform.SlideDistanceProvider+IGravityFlag, Xamarin.Google.Android.Material;com.google.android.material.transition.platform.SlideDistanceProvider$GravityFlag
Google.Android.Material.Transition.Platform.SlideDistanceProvider.IGravityFlag;com.google.android.material.transition.platform.SlideDistanceProvider$GravityFlag
com.google.android.material.transition.platform.SlideDistanceProvider$GravityFlag;com.google.android.material.transition.platform.SlideDistanceProvider$GravityFlag
Google.Android.Material.TimePicker.ITimeFormat, Xamarin.Google.Android.Material;com.google.android.material.timepicker.TimeFormat
Google.Android.Material.TimePicker.ITimeFormat;com.google.android.material.timepicker.TimeFormat
com.google.android.material.timepicker.TimeFormat;com.google.android.material.timepicker.TimeFormat
Google.Android.Material.Slider.IBaseOnChangeListener, Xamarin.Google.Android.Material;com.google.android.material.slider.BaseOnChangeListener
Google.Android.Material.Slider.IBaseOnChangeListener;com.google.android.material.slider.BaseOnChangeListener
com.google.android.material.slider.BaseOnChangeListener;com.google.android.material.slider.BaseOnChangeListener
Google.Android.Material.Slider.IBaseOnChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.slider.BaseOnChangeListenerImplementor
Google.Android.Material.Slider.IBaseOnChangeListenerImplementor;mono.com.google.android.material.slider.BaseOnChangeListenerImplementor
mono.com.google.android.material.slider.BaseOnChangeListenerImplementor;mono.com.google.android.material.slider.BaseOnChangeListenerImplementor
Google.Android.Material.Slider.IBaseOnSliderTouchListener, Xamarin.Google.Android.Material;com.google.android.material.slider.BaseOnSliderTouchListener
Google.Android.Material.Slider.IBaseOnSliderTouchListener;com.google.android.material.slider.BaseOnSliderTouchListener
com.google.android.material.slider.BaseOnSliderTouchListener;com.google.android.material.slider.BaseOnSliderTouchListener
Google.Android.Material.Slider.IBaseOnSliderTouchListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.slider.BaseOnSliderTouchListenerImplementor
Google.Android.Material.Slider.IBaseOnSliderTouchListenerImplementor;mono.com.google.android.material.slider.BaseOnSliderTouchListenerImplementor
mono.com.google.android.material.slider.BaseOnSliderTouchListenerImplementor;mono.com.google.android.material.slider.BaseOnSliderTouchListenerImplementor
Google.Android.Material.Slider.ILabelFormatter, Xamarin.Google.Android.Material;com.google.android.material.slider.LabelFormatter
Google.Android.Material.Slider.ILabelFormatter;com.google.android.material.slider.LabelFormatter
com.google.android.material.slider.LabelFormatter;com.google.android.material.slider.LabelFormatter
Google.Android.Material.Slider.RangeSlider+IOnChangeListener, Xamarin.Google.Android.Material;com.google.android.material.slider.RangeSlider$OnChangeListener
Google.Android.Material.Slider.RangeSlider.IOnChangeListener;com.google.android.material.slider.RangeSlider$OnChangeListener
com.google.android.material.slider.RangeSlider$OnChangeListener;com.google.android.material.slider.RangeSlider$OnChangeListener
Google.Android.Material.Slider.RangeSlider+IOnSliderTouchListener, Xamarin.Google.Android.Material;com.google.android.material.slider.RangeSlider$OnSliderTouchListener
Google.Android.Material.Slider.RangeSlider.IOnSliderTouchListener;com.google.android.material.slider.RangeSlider$OnSliderTouchListener
com.google.android.material.slider.RangeSlider$OnSliderTouchListener;com.google.android.material.slider.RangeSlider$OnSliderTouchListener
Google.Android.Material.Slider.Slider+IOnChangeListener, Xamarin.Google.Android.Material;com.google.android.material.slider.Slider$OnChangeListener
Google.Android.Material.Slider.Slider.IOnChangeListener;com.google.android.material.slider.Slider$OnChangeListener
com.google.android.material.slider.Slider$OnChangeListener;com.google.android.material.slider.Slider$OnChangeListener
Google.Android.Material.Slider.Slider+IOnSliderTouchListener, Xamarin.Google.Android.Material;com.google.android.material.slider.Slider$OnSliderTouchListener
Google.Android.Material.Slider.Slider.IOnSliderTouchListener;com.google.android.material.slider.Slider$OnSliderTouchListener
com.google.android.material.slider.Slider$OnSliderTouchListener;com.google.android.material.slider.Slider$OnSliderTouchListener
Google.Android.Material.Shape.ICornerFamily, Xamarin.Google.Android.Material;com.google.android.material.shape.CornerFamily
Google.Android.Material.Shape.ICornerFamily;com.google.android.material.shape.CornerFamily
com.google.android.material.shape.CornerFamily;com.google.android.material.shape.CornerFamily
Google.Android.Material.Shape.ICornerSize, Xamarin.Google.Android.Material;com.google.android.material.shape.CornerSize
Google.Android.Material.Shape.ICornerSize;com.google.android.material.shape.CornerSize
com.google.android.material.shape.CornerSize;com.google.android.material.shape.CornerSize
Google.Android.Material.Shape.IShapeable, Xamarin.Google.Android.Material;com.google.android.material.shape.Shapeable
Google.Android.Material.Shape.IShapeable;com.google.android.material.shape.Shapeable
com.google.android.material.shape.Shapeable;com.google.android.material.shape.Shapeable
Google.Android.Material.Shape.MaterialShapeDrawable+ICompatibilityShadowMode, Xamarin.Google.Android.Material;com.google.android.material.shape.MaterialShapeDrawable$CompatibilityShadowMode
Google.Android.Material.Shape.MaterialShapeDrawable.ICompatibilityShadowMode;com.google.android.material.shape.MaterialShapeDrawable$CompatibilityShadowMode
com.google.android.material.shape.MaterialShapeDrawable$CompatibilityShadowMode;com.google.android.material.shape.MaterialShapeDrawable$CompatibilityShadowMode
Google.Android.Material.Shape.ShapeAppearanceModel+ICornerSizeUnaryOperator, Xamarin.Google.Android.Material;com.google.android.material.shape.ShapeAppearanceModel$CornerSizeUnaryOperator
Google.Android.Material.Shape.ShapeAppearanceModel.ICornerSizeUnaryOperator;com.google.android.material.shape.ShapeAppearanceModel$CornerSizeUnaryOperator
com.google.android.material.shape.ShapeAppearanceModel$CornerSizeUnaryOperator;com.google.android.material.shape.ShapeAppearanceModel$CornerSizeUnaryOperator
Google.Android.Material.Shape.ShapeAppearancePathProvider+IPathListener, Xamarin.Google.Android.Material;com.google.android.material.shape.ShapeAppearancePathProvider$PathListener
Google.Android.Material.Shape.ShapeAppearancePathProvider.IPathListener;com.google.android.material.shape.ShapeAppearancePathProvider$PathListener
com.google.android.material.shape.ShapeAppearancePathProvider$PathListener;com.google.android.material.shape.ShapeAppearancePathProvider$PathListener
Google.Android.Material.Shape.ShapeAppearancePathProvider+IPathListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.shape.ShapeAppearancePathProvider_PathListenerImplementor
Google.Android.Material.Shape.ShapeAppearancePathProvider.IPathListenerImplementor;mono.com.google.android.material.shape.ShapeAppearancePathProvider_PathListenerImplementor
mono.com.google.android.material.shape.ShapeAppearancePathProvider_PathListenerImplementor;mono.com.google.android.material.shape.ShapeAppearancePathProvider_PathListenerImplementor
Google.Android.Material.Shadow.IShadowViewDelegate, Xamarin.Google.Android.Material;com.google.android.material.shadow.ShadowViewDelegate
Google.Android.Material.Shadow.IShadowViewDelegate;com.google.android.material.shadow.ShadowViewDelegate
com.google.android.material.shadow.ShadowViewDelegate;com.google.android.material.shadow.ShadowViewDelegate
Google.Android.Material.Search.SearchView+ITransitionListener, Xamarin.Google.Android.Material;com.google.android.material.search.SearchView$TransitionListener
Google.Android.Material.Search.SearchView.ITransitionListener;com.google.android.material.search.SearchView$TransitionListener
com.google.android.material.search.SearchView$TransitionListener;com.google.android.material.search.SearchView$TransitionListener
Google.Android.Material.Search.SearchView+ITransitionListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.search.SearchView_TransitionListenerImplementor
Google.Android.Material.Search.SearchView.ITransitionListenerImplementor;mono.com.google.android.material.search.SearchView_TransitionListenerImplementor
mono.com.google.android.material.search.SearchView_TransitionListenerImplementor;mono.com.google.android.material.search.SearchView_TransitionListenerImplementor
Google.Android.Material.Resources.CancelableFontCallback+IApplyFont, Xamarin.Google.Android.Material;com.google.android.material.resources.CancelableFontCallback$ApplyFont
Google.Android.Material.Resources.CancelableFontCallback.IApplyFont;com.google.android.material.resources.CancelableFontCallback$ApplyFont
com.google.android.material.resources.CancelableFontCallback$ApplyFont;com.google.android.material.resources.CancelableFontCallback$ApplyFont
Google.Android.Material.ProgressIndicator.BaseProgressIndicator+IHideAnimationBehavior, Xamarin.Google.Android.Material;com.google.android.material.progressindicator.BaseProgressIndicator$HideAnimationBehavior
Google.Android.Material.ProgressIndicator.BaseProgressIndicator.IHideAnimationBehavior;com.google.android.material.progressindicator.BaseProgressIndicator$HideAnimationBehavior
com.google.android.material.progressindicator.BaseProgressIndicator$HideAnimationBehavior;com.google.android.material.progressindicator.BaseProgressIndicator$HideAnimationBehavior
Google.Android.Material.ProgressIndicator.BaseProgressIndicator+IShowAnimationBehavior, Xamarin.Google.Android.Material;com.google.android.material.progressindicator.BaseProgressIndicator$ShowAnimationBehavior
Google.Android.Material.ProgressIndicator.BaseProgressIndicator.IShowAnimationBehavior;com.google.android.material.progressindicator.BaseProgressIndicator$ShowAnimationBehavior
com.google.android.material.progressindicator.BaseProgressIndicator$ShowAnimationBehavior;com.google.android.material.progressindicator.BaseProgressIndicator$ShowAnimationBehavior
Google.Android.Material.ProgressIndicator.CircularProgressIndicator+IIndicatorDirection, Xamarin.Google.Android.Material;com.google.android.material.progressindicator.CircularProgressIndicator$IndicatorDirection
Google.Android.Material.ProgressIndicator.CircularProgressIndicator.IIndicatorDirection;com.google.android.material.progressindicator.CircularProgressIndicator$IndicatorDirection
com.google.android.material.progressindicator.CircularProgressIndicator$IndicatorDirection;com.google.android.material.progressindicator.CircularProgressIndicator$IndicatorDirection
Google.Android.Material.ProgressIndicator.LinearProgressIndicator+IIndeterminateAnimationType, Xamarin.Google.Android.Material;com.google.android.material.progressindicator.LinearProgressIndicator$IndeterminateAnimationType
Google.Android.Material.ProgressIndicator.LinearProgressIndicator.IIndeterminateAnimationType;com.google.android.material.progressindicator.LinearProgressIndicator$IndeterminateAnimationType
com.google.android.material.progressindicator.LinearProgressIndicator$IndeterminateAnimationType;com.google.android.material.progressindicator.LinearProgressIndicator$IndeterminateAnimationType
Google.Android.Material.ProgressIndicator.LinearProgressIndicator+IIndicatorDirection, Xamarin.Google.Android.Material;com.google.android.material.progressindicator.LinearProgressIndicator$IndicatorDirection
Google.Android.Material.ProgressIndicator.LinearProgressIndicator.IIndicatorDirection;com.google.android.material.progressindicator.LinearProgressIndicator$IndicatorDirection
com.google.android.material.progressindicator.LinearProgressIndicator$IndicatorDirection;com.google.android.material.progressindicator.LinearProgressIndicator$IndicatorDirection
Google.Android.Material.Motion.IMaterialBackHandler, Xamarin.Google.Android.Material;com.google.android.material.motion.MaterialBackHandler
Google.Android.Material.Motion.IMaterialBackHandler;com.google.android.material.motion.MaterialBackHandler
com.google.android.material.motion.MaterialBackHandler;com.google.android.material.motion.MaterialBackHandler
Google.Android.Material.FloatingActionButton.FloatingActionButton+ISize, Xamarin.Google.Android.Material;com.google.android.material.floatingactionbutton.FloatingActionButton$Size
Google.Android.Material.FloatingActionButton.FloatingActionButton.ISize;com.google.android.material.floatingactionbutton.FloatingActionButton$Size
com.google.android.material.floatingactionbutton.FloatingActionButton$Size;com.google.android.material.floatingactionbutton.FloatingActionButton$Size
Google.Android.Material.Expandable.IExpandableTransformationWidget, Xamarin.Google.Android.Material;com.google.android.material.expandable.ExpandableTransformationWidget
Google.Android.Material.Expandable.IExpandableTransformationWidget;com.google.android.material.expandable.ExpandableTransformationWidget
com.google.android.material.expandable.ExpandableTransformationWidget;com.google.android.material.expandable.ExpandableTransformationWidget
Google.Android.Material.Expandable.IExpandableWidget, Xamarin.Google.Android.Material;com.google.android.material.expandable.ExpandableWidget
Google.Android.Material.Expandable.IExpandableWidget;com.google.android.material.expandable.ExpandableWidget
com.google.android.material.expandable.ExpandableWidget;com.google.android.material.expandable.ExpandableWidget
Google.Android.Material.Color.DynamicColors+IOnAppliedCallback, Xamarin.Google.Android.Material;com.google.android.material.color.DynamicColors$OnAppliedCallback
Google.Android.Material.Color.DynamicColors.IOnAppliedCallback;com.google.android.material.color.DynamicColors$OnAppliedCallback
com.google.android.material.color.DynamicColors$OnAppliedCallback;com.google.android.material.color.DynamicColors$OnAppliedCallback
Google.Android.Material.Color.DynamicColors+IPrecondition, Xamarin.Google.Android.Material;com.google.android.material.color.DynamicColors$Precondition
Google.Android.Material.Color.DynamicColors.IPrecondition;com.google.android.material.color.DynamicColors$Precondition
com.google.android.material.color.DynamicColors$Precondition;com.google.android.material.color.DynamicColors$Precondition
Google.Android.Material.Color.IColorResourcesOverride, Xamarin.Google.Android.Material;com.google.android.material.color.ColorResourcesOverride
Google.Android.Material.Color.IColorResourcesOverride;com.google.android.material.color.ColorResourcesOverride
com.google.android.material.color.ColorResourcesOverride;com.google.android.material.color.ColorResourcesOverride
Google.Android.Material.Color.Utilities.IPointProvider, Xamarin.Google.Android.Material;com.google.android.material.color.utilities.PointProvider
Google.Android.Material.Color.Utilities.IPointProvider;com.google.android.material.color.utilities.PointProvider
com.google.android.material.color.utilities.PointProvider;com.google.android.material.color.utilities.PointProvider
Google.Android.Material.Chip.ChipDrawable+IDelegate, Xamarin.Google.Android.Material;com.google.android.material.chip.ChipDrawable$Delegate
Google.Android.Material.Chip.ChipDrawable.IDelegate;com.google.android.material.chip.ChipDrawable$Delegate
com.google.android.material.chip.ChipDrawable$Delegate;com.google.android.material.chip.ChipDrawable$Delegate
Google.Android.Material.Chip.ChipGroup+IOnCheckedChangeListener, Xamarin.Google.Android.Material;com.google.android.material.chip.ChipGroup$OnCheckedChangeListener
Google.Android.Material.Chip.ChipGroup.IOnCheckedChangeListener;com.google.android.material.chip.ChipGroup$OnCheckedChangeListener
com.google.android.material.chip.ChipGroup$OnCheckedChangeListener;com.google.android.material.chip.ChipGroup$OnCheckedChangeListener
Google.Android.Material.Chip.ChipGroup+IOnCheckedChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.chip.ChipGroup_OnCheckedChangeListenerImplementor
Google.Android.Material.Chip.ChipGroup.IOnCheckedChangeListenerImplementor;mono.com.google.android.material.chip.ChipGroup_OnCheckedChangeListenerImplementor
mono.com.google.android.material.chip.ChipGroup_OnCheckedChangeListenerImplementor;mono.com.google.android.material.chip.ChipGroup_OnCheckedChangeListenerImplementor
Google.Android.Material.Chip.ChipGroup+IOnCheckedStateChangeListener, Xamarin.Google.Android.Material;com.google.android.material.chip.ChipGroup$OnCheckedStateChangeListener
Google.Android.Material.Chip.ChipGroup.IOnCheckedStateChangeListener;com.google.android.material.chip.ChipGroup$OnCheckedStateChangeListener
com.google.android.material.chip.ChipGroup$OnCheckedStateChangeListener;com.google.android.material.chip.ChipGroup$OnCheckedStateChangeListener
Google.Android.Material.Chip.ChipGroup+IOnCheckedStateChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.chip.ChipGroup_OnCheckedStateChangeListenerImplementor
Google.Android.Material.Chip.ChipGroup.IOnCheckedStateChangeListenerImplementor;mono.com.google.android.material.chip.ChipGroup_OnCheckedStateChangeListenerImplementor
mono.com.google.android.material.chip.ChipGroup_OnCheckedStateChangeListenerImplementor;mono.com.google.android.material.chip.ChipGroup_OnCheckedStateChangeListenerImplementor
Google.Android.Material.CheckBox.MaterialCheckBox+ICheckedState, Xamarin.Google.Android.Material;com.google.android.material.checkbox.MaterialCheckBox$CheckedState
Google.Android.Material.CheckBox.MaterialCheckBox.ICheckedState;com.google.android.material.checkbox.MaterialCheckBox$CheckedState
com.google.android.material.checkbox.MaterialCheckBox$CheckedState;com.google.android.material.checkbox.MaterialCheckBox$CheckedState
Google.Android.Material.CheckBox.MaterialCheckBox+IOnCheckedStateChangedListener, Xamarin.Google.Android.Material;com.google.android.material.checkbox.MaterialCheckBox$OnCheckedStateChangedListener
Google.Android.Material.CheckBox.MaterialCheckBox.IOnCheckedStateChangedListener;com.google.android.material.checkbox.MaterialCheckBox$OnCheckedStateChangedListener
com.google.android.material.checkbox.MaterialCheckBox$OnCheckedStateChangedListener;com.google.android.material.checkbox.MaterialCheckBox$OnCheckedStateChangedListener
Google.Android.Material.CheckBox.MaterialCheckBox+IOnCheckedStateChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.checkbox.MaterialCheckBox_OnCheckedStateChangedListenerImplementor
Google.Android.Material.CheckBox.MaterialCheckBox.IOnCheckedStateChangedListenerImplementor;mono.com.google.android.material.checkbox.MaterialCheckBox_OnCheckedStateChangedListenerImplementor
mono.com.google.android.material.checkbox.MaterialCheckBox_OnCheckedStateChangedListenerImplementor;mono.com.google.android.material.checkbox.MaterialCheckBox_OnCheckedStateChangedListenerImplementor
Google.Android.Material.CheckBox.MaterialCheckBox+IOnErrorChangedListener, Xamarin.Google.Android.Material;com.google.android.material.checkbox.MaterialCheckBox$OnErrorChangedListener
Google.Android.Material.CheckBox.MaterialCheckBox.IOnErrorChangedListener;com.google.android.material.checkbox.MaterialCheckBox$OnErrorChangedListener
com.google.android.material.checkbox.MaterialCheckBox$OnErrorChangedListener;com.google.android.material.checkbox.MaterialCheckBox$OnErrorChangedListener
Google.Android.Material.CheckBox.MaterialCheckBox+IOnErrorChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.checkbox.MaterialCheckBox_OnErrorChangedListenerImplementor
Google.Android.Material.CheckBox.MaterialCheckBox.IOnErrorChangedListenerImplementor;mono.com.google.android.material.checkbox.MaterialCheckBox_OnErrorChangedListenerImplementor
mono.com.google.android.material.checkbox.MaterialCheckBox_OnErrorChangedListenerImplementor;mono.com.google.android.material.checkbox.MaterialCheckBox_OnErrorChangedListenerImplementor
Google.Android.Material.Carousel.IOnMaskChangedListener, Xamarin.Google.Android.Material;com.google.android.material.carousel.OnMaskChangedListener
Google.Android.Material.Carousel.IOnMaskChangedListener;com.google.android.material.carousel.OnMaskChangedListener
com.google.android.material.carousel.OnMaskChangedListener;com.google.android.material.carousel.OnMaskChangedListener
Google.Android.Material.Carousel.IOnMaskChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.carousel.OnMaskChangedListenerImplementor
Google.Android.Material.Carousel.IOnMaskChangedListenerImplementor;mono.com.google.android.material.carousel.OnMaskChangedListenerImplementor
mono.com.google.android.material.carousel.OnMaskChangedListenerImplementor;mono.com.google.android.material.carousel.OnMaskChangedListenerImplementor
Google.Android.Material.Card.MaterialCardView+ICheckedIconGravity, Xamarin.Google.Android.Material;com.google.android.material.card.MaterialCardView$CheckedIconGravity
Google.Android.Material.Card.MaterialCardView.ICheckedIconGravity;com.google.android.material.card.MaterialCardView$CheckedIconGravity
com.google.android.material.card.MaterialCardView$CheckedIconGravity;com.google.android.material.card.MaterialCardView$CheckedIconGravity
Google.Android.Material.Card.MaterialCardView+IOnCheckedChangeListener, Xamarin.Google.Android.Material;com.google.android.material.card.MaterialCardView$OnCheckedChangeListener
Google.Android.Material.Card.MaterialCardView.IOnCheckedChangeListener;com.google.android.material.card.MaterialCardView$OnCheckedChangeListener
com.google.android.material.card.MaterialCardView$OnCheckedChangeListener;com.google.android.material.card.MaterialCardView$OnCheckedChangeListener
Google.Android.Material.Card.MaterialCardView+IOnCheckedChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.card.MaterialCardView_OnCheckedChangeListenerImplementor
Google.Android.Material.Card.MaterialCardView.IOnCheckedChangeListenerImplementor;mono.com.google.android.material.card.MaterialCardView_OnCheckedChangeListenerImplementor
mono.com.google.android.material.card.MaterialCardView_OnCheckedChangeListenerImplementor;mono.com.google.android.material.card.MaterialCardView_OnCheckedChangeListenerImplementor
Google.Android.Material.Canvas.CanvasCompat+ICanvasOperation, Xamarin.Google.Android.Material;com.google.android.material.canvas.CanvasCompat$CanvasOperation
Google.Android.Material.Canvas.CanvasCompat.ICanvasOperation;com.google.android.material.canvas.CanvasCompat$CanvasOperation
com.google.android.material.canvas.CanvasCompat$CanvasOperation;com.google.android.material.canvas.CanvasCompat$CanvasOperation
Google.Android.Material.Button.MaterialButton+IIconGravity, Xamarin.Google.Android.Material;com.google.android.material.button.MaterialButton$IconGravity
Google.Android.Material.Button.MaterialButton.IIconGravity;com.google.android.material.button.MaterialButton$IconGravity
com.google.android.material.button.MaterialButton$IconGravity;com.google.android.material.button.MaterialButton$IconGravity
Google.Android.Material.Button.MaterialButton+IOnCheckedChangeListener, Xamarin.Google.Android.Material;com.google.android.material.button.MaterialButton$OnCheckedChangeListener
Google.Android.Material.Button.MaterialButton.IOnCheckedChangeListener;com.google.android.material.button.MaterialButton$OnCheckedChangeListener
com.google.android.material.button.MaterialButton$OnCheckedChangeListener;com.google.android.material.button.MaterialButton$OnCheckedChangeListener
Google.Android.Material.Button.MaterialButton+IOnCheckedChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.button.MaterialButton_OnCheckedChangeListenerImplementor
Google.Android.Material.Button.MaterialButton.IOnCheckedChangeListenerImplementor;mono.com.google.android.material.button.MaterialButton_OnCheckedChangeListenerImplementor
mono.com.google.android.material.button.MaterialButton_OnCheckedChangeListenerImplementor;mono.com.google.android.material.button.MaterialButton_OnCheckedChangeListenerImplementor
Google.Android.Material.Button.MaterialButtonToggleGroup+IOnButtonCheckedListener, Xamarin.Google.Android.Material;com.google.android.material.button.MaterialButtonToggleGroup$OnButtonCheckedListener
Google.Android.Material.Button.MaterialButtonToggleGroup.IOnButtonCheckedListener;com.google.android.material.button.MaterialButtonToggleGroup$OnButtonCheckedListener
com.google.android.material.button.MaterialButtonToggleGroup$OnButtonCheckedListener;com.google.android.material.button.MaterialButtonToggleGroup$OnButtonCheckedListener
Google.Android.Material.Button.MaterialButtonToggleGroup+IOnButtonCheckedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.button.MaterialButtonToggleGroup_OnButtonCheckedListenerImplementor
Google.Android.Material.Button.MaterialButtonToggleGroup.IOnButtonCheckedListenerImplementor;mono.com.google.android.material.button.MaterialButtonToggleGroup_OnButtonCheckedListenerImplementor
mono.com.google.android.material.button.MaterialButtonToggleGroup_OnButtonCheckedListenerImplementor;mono.com.google.android.material.button.MaterialButtonToggleGroup_OnButtonCheckedListenerImplementor
Google.Android.Material.BottomSheet.BottomSheetBehavior+ISaveFlags, Xamarin.Google.Android.Material;com.google.android.material.bottomsheet.BottomSheetBehavior$SaveFlags
Google.Android.Material.BottomSheet.BottomSheetBehavior.ISaveFlags;com.google.android.material.bottomsheet.BottomSheetBehavior$SaveFlags
com.google.android.material.bottomsheet.BottomSheetBehavior$SaveFlags;com.google.android.material.bottomsheet.BottomSheetBehavior$SaveFlags
Google.Android.Material.BottomSheet.BottomSheetBehavior+IStableState, Xamarin.Google.Android.Material;com.google.android.material.bottomsheet.BottomSheetBehavior$StableState
Google.Android.Material.BottomSheet.BottomSheetBehavior.IStableState;com.google.android.material.bottomsheet.BottomSheetBehavior$StableState
com.google.android.material.bottomsheet.BottomSheetBehavior$StableState;com.google.android.material.bottomsheet.BottomSheetBehavior$StableState
Google.Android.Material.BottomSheet.BottomSheetBehavior+IState, Xamarin.Google.Android.Material;com.google.android.material.bottomsheet.BottomSheetBehavior$State
Google.Android.Material.BottomSheet.BottomSheetBehavior.IState;com.google.android.material.bottomsheet.BottomSheetBehavior$State
com.google.android.material.bottomsheet.BottomSheetBehavior$State;com.google.android.material.bottomsheet.BottomSheetBehavior$State
Google.Android.Material.BottomAppBar.BottomAppBar+IFabAlignmentMode, Xamarin.Google.Android.Material;com.google.android.material.bottomappbar.BottomAppBar$FabAlignmentMode
Google.Android.Material.BottomAppBar.BottomAppBar.IFabAlignmentMode;com.google.android.material.bottomappbar.BottomAppBar$FabAlignmentMode
com.google.android.material.bottomappbar.BottomAppBar$FabAlignmentMode;com.google.android.material.bottomappbar.BottomAppBar$FabAlignmentMode
Google.Android.Material.BottomAppBar.BottomAppBar+IFabAnchorMode, Xamarin.Google.Android.Material;com.google.android.material.bottomappbar.BottomAppBar$FabAnchorMode
Google.Android.Material.BottomAppBar.BottomAppBar.IFabAnchorMode;com.google.android.material.bottomappbar.BottomAppBar$FabAnchorMode
com.google.android.material.bottomappbar.BottomAppBar$FabAnchorMode;com.google.android.material.bottomappbar.BottomAppBar$FabAnchorMode
Google.Android.Material.BottomAppBar.BottomAppBar+IFabAnimationMode, Xamarin.Google.Android.Material;com.google.android.material.bottomappbar.BottomAppBar$FabAnimationMode
Google.Android.Material.BottomAppBar.BottomAppBar.IFabAnimationMode;com.google.android.material.bottomappbar.BottomAppBar$FabAnimationMode
com.google.android.material.bottomappbar.BottomAppBar$FabAnimationMode;com.google.android.material.bottomappbar.BottomAppBar$FabAnimationMode
Google.Android.Material.BottomAppBar.BottomAppBar+IMenuAlignmentMode, Xamarin.Google.Android.Material;com.google.android.material.bottomappbar.BottomAppBar$MenuAlignmentMode
Google.Android.Material.BottomAppBar.BottomAppBar.IMenuAlignmentMode;com.google.android.material.bottomappbar.BottomAppBar$MenuAlignmentMode
com.google.android.material.bottomappbar.BottomAppBar$MenuAlignmentMode;com.google.android.material.bottomappbar.BottomAppBar$MenuAlignmentMode
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior+IOnScrollStateChangedListener, Xamarin.Google.Android.Material;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$OnScrollStateChangedListener
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior.IOnScrollStateChangedListener;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$OnScrollStateChangedListener
com.google.android.material.behavior.HideBottomViewOnScrollBehavior$OnScrollStateChangedListener;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$OnScrollStateChangedListener
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior+IOnScrollStateChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.behavior.HideBottomViewOnScrollBehavior_OnScrollStateChangedListenerImplementor
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior.IOnScrollStateChangedListenerImplementor;mono.com.google.android.material.behavior.HideBottomViewOnScrollBehavior_OnScrollStateChangedListenerImplementor
mono.com.google.android.material.behavior.HideBottomViewOnScrollBehavior_OnScrollStateChangedListenerImplementor;mono.com.google.android.material.behavior.HideBottomViewOnScrollBehavior_OnScrollStateChangedListenerImplementor
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior+IScrollState, Xamarin.Google.Android.Material;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$ScrollState
Google.Android.Material.Behavior.HideBottomViewOnScrollBehavior.IScrollState;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$ScrollState
com.google.android.material.behavior.HideBottomViewOnScrollBehavior$ScrollState;com.google.android.material.behavior.HideBottomViewOnScrollBehavior$ScrollState
Google.Android.Material.Behavior.SwipeDismissBehavior+IOnDismissListener, Xamarin.Google.Android.Material;com.google.android.material.behavior.SwipeDismissBehavior$OnDismissListener
Google.Android.Material.Behavior.SwipeDismissBehavior.IOnDismissListener;com.google.android.material.behavior.SwipeDismissBehavior$OnDismissListener
com.google.android.material.behavior.SwipeDismissBehavior$OnDismissListener;com.google.android.material.behavior.SwipeDismissBehavior$OnDismissListener
Google.Android.Material.Behavior.SwipeDismissBehavior+IOnDismissListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.behavior.SwipeDismissBehavior_OnDismissListenerImplementor
Google.Android.Material.Behavior.SwipeDismissBehavior.IOnDismissListenerImplementor;mono.com.google.android.material.behavior.SwipeDismissBehavior_OnDismissListenerImplementor
mono.com.google.android.material.behavior.SwipeDismissBehavior_OnDismissListenerImplementor;mono.com.google.android.material.behavior.SwipeDismissBehavior_OnDismissListenerImplementor
Google.Android.Material.Badge.BadgeDrawable+IBadgeGravity, Xamarin.Google.Android.Material;com.google.android.material.badge.BadgeDrawable$BadgeGravity
Google.Android.Material.Badge.BadgeDrawable.IBadgeGravity;com.google.android.material.badge.BadgeDrawable$BadgeGravity
com.google.android.material.badge.BadgeDrawable$BadgeGravity;com.google.android.material.badge.BadgeDrawable$BadgeGravity
Google.Android.Material.Badge.IExperimentalBadgeUtils, Xamarin.Google.Android.Material;com.google.android.material.badge.ExperimentalBadgeUtils
Google.Android.Material.Badge.IExperimentalBadgeUtils;com.google.android.material.badge.ExperimentalBadgeUtils
com.google.android.material.badge.ExperimentalBadgeUtils;com.google.android.material.badge.ExperimentalBadgeUtils
Google.Android.Material.DatePicker.CalendarConstraints+IDateValidator, Xamarin.Google.Android.Material;com.google.android.material.datepicker.CalendarConstraints$DateValidator
Google.Android.Material.DatePicker.CalendarConstraints.IDateValidator;com.google.android.material.datepicker.CalendarConstraints$DateValidator
com.google.android.material.datepicker.CalendarConstraints$DateValidator;com.google.android.material.datepicker.CalendarConstraints$DateValidator
Google.Android.Material.DatePicker.IDateSelector, Xamarin.Google.Android.Material;com.google.android.material.datepicker.DateSelector
Google.Android.Material.DatePicker.IDateSelector;com.google.android.material.datepicker.DateSelector
com.google.android.material.datepicker.DateSelector;com.google.android.material.datepicker.DateSelector
Google.Android.Material.DatePicker.IMaterialPickerOnPositiveButtonClickListener, Xamarin.Google.Android.Material;com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListener
Google.Android.Material.DatePicker.IMaterialPickerOnPositiveButtonClickListener;com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListener
com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListener;com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListener
Google.Android.Material.DatePicker.IMaterialPickerOnPositiveButtonClickListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListenerImplementor
Google.Android.Material.DatePicker.IMaterialPickerOnPositiveButtonClickListenerImplementor;mono.com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListenerImplementor
mono.com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListenerImplementor;mono.com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListenerImplementor
Google.Android.Material.DatePicker.MaterialDatePicker+IInputMode, Xamarin.Google.Android.Material;com.google.android.material.datepicker.MaterialDatePicker$InputMode
Google.Android.Material.DatePicker.MaterialDatePicker.IInputMode;com.google.android.material.datepicker.MaterialDatePicker$InputMode
com.google.android.material.datepicker.MaterialDatePicker$InputMode;com.google.android.material.datepicker.MaterialDatePicker$InputMode
Google.Android.Material.Navigation.NavigationBarView+ILabelVisibility, Xamarin.Google.Android.Material;com.google.android.material.navigation.NavigationBarView$LabelVisibility
Google.Android.Material.Navigation.NavigationBarView.ILabelVisibility;com.google.android.material.navigation.NavigationBarView$LabelVisibility
com.google.android.material.navigation.NavigationBarView$LabelVisibility;com.google.android.material.navigation.NavigationBarView$LabelVisibility
Google.Android.Material.Navigation.NavigationBarView+IOnItemReselectedListener, Xamarin.Google.Android.Material;com.google.android.material.navigation.NavigationBarView$OnItemReselectedListener
Google.Android.Material.Navigation.NavigationBarView.IOnItemReselectedListener;com.google.android.material.navigation.NavigationBarView$OnItemReselectedListener
com.google.android.material.navigation.NavigationBarView$OnItemReselectedListener;com.google.android.material.navigation.NavigationBarView$OnItemReselectedListener
Google.Android.Material.Navigation.NavigationBarView+IOnItemReselectedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.navigation.NavigationBarView_OnItemReselectedListenerImplementor
Google.Android.Material.Navigation.NavigationBarView.IOnItemReselectedListenerImplementor;mono.com.google.android.material.navigation.NavigationBarView_OnItemReselectedListenerImplementor
mono.com.google.android.material.navigation.NavigationBarView_OnItemReselectedListenerImplementor;mono.com.google.android.material.navigation.NavigationBarView_OnItemReselectedListenerImplementor
Google.Android.Material.Navigation.NavigationBarView+IOnItemSelectedListener, Xamarin.Google.Android.Material;com.google.android.material.navigation.NavigationBarView$OnItemSelectedListener
Google.Android.Material.Navigation.NavigationBarView.IOnItemSelectedListener;com.google.android.material.navigation.NavigationBarView$OnItemSelectedListener
com.google.android.material.navigation.NavigationBarView$OnItemSelectedListener;com.google.android.material.navigation.NavigationBarView$OnItemSelectedListener
Google.Android.Material.Navigation.NavigationBarView+IOnItemSelectedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.navigation.NavigationBarView_OnItemSelectedListenerImplementor
Google.Android.Material.Navigation.NavigationBarView.IOnItemSelectedListenerImplementor;mono.com.google.android.material.navigation.NavigationBarView_OnItemSelectedListenerImplementor
mono.com.google.android.material.navigation.NavigationBarView_OnItemSelectedListenerImplementor;mono.com.google.android.material.navigation.NavigationBarView_OnItemSelectedListenerImplementor
Google.Android.Material.Navigation.NavigationView+IOnNavigationItemSelectedListener, Xamarin.Google.Android.Material;com.google.android.material.navigation.NavigationView$OnNavigationItemSelectedListener
Google.Android.Material.Navigation.NavigationView.IOnNavigationItemSelectedListener;com.google.android.material.navigation.NavigationView$OnNavigationItemSelectedListener
com.google.android.material.navigation.NavigationView$OnNavigationItemSelectedListener;com.google.android.material.navigation.NavigationView$OnNavigationItemSelectedListener
Google.Android.Material.Navigation.NavigationView+IOnNavigationItemSelectedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.navigation.NavigationView_OnNavigationItemSelectedListenerImplementor
Google.Android.Material.Navigation.NavigationView.IOnNavigationItemSelectedListenerImplementor;mono.com.google.android.material.navigation.NavigationView_OnNavigationItemSelectedListenerImplementor
mono.com.google.android.material.navigation.NavigationView_OnNavigationItemSelectedListenerImplementor;mono.com.google.android.material.navigation.NavigationView_OnNavigationItemSelectedListenerImplementor
Google.Android.Material.TextField.TextInputLayout+IBoxBackgroundMode, Xamarin.Google.Android.Material;com.google.android.material.textfield.TextInputLayout$BoxBackgroundMode
Google.Android.Material.TextField.TextInputLayout.IBoxBackgroundMode;com.google.android.material.textfield.TextInputLayout$BoxBackgroundMode
com.google.android.material.textfield.TextInputLayout$BoxBackgroundMode;com.google.android.material.textfield.TextInputLayout$BoxBackgroundMode
Google.Android.Material.TextField.TextInputLayout+IEndIconMode, Xamarin.Google.Android.Material;com.google.android.material.textfield.TextInputLayout$EndIconMode
Google.Android.Material.TextField.TextInputLayout.IEndIconMode;com.google.android.material.textfield.TextInputLayout$EndIconMode
com.google.android.material.textfield.TextInputLayout$EndIconMode;com.google.android.material.textfield.TextInputLayout$EndIconMode
Google.Android.Material.TextField.TextInputLayout+ILengthCounter, Xamarin.Google.Android.Material;com.google.android.material.textfield.TextInputLayout$LengthCounter
Google.Android.Material.TextField.TextInputLayout.ILengthCounter;com.google.android.material.textfield.TextInputLayout$LengthCounter
com.google.android.material.textfield.TextInputLayout$LengthCounter;com.google.android.material.textfield.TextInputLayout$LengthCounter
Google.Android.Material.TextField.TextInputLayout+IOnEditTextAttachedListener, Xamarin.Google.Android.Material;com.google.android.material.textfield.TextInputLayout$OnEditTextAttachedListener
Google.Android.Material.TextField.TextInputLayout.IOnEditTextAttachedListener;com.google.android.material.textfield.TextInputLayout$OnEditTextAttachedListener
com.google.android.material.textfield.TextInputLayout$OnEditTextAttachedListener;com.google.android.material.textfield.TextInputLayout$OnEditTextAttachedListener
Google.Android.Material.TextField.TextInputLayout+IOnEditTextAttachedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.textfield.TextInputLayout_OnEditTextAttachedListenerImplementor
Google.Android.Material.TextField.TextInputLayout.IOnEditTextAttachedListenerImplementor;mono.com.google.android.material.textfield.TextInputLayout_OnEditTextAttachedListenerImplementor
mono.com.google.android.material.textfield.TextInputLayout_OnEditTextAttachedListenerImplementor;mono.com.google.android.material.textfield.TextInputLayout_OnEditTextAttachedListenerImplementor
Google.Android.Material.TextField.TextInputLayout+IOnEndIconChangedListener, Xamarin.Google.Android.Material;com.google.android.material.textfield.TextInputLayout$OnEndIconChangedListener
Google.Android.Material.TextField.TextInputLayout.IOnEndIconChangedListener;com.google.android.material.textfield.TextInputLayout$OnEndIconChangedListener
com.google.android.material.textfield.TextInputLayout$OnEndIconChangedListener;com.google.android.material.textfield.TextInputLayout$OnEndIconChangedListener
Google.Android.Material.TextField.TextInputLayout+IOnEndIconChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.textfield.TextInputLayout_OnEndIconChangedListenerImplementor
Google.Android.Material.TextField.TextInputLayout.IOnEndIconChangedListenerImplementor;mono.com.google.android.material.textfield.TextInputLayout_OnEndIconChangedListenerImplementor
mono.com.google.android.material.textfield.TextInputLayout_OnEndIconChangedListenerImplementor;mono.com.google.android.material.textfield.TextInputLayout_OnEndIconChangedListenerImplementor
Google.Android.Material.Tabs.TabLayout+IOnTabSelectedListener, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$BaseOnTabSelectedListener
Google.Android.Material.Tabs.TabLayout.IOnTabSelectedListener;com.google.android.material.tabs.TabLayout$BaseOnTabSelectedListener
com.google.android.material.tabs.TabLayout$BaseOnTabSelectedListener;com.google.android.material.tabs.TabLayout$BaseOnTabSelectedListener
Google.Android.Material.Tabs.TabLayout+IOnTabSelectedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.tabs.TabLayout_BaseOnTabSelectedListenerImplementor
Google.Android.Material.Tabs.TabLayout.IOnTabSelectedListenerImplementor;mono.com.google.android.material.tabs.TabLayout_BaseOnTabSelectedListenerImplementor
mono.com.google.android.material.tabs.TabLayout_BaseOnTabSelectedListenerImplementor;mono.com.google.android.material.tabs.TabLayout_BaseOnTabSelectedListenerImplementor
Google.Android.Material.Tabs.TabLayout+ILabelVisibility, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$LabelVisibility
Google.Android.Material.Tabs.TabLayout.ILabelVisibility;com.google.android.material.tabs.TabLayout$LabelVisibility
com.google.android.material.tabs.TabLayout$LabelVisibility;com.google.android.material.tabs.TabLayout$LabelVisibility
Google.Android.Material.Tabs.TabLayout+IMode, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$Mode
Google.Android.Material.Tabs.TabLayout.IMode;com.google.android.material.tabs.TabLayout$Mode
com.google.android.material.tabs.TabLayout$Mode;com.google.android.material.tabs.TabLayout$Mode
Google.Android.Material.Tabs.TabLayout+IOnTabSelectedListener2, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$OnTabSelectedListener
Google.Android.Material.Tabs.TabLayout.IOnTabSelectedListener2;com.google.android.material.tabs.TabLayout$OnTabSelectedListener
com.google.android.material.tabs.TabLayout$OnTabSelectedListener;com.google.android.material.tabs.TabLayout$OnTabSelectedListener
Google.Android.Material.Tabs.TabLayout+ITabGravity, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$TabGravity
Google.Android.Material.Tabs.TabLayout.ITabGravity;com.google.android.material.tabs.TabLayout$TabGravity
com.google.android.material.tabs.TabLayout$TabGravity;com.google.android.material.tabs.TabLayout$TabGravity
Google.Android.Material.Tabs.TabLayout+ITabIndicatorAnimationMode, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$TabIndicatorAnimationMode
Google.Android.Material.Tabs.TabLayout.ITabIndicatorAnimationMode;com.google.android.material.tabs.TabLayout$TabIndicatorAnimationMode
com.google.android.material.tabs.TabLayout$TabIndicatorAnimationMode;com.google.android.material.tabs.TabLayout$TabIndicatorAnimationMode
Google.Android.Material.Tabs.TabLayout+ITabIndicatorGravity, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayout$TabIndicatorGravity
Google.Android.Material.Tabs.TabLayout.ITabIndicatorGravity;com.google.android.material.tabs.TabLayout$TabIndicatorGravity
com.google.android.material.tabs.TabLayout$TabIndicatorGravity;com.google.android.material.tabs.TabLayout$TabIndicatorGravity
Google.Android.Material.Tabs.TabLayoutMediator+ITabConfigurationStrategy, Xamarin.Google.Android.Material;com.google.android.material.tabs.TabLayoutMediator$TabConfigurationStrategy
Google.Android.Material.Tabs.TabLayoutMediator.ITabConfigurationStrategy;com.google.android.material.tabs.TabLayoutMediator$TabConfigurationStrategy
com.google.android.material.tabs.TabLayoutMediator$TabConfigurationStrategy;com.google.android.material.tabs.TabLayoutMediator$TabConfigurationStrategy
Google.Android.Material.Snackbar.Snackbar+SnackbarActionClickImplementor, Xamarin.Google.Android.Material;com.google.android.material.snackbar.Snackbar_SnackbarActionClickImplementor
Google.Android.Material.Snackbar.Snackbar.SnackbarActionClickImplementor;com.google.android.material.snackbar.Snackbar_SnackbarActionClickImplementor
com.google.android.material.snackbar.Snackbar_SnackbarActionClickImplementor;com.google.android.material.snackbar.Snackbar_SnackbarActionClickImplementor
Google.Android.Material.Snackbar.BaseTransientBottomBar+IAnimationMode, Xamarin.Google.Android.Material;com.google.android.material.snackbar.BaseTransientBottomBar$AnimationMode
Google.Android.Material.Snackbar.BaseTransientBottomBar.IAnimationMode;com.google.android.material.snackbar.BaseTransientBottomBar$AnimationMode
com.google.android.material.snackbar.BaseTransientBottomBar$AnimationMode;com.google.android.material.snackbar.BaseTransientBottomBar$AnimationMode
Google.Android.Material.Snackbar.BaseTransientBottomBar+BaseCallback+IDismissEvent, Xamarin.Google.Android.Material;com.google.android.material.snackbar.BaseTransientBottomBar$BaseCallback$DismissEvent
Google.Android.Material.Snackbar.BaseTransientBottomBar.BaseCallback.IDismissEvent;com.google.android.material.snackbar.BaseTransientBottomBar$BaseCallback$DismissEvent
com.google.android.material.snackbar.BaseTransientBottomBar$BaseCallback$DismissEvent;com.google.android.material.snackbar.BaseTransientBottomBar$BaseCallback$DismissEvent
Google.Android.Material.Snackbar.BaseTransientBottomBar+IContentViewCallback, Xamarin.Google.Android.Material;com.google.android.material.snackbar.BaseTransientBottomBar$ContentViewCallback
Google.Android.Material.Snackbar.BaseTransientBottomBar.IContentViewCallback;com.google.android.material.snackbar.BaseTransientBottomBar$ContentViewCallback
com.google.android.material.snackbar.BaseTransientBottomBar$ContentViewCallback;com.google.android.material.snackbar.BaseTransientBottomBar$ContentViewCallback
Google.Android.Material.Snackbar.BaseTransientBottomBar+IDuration, Xamarin.Google.Android.Material;com.google.android.material.snackbar.BaseTransientBottomBar$Duration
Google.Android.Material.Snackbar.BaseTransientBottomBar.IDuration;com.google.android.material.snackbar.BaseTransientBottomBar$Duration
com.google.android.material.snackbar.BaseTransientBottomBar$Duration;com.google.android.material.snackbar.BaseTransientBottomBar$Duration
Google.Android.Material.Snackbar.IContentViewCallback, Xamarin.Google.Android.Material;com.google.android.material.snackbar.ContentViewCallback
Google.Android.Material.Snackbar.IContentViewCallback;com.google.android.material.snackbar.ContentViewCallback
com.google.android.material.snackbar.ContentViewCallback;com.google.android.material.snackbar.ContentViewCallback
Google.Android.Material.Internal.CheckableGroup+IOnCheckedStateChangeListener, Xamarin.Google.Android.Material;com.google.android.material.internal.CheckableGroup$OnCheckedStateChangeListener
Google.Android.Material.Internal.CheckableGroup.IOnCheckedStateChangeListener;com.google.android.material.internal.CheckableGroup$OnCheckedStateChangeListener
com.google.android.material.internal.CheckableGroup$OnCheckedStateChangeListener;com.google.android.material.internal.CheckableGroup$OnCheckedStateChangeListener
Google.Android.Material.Internal.CheckableGroup+IOnCheckedStateChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.internal.CheckableGroup_OnCheckedStateChangeListenerImplementor
Google.Android.Material.Internal.CheckableGroup.IOnCheckedStateChangeListenerImplementor;mono.com.google.android.material.internal.CheckableGroup_OnCheckedStateChangeListenerImplementor
mono.com.google.android.material.internal.CheckableGroup_OnCheckedStateChangeListenerImplementor;mono.com.google.android.material.internal.CheckableGroup_OnCheckedStateChangeListenerImplementor
Google.Android.Material.Internal.IExperimental, Xamarin.Google.Android.Material;com.google.android.material.internal.Experimental
Google.Android.Material.Internal.IExperimental;com.google.android.material.internal.Experimental
com.google.android.material.internal.Experimental;com.google.android.material.internal.Experimental
Google.Android.Material.Internal.IMaterialCheckableOnCheckedChangeListener, Xamarin.Google.Android.Material;com.google.android.material.internal.MaterialCheckable$OnCheckedChangeListener
Google.Android.Material.Internal.IMaterialCheckableOnCheckedChangeListener;com.google.android.material.internal.MaterialCheckable$OnCheckedChangeListener
com.google.android.material.internal.MaterialCheckable$OnCheckedChangeListener;com.google.android.material.internal.MaterialCheckable$OnCheckedChangeListener
Google.Android.Material.Internal.IMaterialCheckableOnCheckedChangeListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.internal.MaterialCheckable_OnCheckedChangeListenerImplementor
Google.Android.Material.Internal.IMaterialCheckableOnCheckedChangeListenerImplementor;mono.com.google.android.material.internal.MaterialCheckable_OnCheckedChangeListenerImplementor
mono.com.google.android.material.internal.MaterialCheckable_OnCheckedChangeListenerImplementor;mono.com.google.android.material.internal.MaterialCheckable_OnCheckedChangeListenerImplementor
Google.Android.Material.Internal.IMaterialCheckable, Xamarin.Google.Android.Material;com.google.android.material.internal.MaterialCheckable
Google.Android.Material.Internal.IMaterialCheckable;com.google.android.material.internal.MaterialCheckable
com.google.android.material.internal.MaterialCheckable;com.google.android.material.internal.MaterialCheckable
Google.Android.Material.Internal.IStaticLayoutBuilderConfigurer, Xamarin.Google.Android.Material;com.google.android.material.internal.StaticLayoutBuilderConfigurer
Google.Android.Material.Internal.IStaticLayoutBuilderConfigurer;com.google.android.material.internal.StaticLayoutBuilderConfigurer
com.google.android.material.internal.StaticLayoutBuilderConfigurer;com.google.android.material.internal.StaticLayoutBuilderConfigurer
Google.Android.Material.Internal.IViewOverlayImpl, Xamarin.Google.Android.Material;com.google.android.material.internal.ViewOverlayImpl
Google.Android.Material.Internal.IViewOverlayImpl;com.google.android.material.internal.ViewOverlayImpl
com.google.android.material.internal.ViewOverlayImpl;com.google.android.material.internal.ViewOverlayImpl
Google.Android.Material.Internal.TextDrawableHelper+ITextDrawableDelegate, Xamarin.Google.Android.Material;com.google.android.material.internal.TextDrawableHelper$TextDrawableDelegate
Google.Android.Material.Internal.TextDrawableHelper.ITextDrawableDelegate;com.google.android.material.internal.TextDrawableHelper$TextDrawableDelegate
com.google.android.material.internal.TextDrawableHelper$TextDrawableDelegate;com.google.android.material.internal.TextDrawableHelper$TextDrawableDelegate
Google.Android.Material.Internal.ViewUtils+IOnApplyWindowInsetsListener, Xamarin.Google.Android.Material;com.google.android.material.internal.ViewUtils$OnApplyWindowInsetsListener
Google.Android.Material.Internal.ViewUtils.IOnApplyWindowInsetsListener;com.google.android.material.internal.ViewUtils$OnApplyWindowInsetsListener
com.google.android.material.internal.ViewUtils$OnApplyWindowInsetsListener;com.google.android.material.internal.ViewUtils$OnApplyWindowInsetsListener
Google.Android.Material.Internal.ViewUtils+IOnApplyWindowInsetsListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.internal.ViewUtils_OnApplyWindowInsetsListenerImplementor
Google.Android.Material.Internal.ViewUtils.IOnApplyWindowInsetsListenerImplementor;mono.com.google.android.material.internal.ViewUtils_OnApplyWindowInsetsListenerImplementor
mono.com.google.android.material.internal.ViewUtils_OnApplyWindowInsetsListenerImplementor;mono.com.google.android.material.internal.ViewUtils_OnApplyWindowInsetsListenerImplementor
Google.Android.Material.CircularReveal.CircularRevealHelper+IDelegate, Xamarin.Google.Android.Material;com.google.android.material.circularreveal.CircularRevealHelper$Delegate
Google.Android.Material.CircularReveal.CircularRevealHelper.IDelegate;com.google.android.material.circularreveal.CircularRevealHelper$Delegate
com.google.android.material.circularreveal.CircularRevealHelper$Delegate;com.google.android.material.circularreveal.CircularRevealHelper$Delegate
Google.Android.Material.CircularReveal.CircularRevealHelper+IStrategy, Xamarin.Google.Android.Material;com.google.android.material.circularreveal.CircularRevealHelper$Strategy
Google.Android.Material.CircularReveal.CircularRevealHelper.IStrategy;com.google.android.material.circularreveal.CircularRevealHelper$Strategy
com.google.android.material.circularreveal.CircularRevealHelper$Strategy;com.google.android.material.circularreveal.CircularRevealHelper$Strategy
Google.Android.Material.CircularReveal.ICircularRevealWidget, Xamarin.Google.Android.Material;com.google.android.material.circularreveal.CircularRevealWidget
Google.Android.Material.CircularReveal.ICircularRevealWidget;com.google.android.material.circularreveal.CircularRevealWidget
com.google.android.material.circularreveal.CircularRevealWidget;com.google.android.material.circularreveal.CircularRevealWidget
Google.Android.Material.BottomNavigation.BottomNavigationView+IOnNavigationItemReselectedListener, Xamarin.Google.Android.Material;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemReselectedListener
Google.Android.Material.BottomNavigation.BottomNavigationView.IOnNavigationItemReselectedListener;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemReselectedListener
com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemReselectedListener;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemReselectedListener
Google.Android.Material.BottomNavigation.BottomNavigationView+IOnNavigationItemSelectedListener, Xamarin.Google.Android.Material;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemSelectedListener
Google.Android.Material.BottomNavigation.BottomNavigationView.IOnNavigationItemSelectedListener;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemSelectedListener
com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemSelectedListener;com.google.android.material.bottomnavigation.BottomNavigationView$OnNavigationItemSelectedListener
Google.Android.Material.BottomNavigation.ILabelVisibilityMode, Xamarin.Google.Android.Material;com.google.android.material.bottomnavigation.LabelVisibilityMode
Google.Android.Material.BottomNavigation.ILabelVisibilityMode;com.google.android.material.bottomnavigation.LabelVisibilityMode
com.google.android.material.bottomnavigation.LabelVisibilityMode;com.google.android.material.bottomnavigation.LabelVisibilityMode
Google.Android.Material.AppBar.CollapsingToolbarLayout+IStaticLayoutBuilderConfigurer, Xamarin.Google.Android.Material;com.google.android.material.appbar.CollapsingToolbarLayout$StaticLayoutBuilderConfigurer
Google.Android.Material.AppBar.CollapsingToolbarLayout.IStaticLayoutBuilderConfigurer;com.google.android.material.appbar.CollapsingToolbarLayout$StaticLayoutBuilderConfigurer
com.google.android.material.appbar.CollapsingToolbarLayout$StaticLayoutBuilderConfigurer;com.google.android.material.appbar.CollapsingToolbarLayout$StaticLayoutBuilderConfigurer
Google.Android.Material.AppBar.CollapsingToolbarLayout+ITitleCollapseMode, Xamarin.Google.Android.Material;com.google.android.material.appbar.CollapsingToolbarLayout$TitleCollapseMode
Google.Android.Material.AppBar.CollapsingToolbarLayout.ITitleCollapseMode;com.google.android.material.appbar.CollapsingToolbarLayout$TitleCollapseMode
com.google.android.material.appbar.CollapsingToolbarLayout$TitleCollapseMode;com.google.android.material.appbar.CollapsingToolbarLayout$TitleCollapseMode
Google.Android.Material.AppBar.AppBarLayout+LayoutParams+IScrollEffect, Xamarin.Google.Android.Material;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollEffect
Google.Android.Material.AppBar.AppBarLayout.LayoutParams.IScrollEffect;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollEffect
com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollEffect;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollEffect
Google.Android.Material.AppBar.AppBarLayout+LayoutParams+IScrollFlags, Xamarin.Google.Android.Material;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollFlags
Google.Android.Material.AppBar.AppBarLayout.LayoutParams.IScrollFlags;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollFlags
com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollFlags;com.google.android.material.appbar.AppBarLayout$LayoutParams$ScrollFlags
Google.Android.Material.AppBar.AppBarLayout+ILiftOnScrollListener, Xamarin.Google.Android.Material;com.google.android.material.appbar.AppBarLayout$LiftOnScrollListener
Google.Android.Material.AppBar.AppBarLayout.ILiftOnScrollListener;com.google.android.material.appbar.AppBarLayout$LiftOnScrollListener
com.google.android.material.appbar.AppBarLayout$LiftOnScrollListener;com.google.android.material.appbar.AppBarLayout$LiftOnScrollListener
Google.Android.Material.AppBar.AppBarLayout+ILiftOnScrollListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.appbar.AppBarLayout_LiftOnScrollListenerImplementor
Google.Android.Material.AppBar.AppBarLayout.ILiftOnScrollListenerImplementor;mono.com.google.android.material.appbar.AppBarLayout_LiftOnScrollListenerImplementor
mono.com.google.android.material.appbar.AppBarLayout_LiftOnScrollListenerImplementor;mono.com.google.android.material.appbar.AppBarLayout_LiftOnScrollListenerImplementor
Google.Android.Material.AppBar.AppBarLayout+IOnOffsetChangedListener, Xamarin.Google.Android.Material;com.google.android.material.appbar.AppBarLayout$OnOffsetChangedListener
Google.Android.Material.AppBar.AppBarLayout.IOnOffsetChangedListener;com.google.android.material.appbar.AppBarLayout$OnOffsetChangedListener
com.google.android.material.appbar.AppBarLayout$OnOffsetChangedListener;com.google.android.material.appbar.AppBarLayout$OnOffsetChangedListener
Google.Android.Material.AppBar.AppBarLayout+IOnOffsetChangedListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.appbar.AppBarLayout_OnOffsetChangedListenerImplementor
Google.Android.Material.AppBar.AppBarLayout.IOnOffsetChangedListenerImplementor;mono.com.google.android.material.appbar.AppBarLayout_OnOffsetChangedListenerImplementor
mono.com.google.android.material.appbar.AppBarLayout_OnOffsetChangedListenerImplementor;mono.com.google.android.material.appbar.AppBarLayout_OnOffsetChangedListenerImplementor
Google.Android.Material.Animation.IAnimatableViewListener, Xamarin.Google.Android.Material;com.google.android.material.animation.AnimatableView$Listener
Google.Android.Material.Animation.IAnimatableViewListener;com.google.android.material.animation.AnimatableView$Listener
com.google.android.material.animation.AnimatableView$Listener;com.google.android.material.animation.AnimatableView$Listener
Google.Android.Material.Animation.IAnimatableViewListenerImplementor, Xamarin.Google.Android.Material;mono.com.google.android.material.animation.AnimatableView_ListenerImplementor
Google.Android.Material.Animation.IAnimatableViewListenerImplementor;mono.com.google.android.material.animation.AnimatableView_ListenerImplementor
mono.com.google.android.material.animation.AnimatableView_ListenerImplementor;mono.com.google.android.material.animation.AnimatableView_ListenerImplementor
Google.Android.Material.Animation.IAnimatableView, Xamarin.Google.Android.Material;com.google.android.material.animation.AnimatableView
Google.Android.Material.Animation.IAnimatableView;com.google.android.material.animation.AnimatableView
com.google.android.material.animation.AnimatableView;com.google.android.material.animation.AnimatableView
Google.Android.Material.Animation.ITransformationCallback, Xamarin.Google.Android.Material;com.google.android.material.animation.TransformationCallback
Google.Android.Material.Animation.ITransformationCallback;com.google.android.material.animation.TransformationCallback
com.google.android.material.animation.TransformationCallback;com.google.android.material.animation.TransformationCallback
Javax.Annotation.ICheckForNull, Jsr305Binding;javax.annotation.CheckForNull
Javax.Annotation.ICheckForNull;javax.annotation.CheckForNull
javax.annotation.CheckForNull;javax.annotation.CheckForNull
Javax.Annotation.ICheckForSigned, Jsr305Binding;javax.annotation.CheckForSigned
Javax.Annotation.ICheckForSigned;javax.annotation.CheckForSigned
javax.annotation.CheckForSigned;javax.annotation.CheckForSigned
Javax.Annotation.ICheckReturnValue, Jsr305Binding;javax.annotation.CheckReturnValue
Javax.Annotation.ICheckReturnValue;javax.annotation.CheckReturnValue
javax.annotation.CheckReturnValue;javax.annotation.CheckReturnValue
Javax.Annotation.IDetainted, Jsr305Binding;javax.annotation.Detainted
Javax.Annotation.IDetainted;javax.annotation.Detainted
javax.annotation.Detainted;javax.annotation.Detainted
Javax.Annotation.IMatchesPattern, Jsr305Binding;javax.annotation.MatchesPattern
Javax.Annotation.IMatchesPattern;javax.annotation.MatchesPattern
javax.annotation.MatchesPattern;javax.annotation.MatchesPattern
Javax.Annotation.INonnegative, Jsr305Binding;javax.annotation.Nonnegative
Javax.Annotation.INonnegative;javax.annotation.Nonnegative
javax.annotation.Nonnegative;javax.annotation.Nonnegative
Javax.Annotation.INonnull, Jsr305Binding;javax.annotation.Nonnull
Javax.Annotation.INonnull;javax.annotation.Nonnull
javax.annotation.Nonnull;javax.annotation.Nonnull
Javax.Annotation.INullable, Jsr305Binding;javax.annotation.Nullable
Javax.Annotation.INullable;javax.annotation.Nullable
javax.annotation.Nullable;javax.annotation.Nullable
Javax.Annotation.IOverridingMethodsMustInvokeSuper, Jsr305Binding;javax.annotation.OverridingMethodsMustInvokeSuper
Javax.Annotation.IOverridingMethodsMustInvokeSuper;javax.annotation.OverridingMethodsMustInvokeSuper
javax.annotation.OverridingMethodsMustInvokeSuper;javax.annotation.OverridingMethodsMustInvokeSuper
Javax.Annotation.IParametersAreNonnullByDefault, Jsr305Binding;javax.annotation.ParametersAreNonnullByDefault
Javax.Annotation.IParametersAreNonnullByDefault;javax.annotation.ParametersAreNonnullByDefault
javax.annotation.ParametersAreNonnullByDefault;javax.annotation.ParametersAreNonnullByDefault
Javax.Annotation.IParametersAreNullableByDefault, Jsr305Binding;javax.annotation.ParametersAreNullableByDefault
Javax.Annotation.IParametersAreNullableByDefault;javax.annotation.ParametersAreNullableByDefault
javax.annotation.ParametersAreNullableByDefault;javax.annotation.ParametersAreNullableByDefault
Javax.Annotation.IPropertyKey, Jsr305Binding;javax.annotation.PropertyKey
Javax.Annotation.IPropertyKey;javax.annotation.PropertyKey
javax.annotation.PropertyKey;javax.annotation.PropertyKey
Javax.Annotation.IRegEx, Jsr305Binding;javax.annotation.RegEx
Javax.Annotation.IRegEx;javax.annotation.RegEx
javax.annotation.RegEx;javax.annotation.RegEx
Javax.Annotation.ISigned, Jsr305Binding;javax.annotation.Signed
Javax.Annotation.ISigned;javax.annotation.Signed
javax.annotation.Signed;javax.annotation.Signed
Javax.Annotation.ISyntax, Jsr305Binding;javax.annotation.Syntax
Javax.Annotation.ISyntax;javax.annotation.Syntax
javax.annotation.Syntax;javax.annotation.Syntax
Javax.Annotation.ITainted, Jsr305Binding;javax.annotation.Tainted
Javax.Annotation.ITainted;javax.annotation.Tainted
javax.annotation.Tainted;javax.annotation.Tainted
Javax.Annotation.IUntainted, Jsr305Binding;javax.annotation.Untainted
Javax.Annotation.IUntainted;javax.annotation.Untainted
javax.annotation.Untainted;javax.annotation.Untainted
Javax.Annotation.IWillClose, Jsr305Binding;javax.annotation.WillClose
Javax.Annotation.IWillClose;javax.annotation.WillClose
javax.annotation.WillClose;javax.annotation.WillClose
Javax.Annotation.IWillCloseWhenClosed, Jsr305Binding;javax.annotation.WillCloseWhenClosed
Javax.Annotation.IWillCloseWhenClosed;javax.annotation.WillCloseWhenClosed
javax.annotation.WillCloseWhenClosed;javax.annotation.WillCloseWhenClosed
Javax.Annotation.IWillNotClose, Jsr305Binding;javax.annotation.WillNotClose
Javax.Annotation.IWillNotClose;javax.annotation.WillNotClose
javax.annotation.WillNotClose;javax.annotation.WillNotClose
Javax.Annotation.Meta.IExclusive, Jsr305Binding;javax.annotation.meta.Exclusive
Javax.Annotation.Meta.IExclusive;javax.annotation.meta.Exclusive
javax.annotation.meta.Exclusive;javax.annotation.meta.Exclusive
Javax.Annotation.Meta.IExhaustive, Jsr305Binding;javax.annotation.meta.Exhaustive
Javax.Annotation.Meta.IExhaustive;javax.annotation.meta.Exhaustive
javax.annotation.meta.Exhaustive;javax.annotation.meta.Exhaustive
Javax.Annotation.Meta.ITypeQualifier, Jsr305Binding;javax.annotation.meta.TypeQualifier
Javax.Annotation.Meta.ITypeQualifier;javax.annotation.meta.TypeQualifier
javax.annotation.meta.TypeQualifier;javax.annotation.meta.TypeQualifier
Javax.Annotation.Meta.ITypeQualifierDefault, Jsr305Binding;javax.annotation.meta.TypeQualifierDefault
Javax.Annotation.Meta.ITypeQualifierDefault;javax.annotation.meta.TypeQualifierDefault
javax.annotation.meta.TypeQualifierDefault;javax.annotation.meta.TypeQualifierDefault
Javax.Annotation.Meta.ITypeQualifierNickname, Jsr305Binding;javax.annotation.meta.TypeQualifierNickname
Javax.Annotation.Meta.ITypeQualifierNickname;javax.annotation.meta.TypeQualifierNickname
javax.annotation.meta.TypeQualifierNickname;javax.annotation.meta.TypeQualifierNickname
Javax.Annotation.Meta.ITypeQualifierValidator, Jsr305Binding;javax.annotation.meta.TypeQualifierValidator
Javax.Annotation.Meta.ITypeQualifierValidator;javax.annotation.meta.TypeQualifierValidator
javax.annotation.meta.TypeQualifierValidator;javax.annotation.meta.TypeQualifierValidator
Javax.Annotation.Concurrent.IGuardedBy, Jsr305Binding;javax.annotation.concurrent.GuardedBy
Javax.Annotation.Concurrent.IGuardedBy;javax.annotation.concurrent.GuardedBy
javax.annotation.concurrent.GuardedBy;javax.annotation.concurrent.GuardedBy
Javax.Annotation.Concurrent.IImmutable, Jsr305Binding;javax.annotation.concurrent.Immutable
Javax.Annotation.Concurrent.IImmutable;javax.annotation.concurrent.Immutable
javax.annotation.concurrent.Immutable;javax.annotation.concurrent.Immutable
Javax.Annotation.Concurrent.INotThreadSafe, Jsr305Binding;javax.annotation.concurrent.NotThreadSafe
Javax.Annotation.Concurrent.INotThreadSafe;javax.annotation.concurrent.NotThreadSafe
javax.annotation.concurrent.NotThreadSafe;javax.annotation.concurrent.NotThreadSafe
Javax.Annotation.Concurrent.IThreadSafe, Jsr305Binding;javax.annotation.concurrent.ThreadSafe
Javax.Annotation.Concurrent.IThreadSafe;javax.annotation.concurrent.ThreadSafe
javax.annotation.concurrent.ThreadSafe;javax.annotation.concurrent.ThreadSafe
Xamarin.Google.Crypto.Tink.IAccessesPartialKey, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.AccessesPartialKey
Xamarin.Google.Crypto.Tink.IAccessesPartialKey;com.google.crypto.tink.AccessesPartialKey
com.google.crypto.tink.AccessesPartialKey;com.google.crypto.tink.AccessesPartialKey
Xamarin.Google.Crypto.Tink.IAead, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.Aead
Xamarin.Google.Crypto.Tink.IAead;com.google.crypto.tink.Aead
com.google.crypto.tink.Aead;com.google.crypto.tink.Aead
Xamarin.Google.Crypto.Tink.IDeterministicAead, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.DeterministicAead
Xamarin.Google.Crypto.Tink.IDeterministicAead;com.google.crypto.tink.DeterministicAead
com.google.crypto.tink.DeterministicAead;com.google.crypto.tink.DeterministicAead
Xamarin.Google.Crypto.Tink.IHybridDecrypt, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.HybridDecrypt
Xamarin.Google.Crypto.Tink.IHybridDecrypt;com.google.crypto.tink.HybridDecrypt
com.google.crypto.tink.HybridDecrypt;com.google.crypto.tink.HybridDecrypt
Xamarin.Google.Crypto.Tink.IHybridEncrypt, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.HybridEncrypt
Xamarin.Google.Crypto.Tink.IHybridEncrypt;com.google.crypto.tink.HybridEncrypt
com.google.crypto.tink.HybridEncrypt;com.google.crypto.tink.HybridEncrypt
Xamarin.Google.Crypto.Tink.IKeyManager, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.KeyManager
Xamarin.Google.Crypto.Tink.IKeyManager;com.google.crypto.tink.KeyManager
com.google.crypto.tink.KeyManager;com.google.crypto.tink.KeyManager
Xamarin.Google.Crypto.Tink.IKeysetReader, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.KeysetReader
Xamarin.Google.Crypto.Tink.IKeysetReader;com.google.crypto.tink.KeysetReader
com.google.crypto.tink.KeysetReader;com.google.crypto.tink.KeysetReader
Xamarin.Google.Crypto.Tink.IKeysetWriter, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.KeysetWriter
Xamarin.Google.Crypto.Tink.IKeysetWriter;com.google.crypto.tink.KeysetWriter
com.google.crypto.tink.KeysetWriter;com.google.crypto.tink.KeysetWriter
Xamarin.Google.Crypto.Tink.IKeyWrap, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.KeyWrap
Xamarin.Google.Crypto.Tink.IKeyWrap;com.google.crypto.tink.KeyWrap
com.google.crypto.tink.KeyWrap;com.google.crypto.tink.KeyWrap
Xamarin.Google.Crypto.Tink.IKmsClient, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.KmsClient
Xamarin.Google.Crypto.Tink.IKmsClient;com.google.crypto.tink.KmsClient
com.google.crypto.tink.KmsClient;com.google.crypto.tink.KmsClient
Xamarin.Google.Crypto.Tink.IMac, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.Mac
Xamarin.Google.Crypto.Tink.IMac;com.google.crypto.tink.Mac
com.google.crypto.tink.Mac;com.google.crypto.tink.Mac
Xamarin.Google.Crypto.Tink.IPrivateKey, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.PrivateKey
Xamarin.Google.Crypto.Tink.IPrivateKey;com.google.crypto.tink.PrivateKey
com.google.crypto.tink.PrivateKey;com.google.crypto.tink.PrivateKey
Xamarin.Google.Crypto.Tink.IPrivateKeyManager, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.PrivateKeyManager
Xamarin.Google.Crypto.Tink.IPrivateKeyManager;com.google.crypto.tink.PrivateKeyManager
com.google.crypto.tink.PrivateKeyManager;com.google.crypto.tink.PrivateKeyManager
Xamarin.Google.Crypto.Tink.IPublicKeySign, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.PublicKeySign
Xamarin.Google.Crypto.Tink.IPublicKeySign;com.google.crypto.tink.PublicKeySign
com.google.crypto.tink.PublicKeySign;com.google.crypto.tink.PublicKeySign
Xamarin.Google.Crypto.Tink.IPublicKeyVerify, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.PublicKeyVerify
Xamarin.Google.Crypto.Tink.IPublicKeyVerify;com.google.crypto.tink.PublicKeyVerify
com.google.crypto.tink.PublicKeyVerify;com.google.crypto.tink.PublicKeyVerify
Xamarin.Google.Crypto.Tink.IStreamingAead, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.StreamingAead
Xamarin.Google.Crypto.Tink.IStreamingAead;com.google.crypto.tink.StreamingAead
com.google.crypto.tink.StreamingAead;com.google.crypto.tink.StreamingAead
Xamarin.Google.Crypto.Tink.Tinkkey.ITinkKey, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.tinkkey.TinkKey
Xamarin.Google.Crypto.Tink.Tinkkey.ITinkKey;com.google.crypto.tink.tinkkey.TinkKey
com.google.crypto.tink.tinkkey.TinkKey;com.google.crypto.tink.tinkkey.TinkKey
Xamarin.Google.Crypto.Tink.Subtle.IEngineWrapper, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.subtle.EngineWrapper
Xamarin.Google.Crypto.Tink.Subtle.IEngineWrapper;com.google.crypto.tink.subtle.EngineWrapper
com.google.crypto.tink.subtle.EngineWrapper;com.google.crypto.tink.subtle.EngineWrapper
Xamarin.Google.Crypto.Tink.Subtle.IIndCpaCipher, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.subtle.IndCpaCipher
Xamarin.Google.Crypto.Tink.Subtle.IIndCpaCipher;com.google.crypto.tink.subtle.IndCpaCipher
com.google.crypto.tink.subtle.IndCpaCipher;com.google.crypto.tink.subtle.IndCpaCipher
Xamarin.Google.Crypto.Tink.Subtle.IStreamSegmentDecrypter, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.subtle.StreamSegmentDecrypter
Xamarin.Google.Crypto.Tink.Subtle.IStreamSegmentDecrypter;com.google.crypto.tink.subtle.StreamSegmentDecrypter
com.google.crypto.tink.subtle.StreamSegmentDecrypter;com.google.crypto.tink.subtle.StreamSegmentDecrypter
Xamarin.Google.Crypto.Tink.Subtle.IStreamSegmentEncrypter, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.subtle.StreamSegmentEncrypter
Xamarin.Google.Crypto.Tink.Subtle.IStreamSegmentEncrypter;com.google.crypto.tink.subtle.StreamSegmentEncrypter
com.google.crypto.tink.subtle.StreamSegmentEncrypter;com.google.crypto.tink.subtle.StreamSegmentEncrypter
Xamarin.Google.Crypto.Tink.Subtle.Prf.IStreamingPrf, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.subtle.prf.StreamingPrf
Xamarin.Google.Crypto.Tink.Subtle.Prf.IStreamingPrf;com.google.crypto.tink.subtle.prf.StreamingPrf
com.google.crypto.tink.subtle.prf.StreamingPrf;com.google.crypto.tink.subtle.prf.StreamingPrf
Xamarin.Google.Crypto.Tink.Proto.IAesCmacKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCmacKeyFormatOrBuilder
com.google.crypto.tink.proto.AesCmacKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacKeyOrBuilder;com.google.crypto.tink.proto.AesCmacKeyOrBuilder
com.google.crypto.tink.proto.AesCmacKeyOrBuilder;com.google.crypto.tink.proto.AesCmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCmacParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacParamsOrBuilder;com.google.crypto.tink.proto.AesCmacParamsOrBuilder
com.google.crypto.tink.proto.AesCmacParamsOrBuilder;com.google.crypto.tink.proto.AesCmacParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacPrfKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCmacPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCmacPrfKeyFormatOrBuilder
com.google.crypto.tink.proto.AesCmacPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCmacPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacPrfKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCmacPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCmacPrfKeyOrBuilder;com.google.crypto.tink.proto.AesCmacPrfKeyOrBuilder
com.google.crypto.tink.proto.AesCmacPrfKeyOrBuilder;com.google.crypto.tink.proto.AesCmacPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacAeadKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormatOrBuilder
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacAeadKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrHmacAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacAeadKeyOrBuilder;com.google.crypto.tink.proto.AesCtrHmacAeadKeyOrBuilder
com.google.crypto.tink.proto.AesCtrHmacAeadKeyOrBuilder;com.google.crypto.tink.proto.AesCtrHmacAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyFormatOrBuilder
com.google.crypto.tink.proto.AesCtrHmacStreamingKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingKeyOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyOrBuilder
com.google.crypto.tink.proto.AesCtrHmacStreamingKeyOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrHmacStreamingParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrHmacStreamingParamsOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingParamsOrBuilder
com.google.crypto.tink.proto.AesCtrHmacStreamingParamsOrBuilder;com.google.crypto.tink.proto.AesCtrHmacStreamingParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrKeyFormatOrBuilder
com.google.crypto.tink.proto.AesCtrKeyFormatOrBuilder;com.google.crypto.tink.proto.AesCtrKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrKeyOrBuilder;com.google.crypto.tink.proto.AesCtrKeyOrBuilder
com.google.crypto.tink.proto.AesCtrKeyOrBuilder;com.google.crypto.tink.proto.AesCtrKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesCtrParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesCtrParamsOrBuilder;com.google.crypto.tink.proto.AesCtrParamsOrBuilder
com.google.crypto.tink.proto.AesCtrParamsOrBuilder;com.google.crypto.tink.proto.AesCtrParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesEaxKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxKeyFormatOrBuilder;com.google.crypto.tink.proto.AesEaxKeyFormatOrBuilder
com.google.crypto.tink.proto.AesEaxKeyFormatOrBuilder;com.google.crypto.tink.proto.AesEaxKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesEaxKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxKeyOrBuilder;com.google.crypto.tink.proto.AesEaxKeyOrBuilder
com.google.crypto.tink.proto.AesEaxKeyOrBuilder;com.google.crypto.tink.proto.AesEaxKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesEaxParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesEaxParamsOrBuilder;com.google.crypto.tink.proto.AesEaxParamsOrBuilder
com.google.crypto.tink.proto.AesEaxParamsOrBuilder;com.google.crypto.tink.proto.AesEaxParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyFormatOrBuilder
com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingKeyOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyOrBuilder
com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmHkdfStreamingParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmHkdfStreamingParamsOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingParamsOrBuilder
com.google.crypto.tink.proto.AesGcmHkdfStreamingParamsOrBuilder;com.google.crypto.tink.proto.AesGcmHkdfStreamingParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmKeyFormatOrBuilder
com.google.crypto.tink.proto.AesGcmKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmKeyOrBuilder;com.google.crypto.tink.proto.AesGcmKeyOrBuilder
com.google.crypto.tink.proto.AesGcmKeyOrBuilder;com.google.crypto.tink.proto.AesGcmKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmSivKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmSivKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmSivKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmSivKeyFormatOrBuilder
com.google.crypto.tink.proto.AesGcmSivKeyFormatOrBuilder;com.google.crypto.tink.proto.AesGcmSivKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmSivKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesGcmSivKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesGcmSivKeyOrBuilder;com.google.crypto.tink.proto.AesGcmSivKeyOrBuilder
com.google.crypto.tink.proto.AesGcmSivKeyOrBuilder;com.google.crypto.tink.proto.AesGcmSivKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesSivKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesSivKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesSivKeyFormatOrBuilder;com.google.crypto.tink.proto.AesSivKeyFormatOrBuilder
com.google.crypto.tink.proto.AesSivKeyFormatOrBuilder;com.google.crypto.tink.proto.AesSivKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesSivKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.AesSivKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IAesSivKeyOrBuilder;com.google.crypto.tink.proto.AesSivKeyOrBuilder
com.google.crypto.tink.proto.AesSivKeyOrBuilder;com.google.crypto.tink.proto.AesSivKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IChaCha20Poly1305KeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IChaCha20Poly1305KeyFormatOrBuilder;com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormatOrBuilder
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormatOrBuilder;com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IChaCha20Poly1305KeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.ChaCha20Poly1305KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IChaCha20Poly1305KeyOrBuilder;com.google.crypto.tink.proto.ChaCha20Poly1305KeyOrBuilder
com.google.crypto.tink.proto.ChaCha20Poly1305KeyOrBuilder;com.google.crypto.tink.proto.ChaCha20Poly1305KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EcdsaKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaKeyFormatOrBuilder;com.google.crypto.tink.proto.EcdsaKeyFormatOrBuilder
com.google.crypto.tink.proto.EcdsaKeyFormatOrBuilder;com.google.crypto.tink.proto.EcdsaKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EcdsaParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaParamsOrBuilder;com.google.crypto.tink.proto.EcdsaParamsOrBuilder
com.google.crypto.tink.proto.EcdsaParamsOrBuilder;com.google.crypto.tink.proto.EcdsaParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaPrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EcdsaPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaPrivateKeyOrBuilder;com.google.crypto.tink.proto.EcdsaPrivateKeyOrBuilder
com.google.crypto.tink.proto.EcdsaPrivateKeyOrBuilder;com.google.crypto.tink.proto.EcdsaPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaPublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EcdsaPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEcdsaPublicKeyOrBuilder;com.google.crypto.tink.proto.EcdsaPublicKeyOrBuilder
com.google.crypto.tink.proto.EcdsaPublicKeyOrBuilder;com.google.crypto.tink.proto.EcdsaPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadDemParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesAeadDemParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadDemParamsOrBuilder;com.google.crypto.tink.proto.EciesAeadDemParamsOrBuilder
com.google.crypto.tink.proto.EciesAeadDemParamsOrBuilder;com.google.crypto.tink.proto.EciesAeadDemParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesAeadHkdfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfKeyFormatOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfKeyFormatOrBuilder
com.google.crypto.tink.proto.EciesAeadHkdfKeyFormatOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesAeadHkdfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfParamsOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfParamsOrBuilder
com.google.crypto.tink.proto.EciesAeadHkdfParamsOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfPrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesAeadHkdfPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfPrivateKeyOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfPrivateKeyOrBuilder
com.google.crypto.tink.proto.EciesAeadHkdfPrivateKeyOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfPublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesAeadHkdfPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesAeadHkdfPublicKeyOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfPublicKeyOrBuilder
com.google.crypto.tink.proto.EciesAeadHkdfPublicKeyOrBuilder;com.google.crypto.tink.proto.EciesAeadHkdfPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesHkdfKemParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EciesHkdfKemParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEciesHkdfKemParamsOrBuilder;com.google.crypto.tink.proto.EciesHkdfKemParamsOrBuilder
com.google.crypto.tink.proto.EciesHkdfKemParamsOrBuilder;com.google.crypto.tink.proto.EciesHkdfKemParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519KeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.Ed25519KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519KeyFormatOrBuilder;com.google.crypto.tink.proto.Ed25519KeyFormatOrBuilder
com.google.crypto.tink.proto.Ed25519KeyFormatOrBuilder;com.google.crypto.tink.proto.Ed25519KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519PrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.Ed25519PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519PrivateKeyOrBuilder;com.google.crypto.tink.proto.Ed25519PrivateKeyOrBuilder
com.google.crypto.tink.proto.Ed25519PrivateKeyOrBuilder;com.google.crypto.tink.proto.Ed25519PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519PublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.Ed25519PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEd25519PublicKeyOrBuilder;com.google.crypto.tink.proto.Ed25519PublicKeyOrBuilder
com.google.crypto.tink.proto.Ed25519PublicKeyOrBuilder;com.google.crypto.tink.proto.Ed25519PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEncryptedKeysetOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.EncryptedKeysetOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IEncryptedKeysetOrBuilder;com.google.crypto.tink.proto.EncryptedKeysetOrBuilder
com.google.crypto.tink.proto.EncryptedKeysetOrBuilder;com.google.crypto.tink.proto.EncryptedKeysetOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HkdfPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.HkdfPrfKeyFormatOrBuilder
com.google.crypto.tink.proto.HkdfPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.HkdfPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HkdfPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfKeyOrBuilder;com.google.crypto.tink.proto.HkdfPrfKeyOrBuilder
com.google.crypto.tink.proto.HkdfPrfKeyOrBuilder;com.google.crypto.tink.proto.HkdfPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HkdfPrfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHkdfPrfParamsOrBuilder;com.google.crypto.tink.proto.HkdfPrfParamsOrBuilder
com.google.crypto.tink.proto.HkdfPrfParamsOrBuilder;com.google.crypto.tink.proto.HkdfPrfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacKeyFormatOrBuilder;com.google.crypto.tink.proto.HmacKeyFormatOrBuilder
com.google.crypto.tink.proto.HmacKeyFormatOrBuilder;com.google.crypto.tink.proto.HmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacKeyOrBuilder;com.google.crypto.tink.proto.HmacKeyOrBuilder
com.google.crypto.tink.proto.HmacKeyOrBuilder;com.google.crypto.tink.proto.HmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacParamsOrBuilder;com.google.crypto.tink.proto.HmacParamsOrBuilder
com.google.crypto.tink.proto.HmacParamsOrBuilder;com.google.crypto.tink.proto.HmacParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.HmacPrfKeyFormatOrBuilder
com.google.crypto.tink.proto.HmacPrfKeyFormatOrBuilder;com.google.crypto.tink.proto.HmacPrfKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfKeyOrBuilder;com.google.crypto.tink.proto.HmacPrfKeyOrBuilder
com.google.crypto.tink.proto.HmacPrfKeyOrBuilder;com.google.crypto.tink.proto.HmacPrfKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HmacPrfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHmacPrfParamsOrBuilder;com.google.crypto.tink.proto.HmacPrfParamsOrBuilder
com.google.crypto.tink.proto.HmacPrfParamsOrBuilder;com.google.crypto.tink.proto.HmacPrfParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkeKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HpkeKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkeKeyFormatOrBuilder;com.google.crypto.tink.proto.HpkeKeyFormatOrBuilder
com.google.crypto.tink.proto.HpkeKeyFormatOrBuilder;com.google.crypto.tink.proto.HpkeKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkeParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HpkeParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkeParamsOrBuilder;com.google.crypto.tink.proto.HpkeParamsOrBuilder
com.google.crypto.tink.proto.HpkeParamsOrBuilder;com.google.crypto.tink.proto.HpkeParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkePrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HpkePrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkePrivateKeyOrBuilder;com.google.crypto.tink.proto.HpkePrivateKeyOrBuilder
com.google.crypto.tink.proto.HpkePrivateKeyOrBuilder;com.google.crypto.tink.proto.HpkePrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkePublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.HpkePublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IHpkePublicKeyOrBuilder;com.google.crypto.tink.proto.HpkePublicKeyOrBuilder
com.google.crypto.tink.proto.HpkePublicKeyOrBuilder;com.google.crypto.tink.proto.HpkePublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtEcdsaKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtEcdsaKeyFormatOrBuilder
com.google.crypto.tink.proto.JwtEcdsaKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtEcdsaKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaPrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtEcdsaPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaPrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPrivateKeyOrBuilder
com.google.crypto.tink.proto.JwtEcdsaPrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaPublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtEcdsaPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtEcdsaPublicKeyOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPublicKeyOrBuilder
com.google.crypto.tink.proto.JwtEcdsaPublicKeyOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtHmacKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtHmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtHmacKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtHmacKeyFormatOrBuilder
com.google.crypto.tink.proto.JwtHmacKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtHmacKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtHmacKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtHmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtHmacKeyOrBuilder;com.google.crypto.tink.proto.JwtHmacKeyOrBuilder
com.google.crypto.tink.proto.JwtHmacKeyOrBuilder;com.google.crypto.tink.proto.JwtHmacKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1KeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPkcs1KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1KeyFormatOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1KeyFormatOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPkcs1KeyFormatOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1PrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1PrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PrivateKeyOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPkcs1PrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1PublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPkcs1PublicKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKeyOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPssKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssKeyFormatOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPssKeyFormatOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssPrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPssPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssPrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPrivateKeyOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPssPrivateKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssPublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IJwtRsaSsaPssPublicKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKeyOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPssPublicKeyOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyDataOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeyDataOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyDataOrBuilder;com.google.crypto.tink.proto.KeyDataOrBuilder
com.google.crypto.tink.proto.KeyDataOrBuilder;com.google.crypto.tink.proto.KeyDataOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeysetInfoOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeysetInfoOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeysetInfoOrBuilder;com.google.crypto.tink.proto.KeysetInfoOrBuilder
com.google.crypto.tink.proto.KeysetInfoOrBuilder;com.google.crypto.tink.proto.KeysetInfoOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeysetOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeysetOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeysetOrBuilder;com.google.crypto.tink.proto.KeysetOrBuilder
com.google.crypto.tink.proto.KeysetOrBuilder;com.google.crypto.tink.proto.KeysetOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyTemplateOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeyTemplateOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyTemplateOrBuilder;com.google.crypto.tink.proto.KeyTemplateOrBuilder
com.google.crypto.tink.proto.KeyTemplateOrBuilder;com.google.crypto.tink.proto.KeyTemplateOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyTypeEntryOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeyTypeEntryOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKeyTypeEntryOrBuilder;com.google.crypto.tink.proto.KeyTypeEntryOrBuilder
com.google.crypto.tink.proto.KeyTypeEntryOrBuilder;com.google.crypto.tink.proto.KeyTypeEntryOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsAeadKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KmsAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.KmsAeadKeyFormatOrBuilder
com.google.crypto.tink.proto.KmsAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.KmsAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsAeadKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KmsAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsAeadKeyOrBuilder;com.google.crypto.tink.proto.KmsAeadKeyOrBuilder
com.google.crypto.tink.proto.KmsAeadKeyOrBuilder;com.google.crypto.tink.proto.KmsAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsEnvelopeAeadKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsEnvelopeAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormatOrBuilder
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormatOrBuilder;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsEnvelopeAeadKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IKmsEnvelopeAeadKeyOrBuilder;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyOrBuilder
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyOrBuilder;com.google.crypto.tink.proto.KmsEnvelopeAeadKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.PrfBasedDeriverKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverKeyFormatOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverKeyFormatOrBuilder
com.google.crypto.tink.proto.PrfBasedDeriverKeyFormatOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.PrfBasedDeriverKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverKeyOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverKeyOrBuilder
com.google.crypto.tink.proto.PrfBasedDeriverKeyOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.PrfBasedDeriverParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IPrfBasedDeriverParamsOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverParamsOrBuilder
com.google.crypto.tink.proto.PrfBasedDeriverParamsOrBuilder;com.google.crypto.tink.proto.PrfBasedDeriverParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRegistryConfigOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RegistryConfigOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRegistryConfigOrBuilder;com.google.crypto.tink.proto.RegistryConfigOrBuilder
com.google.crypto.tink.proto.RegistryConfigOrBuilder;com.google.crypto.tink.proto.RegistryConfigOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1KeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPkcs1KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1KeyFormatOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1KeyFormatOrBuilder
com.google.crypto.tink.proto.RsaSsaPkcs1KeyFormatOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1ParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPkcs1ParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1ParamsOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1ParamsOrBuilder
com.google.crypto.tink.proto.RsaSsaPkcs1ParamsOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1ParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1PrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPkcs1PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1PrivateKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1PrivateKeyOrBuilder
com.google.crypto.tink.proto.RsaSsaPkcs1PrivateKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1PrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1PublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPkcs1PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPkcs1PublicKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1PublicKeyOrBuilder
com.google.crypto.tink.proto.RsaSsaPkcs1PublicKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPkcs1PublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPssKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssKeyFormatOrBuilder;com.google.crypto.tink.proto.RsaSsaPssKeyFormatOrBuilder
com.google.crypto.tink.proto.RsaSsaPssKeyFormatOrBuilder;com.google.crypto.tink.proto.RsaSsaPssKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPssParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssParamsOrBuilder;com.google.crypto.tink.proto.RsaSsaPssParamsOrBuilder
com.google.crypto.tink.proto.RsaSsaPssParamsOrBuilder;com.google.crypto.tink.proto.RsaSsaPssParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssPrivateKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPssPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssPrivateKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPssPrivateKeyOrBuilder
com.google.crypto.tink.proto.RsaSsaPssPrivateKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPssPrivateKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssPublicKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.RsaSsaPssPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IRsaSsaPssPublicKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPssPublicKeyOrBuilder
com.google.crypto.tink.proto.RsaSsaPssPublicKeyOrBuilder;com.google.crypto.tink.proto.RsaSsaPssPublicKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmKeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.XAesGcmKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmKeyFormatOrBuilder;com.google.crypto.tink.proto.XAesGcmKeyFormatOrBuilder
com.google.crypto.tink.proto.XAesGcmKeyFormatOrBuilder;com.google.crypto.tink.proto.XAesGcmKeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.XAesGcmKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmKeyOrBuilder;com.google.crypto.tink.proto.XAesGcmKeyOrBuilder
com.google.crypto.tink.proto.XAesGcmKeyOrBuilder;com.google.crypto.tink.proto.XAesGcmKeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmParamsOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.XAesGcmParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXAesGcmParamsOrBuilder;com.google.crypto.tink.proto.XAesGcmParamsOrBuilder
com.google.crypto.tink.proto.XAesGcmParamsOrBuilder;com.google.crypto.tink.proto.XAesGcmParamsOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXChaCha20Poly1305KeyFormatOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXChaCha20Poly1305KeyFormatOrBuilder;com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormatOrBuilder
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormatOrBuilder;com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormatOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXChaCha20Poly1305KeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.XChaCha20Poly1305KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.IXChaCha20Poly1305KeyOrBuilder;com.google.crypto.tink.proto.XChaCha20Poly1305KeyOrBuilder
com.google.crypto.tink.proto.XChaCha20Poly1305KeyOrBuilder;com.google.crypto.tink.proto.XChaCha20Poly1305KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtEcdsaPublicKey+ICustomKidOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtEcdsaPublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtEcdsaPublicKey.ICustomKidOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPublicKey$CustomKidOrBuilder
com.google.crypto.tink.proto.JwtEcdsaPublicKey$CustomKidOrBuilder;com.google.crypto.tink.proto.JwtEcdsaPublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtHmacKey+ICustomKidOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtHmacKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtHmacKey.ICustomKidOrBuilder;com.google.crypto.tink.proto.JwtHmacKey$CustomKidOrBuilder
com.google.crypto.tink.proto.JwtHmacKey$CustomKidOrBuilder;com.google.crypto.tink.proto.JwtHmacKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtRsaSsaPkcs1PublicKey+ICustomKidOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtRsaSsaPkcs1PublicKey.ICustomKidOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKey$CustomKidOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKey$CustomKidOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPkcs1PublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtRsaSsaPssPublicKey+ICustomKidOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.JwtRsaSsaPssPublicKey.ICustomKidOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKey$CustomKidOrBuilder
com.google.crypto.tink.proto.JwtRsaSsaPssPublicKey$CustomKidOrBuilder;com.google.crypto.tink.proto.JwtRsaSsaPssPublicKey$CustomKidOrBuilder
Xamarin.Google.Crypto.Tink.Proto.Keyset+IKeyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.Keyset$KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.Keyset.IKeyOrBuilder;com.google.crypto.tink.proto.Keyset$KeyOrBuilder
com.google.crypto.tink.proto.Keyset$KeyOrBuilder;com.google.crypto.tink.proto.Keyset$KeyOrBuilder
Xamarin.Google.Crypto.Tink.Proto.KeysetInfo+IKeyInfoOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.proto.KeysetInfo$KeyInfoOrBuilder
Xamarin.Google.Crypto.Tink.Proto.KeysetInfo.IKeyInfoOrBuilder;com.google.crypto.tink.proto.KeysetInfo$KeyInfoOrBuilder
com.google.crypto.tink.proto.KeysetInfo$KeyInfoOrBuilder;com.google.crypto.tink.proto.KeysetInfo$KeyInfoOrBuilder
Xamarin.Google.Crypto.Tink.Prf.IPrf, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.prf.Prf
Xamarin.Google.Crypto.Tink.Prf.IPrf;com.google.crypto.tink.prf.Prf
com.google.crypto.tink.prf.Prf;com.google.crypto.tink.prf.Prf
Xamarin.Google.Crypto.Tink.Mac.IChunkedMac, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.mac.ChunkedMac
Xamarin.Google.Crypto.Tink.Mac.IChunkedMac;com.google.crypto.tink.mac.ChunkedMac
com.google.crypto.tink.mac.ChunkedMac;com.google.crypto.tink.mac.ChunkedMac
Xamarin.Google.Crypto.Tink.Mac.IChunkedMacComputation, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.mac.ChunkedMacComputation
Xamarin.Google.Crypto.Tink.Mac.IChunkedMacComputation;com.google.crypto.tink.mac.ChunkedMacComputation
com.google.crypto.tink.mac.ChunkedMacComputation;com.google.crypto.tink.mac.ChunkedMacComputation
Xamarin.Google.Crypto.Tink.Mac.IChunkedMacVerification, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.mac.ChunkedMacVerification
Xamarin.Google.Crypto.Tink.Mac.IChunkedMacVerification;com.google.crypto.tink.mac.ChunkedMacVerification
com.google.crypto.tink.mac.ChunkedMacVerification;com.google.crypto.tink.mac.ChunkedMacVerification
Xamarin.Google.Crypto.Tink.KeyDerivation.IKeysetDeriver, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.keyderivation.KeysetDeriver
Xamarin.Google.Crypto.Tink.KeyDerivation.IKeysetDeriver;com.google.crypto.tink.keyderivation.KeysetDeriver
com.google.crypto.tink.keyderivation.KeysetDeriver;com.google.crypto.tink.keyderivation.KeysetDeriver
Xamarin.Google.Crypto.Tink.KeyDerivation.Internal.IKeyDeriver, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.keyderivation.internal.KeyDeriver
Xamarin.Google.Crypto.Tink.KeyDerivation.Internal.IKeyDeriver;com.google.crypto.tink.keyderivation.internal.KeyDeriver
com.google.crypto.tink.keyderivation.internal.KeyDeriver;com.google.crypto.tink.keyderivation.internal.KeyDeriver
Xamarin.Google.Crypto.Tink.Jwt.IJwtMac, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.jwt.JwtMac
Xamarin.Google.Crypto.Tink.Jwt.IJwtMac;com.google.crypto.tink.jwt.JwtMac
com.google.crypto.tink.jwt.JwtMac;com.google.crypto.tink.jwt.JwtMac
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeySign, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.jwt.JwtPublicKeySign
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeySign;com.google.crypto.tink.jwt.JwtPublicKeySign
com.google.crypto.tink.jwt.JwtPublicKeySign;com.google.crypto.tink.jwt.JwtPublicKeySign
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeySignInternal, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.jwt.JwtPublicKeySignInternal
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeySignInternal;com.google.crypto.tink.jwt.JwtPublicKeySignInternal
com.google.crypto.tink.jwt.JwtPublicKeySignInternal;com.google.crypto.tink.jwt.JwtPublicKeySignInternal
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeyVerify, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.jwt.JwtPublicKeyVerify
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeyVerify;com.google.crypto.tink.jwt.JwtPublicKeyVerify
com.google.crypto.tink.jwt.JwtPublicKeyVerify;com.google.crypto.tink.jwt.JwtPublicKeyVerify
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeyVerifyInternal, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.jwt.JwtPublicKeyVerifyInternal
Xamarin.Google.Crypto.Tink.Jwt.IJwtPublicKeyVerifyInternal;com.google.crypto.tink.jwt.JwtPublicKeyVerifyInternal
com.google.crypto.tink.jwt.JwtPublicKeyVerifyInternal;com.google.crypto.tink.jwt.JwtPublicKeyVerifyInternal
Xamarin.Google.Crypto.Tink.Internal.IMonitoringClientLogger, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.MonitoringClient$Logger
Xamarin.Google.Crypto.Tink.Internal.IMonitoringClientLogger;com.google.crypto.tink.internal.MonitoringClient$Logger
com.google.crypto.tink.internal.MonitoringClient$Logger;com.google.crypto.tink.internal.MonitoringClient$Logger
Xamarin.Google.Crypto.Tink.Internal.IMonitoringClient, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.MonitoringClient
Xamarin.Google.Crypto.Tink.Internal.IMonitoringClient;com.google.crypto.tink.internal.MonitoringClient
com.google.crypto.tink.internal.MonitoringClient;com.google.crypto.tink.internal.MonitoringClient
Xamarin.Google.Crypto.Tink.Internal.ISerialization, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.Serialization
Xamarin.Google.Crypto.Tink.Internal.ISerialization;com.google.crypto.tink.internal.Serialization
com.google.crypto.tink.internal.Serialization;com.google.crypto.tink.internal.Serialization
Xamarin.Google.Crypto.Tink.Internal.KeyParser+IKeyParsingFunction, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.KeyParser$KeyParsingFunction
Xamarin.Google.Crypto.Tink.Internal.KeyParser.IKeyParsingFunction;com.google.crypto.tink.internal.KeyParser$KeyParsingFunction
com.google.crypto.tink.internal.KeyParser$KeyParsingFunction;com.google.crypto.tink.internal.KeyParser$KeyParsingFunction
Xamarin.Google.Crypto.Tink.Internal.KeySerializer+IKeySerializationFunction, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.KeySerializer$KeySerializationFunction
Xamarin.Google.Crypto.Tink.Internal.KeySerializer.IKeySerializationFunction;com.google.crypto.tink.internal.KeySerializer$KeySerializationFunction
com.google.crypto.tink.internal.KeySerializer$KeySerializationFunction;com.google.crypto.tink.internal.KeySerializer$KeySerializationFunction
Xamarin.Google.Crypto.Tink.Internal.MutableKeyCreationRegistry+IKeyCreator, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.MutableKeyCreationRegistry$KeyCreator
Xamarin.Google.Crypto.Tink.Internal.MutableKeyCreationRegistry.IKeyCreator;com.google.crypto.tink.internal.MutableKeyCreationRegistry$KeyCreator
com.google.crypto.tink.internal.MutableKeyCreationRegistry$KeyCreator;com.google.crypto.tink.internal.MutableKeyCreationRegistry$KeyCreator
Xamarin.Google.Crypto.Tink.Internal.MutableKeyDerivationRegistry+IInsecureKeyCreator, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.MutableKeyDerivationRegistry$InsecureKeyCreator
Xamarin.Google.Crypto.Tink.Internal.MutableKeyDerivationRegistry.IInsecureKeyCreator;com.google.crypto.tink.internal.MutableKeyDerivationRegistry$InsecureKeyCreator
com.google.crypto.tink.internal.MutableKeyDerivationRegistry$InsecureKeyCreator;com.google.crypto.tink.internal.MutableKeyDerivationRegistry$InsecureKeyCreator
Xamarin.Google.Crypto.Tink.Internal.ParametersParser+IParametersParsingFunction, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.ParametersParser$ParametersParsingFunction
Xamarin.Google.Crypto.Tink.Internal.ParametersParser.IParametersParsingFunction;com.google.crypto.tink.internal.ParametersParser$ParametersParsingFunction
com.google.crypto.tink.internal.ParametersParser$ParametersParsingFunction;com.google.crypto.tink.internal.ParametersParser$ParametersParsingFunction
Xamarin.Google.Crypto.Tink.Internal.ParametersSerializer+IParametersSerializationFunction, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.ParametersSerializer$ParametersSerializationFunction
Xamarin.Google.Crypto.Tink.Internal.ParametersSerializer.IParametersSerializationFunction;com.google.crypto.tink.internal.ParametersSerializer$ParametersSerializationFunction
com.google.crypto.tink.internal.ParametersSerializer$ParametersSerializationFunction;com.google.crypto.tink.internal.ParametersSerializer$ParametersSerializationFunction
Xamarin.Google.Crypto.Tink.Internal.PrimitiveConstructor+IPrimitiveConstructionFunction, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.PrimitiveConstructor$PrimitiveConstructionFunction
Xamarin.Google.Crypto.Tink.Internal.PrimitiveConstructor.IPrimitiveConstructionFunction;com.google.crypto.tink.internal.PrimitiveConstructor$PrimitiveConstructionFunction
com.google.crypto.tink.internal.PrimitiveConstructor$PrimitiveConstructionFunction;com.google.crypto.tink.internal.PrimitiveConstructor$PrimitiveConstructionFunction
Xamarin.Google.Crypto.Tink.Internal.TinkBugException+IThrowingRunnable, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.TinkBugException$ThrowingRunnable
Xamarin.Google.Crypto.Tink.Internal.TinkBugException.IThrowingRunnable;com.google.crypto.tink.internal.TinkBugException$ThrowingRunnable
com.google.crypto.tink.internal.TinkBugException$ThrowingRunnable;com.google.crypto.tink.internal.TinkBugException$ThrowingRunnable
Xamarin.Google.Crypto.Tink.Internal.TinkBugException+IThrowingSupplier, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.internal.TinkBugException$ThrowingSupplier
Xamarin.Google.Crypto.Tink.Internal.TinkBugException.IThrowingSupplier;com.google.crypto.tink.internal.TinkBugException$ThrowingSupplier
com.google.crypto.tink.internal.TinkBugException$ThrowingSupplier;com.google.crypto.tink.internal.TinkBugException$ThrowingSupplier
Xamarin.Google.Crypto.Tink.Hybrid.Internal.EciesDemHelper+IDem, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.hybrid.internal.EciesDemHelper$Dem
Xamarin.Google.Crypto.Tink.Hybrid.Internal.EciesDemHelper.IDem;com.google.crypto.tink.hybrid.internal.EciesDemHelper$Dem
com.google.crypto.tink.hybrid.internal.EciesDemHelper$Dem;com.google.crypto.tink.hybrid.internal.EciesDemHelper$Dem
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeAead, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.hybrid.internal.HpkeAead
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeAead;com.google.crypto.tink.hybrid.internal.HpkeAead
com.google.crypto.tink.hybrid.internal.HpkeAead;com.google.crypto.tink.hybrid.internal.HpkeAead
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKdf, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.hybrid.internal.HpkeKdf
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKdf;com.google.crypto.tink.hybrid.internal.HpkeKdf
com.google.crypto.tink.hybrid.internal.HpkeKdf;com.google.crypto.tink.hybrid.internal.HpkeKdf
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKem, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.hybrid.internal.HpkeKem
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKem;com.google.crypto.tink.hybrid.internal.HpkeKem
com.google.crypto.tink.hybrid.internal.HpkeKem;com.google.crypto.tink.hybrid.internal.HpkeKem
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKemPrivateKey, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.hybrid.internal.HpkeKemPrivateKey
Xamarin.Google.Crypto.Tink.Hybrid.Internal.IHpkeKemPrivateKey;com.google.crypto.tink.hybrid.internal.HpkeKemPrivateKey
com.google.crypto.tink.hybrid.internal.HpkeKemPrivateKey;com.google.crypto.tink.hybrid.internal.HpkeKemPrivateKey
Xamarin.Google.Crypto.Tink.Annotations.IAlpha, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.annotations.Alpha
Xamarin.Google.Crypto.Tink.Annotations.IAlpha;com.google.crypto.tink.annotations.Alpha
com.google.crypto.tink.annotations.Alpha;com.google.crypto.tink.annotations.Alpha
Xamarin.Google.Crypto.Tink.Aead.Subtle.IAeadFactory, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.aead.subtle.AeadFactory
Xamarin.Google.Crypto.Tink.Aead.Subtle.IAeadFactory;com.google.crypto.tink.aead.subtle.AeadFactory
com.google.crypto.tink.aead.subtle.AeadFactory;com.google.crypto.tink.aead.subtle.AeadFactory
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+MapAdapter+IConverter, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$MapAdapter$Converter
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.MapAdapter.IConverter;com.google.crypto.tink.shaded.protobuf.Internal$MapAdapter$Converter
com.google.crypto.tink.shaded.protobuf.Internal$MapAdapter$Converter;com.google.crypto.tink.shaded.protobuf.Internal$MapAdapter$Converter
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IBooleanList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$BooleanList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IBooleanList;com.google.crypto.tink.shaded.protobuf.Internal$BooleanList
com.google.crypto.tink.shaded.protobuf.Internal$BooleanList;com.google.crypto.tink.shaded.protobuf.Internal$BooleanList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IDoubleList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$DoubleList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IDoubleList;com.google.crypto.tink.shaded.protobuf.Internal$DoubleList
com.google.crypto.tink.shaded.protobuf.Internal$DoubleList;com.google.crypto.tink.shaded.protobuf.Internal$DoubleList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IEnumLite, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$EnumLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IEnumLite;com.google.crypto.tink.shaded.protobuf.Internal$EnumLite
com.google.crypto.tink.shaded.protobuf.Internal$EnumLite;com.google.crypto.tink.shaded.protobuf.Internal$EnumLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IEnumLiteMap, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$EnumLiteMap
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IEnumLiteMap;com.google.crypto.tink.shaded.protobuf.Internal$EnumLiteMap
com.google.crypto.tink.shaded.protobuf.Internal$EnumLiteMap;com.google.crypto.tink.shaded.protobuf.Internal$EnumLiteMap
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IEnumVerifier, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$EnumVerifier
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IEnumVerifier;com.google.crypto.tink.shaded.protobuf.Internal$EnumVerifier
com.google.crypto.tink.shaded.protobuf.Internal$EnumVerifier;com.google.crypto.tink.shaded.protobuf.Internal$EnumVerifier
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IFloatList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$FloatList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IFloatList;com.google.crypto.tink.shaded.protobuf.Internal$FloatList
com.google.crypto.tink.shaded.protobuf.Internal$FloatList;com.google.crypto.tink.shaded.protobuf.Internal$FloatList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IIntList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$IntList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IIntList;com.google.crypto.tink.shaded.protobuf.Internal$IntList
com.google.crypto.tink.shaded.protobuf.Internal$IntList;com.google.crypto.tink.shaded.protobuf.Internal$IntList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+ListAdapter+IConverter, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$ListAdapter$Converter
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.ListAdapter.IConverter;com.google.crypto.tink.shaded.protobuf.Internal$ListAdapter$Converter
com.google.crypto.tink.shaded.protobuf.Internal$ListAdapter$Converter;com.google.crypto.tink.shaded.protobuf.Internal$ListAdapter$Converter
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+ILongList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$LongList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.ILongList;com.google.crypto.tink.shaded.protobuf.Internal$LongList
com.google.crypto.tink.shaded.protobuf.Internal$LongList;com.google.crypto.tink.shaded.protobuf.Internal$LongList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal+IProtobufList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.Internal.IProtobufList;com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList
com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList;com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.AbstractMessageLite+IInternalOneOfEnum, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.AbstractMessageLite$InternalOneOfEnum
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.AbstractMessageLite.IInternalOneOfEnum;com.google.crypto.tink.shaded.protobuf.AbstractMessageLite$InternalOneOfEnum
com.google.crypto.tink.shaded.protobuf.AbstractMessageLite$InternalOneOfEnum;com.google.crypto.tink.shaded.protobuf.AbstractMessageLite$InternalOneOfEnum
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ByteString+IByteIterator, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ByteString$ByteIterator
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ByteString.IByteIterator;com.google.crypto.tink.shaded.protobuf.ByteString$ByteIterator
com.google.crypto.tink.shaded.protobuf.ByteString$ByteIterator;com.google.crypto.tink.shaded.protobuf.ByteString$ByteIterator
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.FieldSet+IFieldDescriptorLite, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.FieldSet$FieldDescriptorLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.FieldSet.IFieldDescriptorLite;com.google.crypto.tink.shaded.protobuf.FieldSet$FieldDescriptorLite
com.google.crypto.tink.shaded.protobuf.FieldSet$FieldDescriptorLite;com.google.crypto.tink.shaded.protobuf.FieldSet$FieldDescriptorLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.GeneratedMessageLite+IExtendableMessageOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.GeneratedMessageLite.IExtendableMessageOrBuilder;com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder;com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IAnyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.AnyOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IAnyOrBuilder;com.google.crypto.tink.shaded.protobuf.AnyOrBuilder
com.google.crypto.tink.shaded.protobuf.AnyOrBuilder;com.google.crypto.tink.shaded.protobuf.AnyOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IApiOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ApiOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IApiOrBuilder;com.google.crypto.tink.shaded.protobuf.ApiOrBuilder
com.google.crypto.tink.shaded.protobuf.ApiOrBuilder;com.google.crypto.tink.shaded.protobuf.ApiOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IBoolValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.BoolValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IBoolValueOrBuilder;com.google.crypto.tink.shaded.protobuf.BoolValueOrBuilder
com.google.crypto.tink.shaded.protobuf.BoolValueOrBuilder;com.google.crypto.tink.shaded.protobuf.BoolValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IBytesValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.BytesValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IBytesValueOrBuilder;com.google.crypto.tink.shaded.protobuf.BytesValueOrBuilder
com.google.crypto.tink.shaded.protobuf.BytesValueOrBuilder;com.google.crypto.tink.shaded.protobuf.BytesValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IDoubleValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.DoubleValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IDoubleValueOrBuilder;com.google.crypto.tink.shaded.protobuf.DoubleValueOrBuilder
com.google.crypto.tink.shaded.protobuf.DoubleValueOrBuilder;com.google.crypto.tink.shaded.protobuf.DoubleValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IDurationOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.DurationOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IDurationOrBuilder;com.google.crypto.tink.shaded.protobuf.DurationOrBuilder
com.google.crypto.tink.shaded.protobuf.DurationOrBuilder;com.google.crypto.tink.shaded.protobuf.DurationOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEmptyOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.EmptyOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEmptyOrBuilder;com.google.crypto.tink.shaded.protobuf.EmptyOrBuilder
com.google.crypto.tink.shaded.protobuf.EmptyOrBuilder;com.google.crypto.tink.shaded.protobuf.EmptyOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEnumOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.EnumOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEnumOrBuilder;com.google.crypto.tink.shaded.protobuf.EnumOrBuilder
com.google.crypto.tink.shaded.protobuf.EnumOrBuilder;com.google.crypto.tink.shaded.protobuf.EnumOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEnumValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.EnumValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IEnumValueOrBuilder;com.google.crypto.tink.shaded.protobuf.EnumValueOrBuilder
com.google.crypto.tink.shaded.protobuf.EnumValueOrBuilder;com.google.crypto.tink.shaded.protobuf.EnumValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IExperimentalApi, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ExperimentalApi
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IExperimentalApi;com.google.crypto.tink.shaded.protobuf.ExperimentalApi
com.google.crypto.tink.shaded.protobuf.ExperimentalApi;com.google.crypto.tink.shaded.protobuf.ExperimentalApi
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IFieldMaskOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.FieldMaskOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IFieldMaskOrBuilder;com.google.crypto.tink.shaded.protobuf.FieldMaskOrBuilder
com.google.crypto.tink.shaded.protobuf.FieldMaskOrBuilder;com.google.crypto.tink.shaded.protobuf.FieldMaskOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IFloatValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.FloatValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IFloatValueOrBuilder;com.google.crypto.tink.shaded.protobuf.FloatValueOrBuilder
com.google.crypto.tink.shaded.protobuf.FloatValueOrBuilder;com.google.crypto.tink.shaded.protobuf.FloatValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IInt32ValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Int32ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IInt32ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.Int32ValueOrBuilder
com.google.crypto.tink.shaded.protobuf.Int32ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.Int32ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IInt64ValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Int64ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IInt64ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.Int64ValueOrBuilder
com.google.crypto.tink.shaded.protobuf.Int64ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.Int64ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ILazyStringList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.LazyStringList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ILazyStringList;com.google.crypto.tink.shaded.protobuf.LazyStringList
com.google.crypto.tink.shaded.protobuf.LazyStringList;com.google.crypto.tink.shaded.protobuf.LazyStringList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IListValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ListValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IListValueOrBuilder;com.google.crypto.tink.shaded.protobuf.ListValueOrBuilder
com.google.crypto.tink.shaded.protobuf.ListValueOrBuilder;com.google.crypto.tink.shaded.protobuf.ListValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLiteBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.MessageLite$Builder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLiteBuilder;com.google.crypto.tink.shaded.protobuf.MessageLite$Builder
com.google.crypto.tink.shaded.protobuf.MessageLite$Builder;com.google.crypto.tink.shaded.protobuf.MessageLite$Builder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLite, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.MessageLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLite;com.google.crypto.tink.shaded.protobuf.MessageLite
com.google.crypto.tink.shaded.protobuf.MessageLite;com.google.crypto.tink.shaded.protobuf.MessageLite
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLiteOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.MessageLiteOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMessageLiteOrBuilder;com.google.crypto.tink.shaded.protobuf.MessageLiteOrBuilder
com.google.crypto.tink.shaded.protobuf.MessageLiteOrBuilder;com.google.crypto.tink.shaded.protobuf.MessageLiteOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMethodOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.MethodOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMethodOrBuilder;com.google.crypto.tink.shaded.protobuf.MethodOrBuilder
com.google.crypto.tink.shaded.protobuf.MethodOrBuilder;com.google.crypto.tink.shaded.protobuf.MethodOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMixinOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.MixinOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IMixinOrBuilder;com.google.crypto.tink.shaded.protobuf.MixinOrBuilder
com.google.crypto.tink.shaded.protobuf.MixinOrBuilder;com.google.crypto.tink.shaded.protobuf.MixinOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IOptionOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.OptionOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IOptionOrBuilder;com.google.crypto.tink.shaded.protobuf.OptionOrBuilder
com.google.crypto.tink.shaded.protobuf.OptionOrBuilder;com.google.crypto.tink.shaded.protobuf.OptionOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IParser, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.Parser
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IParser;com.google.crypto.tink.shaded.protobuf.Parser
com.google.crypto.tink.shaded.protobuf.Parser;com.google.crypto.tink.shaded.protobuf.Parser
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IProtocolStringList, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ProtocolStringList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IProtocolStringList;com.google.crypto.tink.shaded.protobuf.ProtocolStringList
com.google.crypto.tink.shaded.protobuf.ProtocolStringList;com.google.crypto.tink.shaded.protobuf.ProtocolStringList
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ISourceContextOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.SourceContextOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ISourceContextOrBuilder;com.google.crypto.tink.shaded.protobuf.SourceContextOrBuilder
com.google.crypto.tink.shaded.protobuf.SourceContextOrBuilder;com.google.crypto.tink.shaded.protobuf.SourceContextOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IStringValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.StringValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IStringValueOrBuilder;com.google.crypto.tink.shaded.protobuf.StringValueOrBuilder
com.google.crypto.tink.shaded.protobuf.StringValueOrBuilder;com.google.crypto.tink.shaded.protobuf.StringValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IStructOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.StructOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IStructOrBuilder;com.google.crypto.tink.shaded.protobuf.StructOrBuilder
com.google.crypto.tink.shaded.protobuf.StructOrBuilder;com.google.crypto.tink.shaded.protobuf.StructOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ITimestampOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.TimestampOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.ITimestampOrBuilder;com.google.crypto.tink.shaded.protobuf.TimestampOrBuilder
com.google.crypto.tink.shaded.protobuf.TimestampOrBuilder;com.google.crypto.tink.shaded.protobuf.TimestampOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IUInt32ValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.UInt32ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IUInt32ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.UInt32ValueOrBuilder
com.google.crypto.tink.shaded.protobuf.UInt32ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.UInt32ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IUInt64ValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.UInt64ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IUInt64ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.UInt64ValueOrBuilder
com.google.crypto.tink.shaded.protobuf.UInt64ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.UInt64ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IValueOrBuilder, Xamarin.Google.Crypto.Tink.Android;com.google.crypto.tink.shaded.protobuf.ValueOrBuilder
Xamarin.Google.Crypto.Tink.Shaded.Protobuf.IValueOrBuilder;com.google.crypto.tink.shaded.protobuf.ValueOrBuilder
com.google.crypto.tink.shaded.protobuf.ValueOrBuilder;com.google.crypto.tink.shaded.protobuf.ValueOrBuilder
Xamarin.Google.ErrorProne.Annotations.ICanIgnoreReturnValue, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.CanIgnoreReturnValue
Xamarin.Google.ErrorProne.Annotations.ICanIgnoreReturnValue;com.google.errorprone.annotations.CanIgnoreReturnValue
com.google.errorprone.annotations.CanIgnoreReturnValue;com.google.errorprone.annotations.CanIgnoreReturnValue
Xamarin.Google.ErrorProne.Annotations.ICheckReturnValue, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.CheckReturnValue
Xamarin.Google.ErrorProne.Annotations.ICheckReturnValue;com.google.errorprone.annotations.CheckReturnValue
com.google.errorprone.annotations.CheckReturnValue;com.google.errorprone.annotations.CheckReturnValue
Xamarin.Google.ErrorProne.Annotations.ICompatibleWith, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.CompatibleWith
Xamarin.Google.ErrorProne.Annotations.ICompatibleWith;com.google.errorprone.annotations.CompatibleWith
com.google.errorprone.annotations.CompatibleWith;com.google.errorprone.annotations.CompatibleWith
Xamarin.Google.ErrorProne.Annotations.ICompileTimeConstant, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.CompileTimeConstant
Xamarin.Google.ErrorProne.Annotations.ICompileTimeConstant;com.google.errorprone.annotations.CompileTimeConstant
com.google.errorprone.annotations.CompileTimeConstant;com.google.errorprone.annotations.CompileTimeConstant
Xamarin.Google.ErrorProne.Annotations.IDoNotCall, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.DoNotCall
Xamarin.Google.ErrorProne.Annotations.IDoNotCall;com.google.errorprone.annotations.DoNotCall
com.google.errorprone.annotations.DoNotCall;com.google.errorprone.annotations.DoNotCall
Xamarin.Google.ErrorProne.Annotations.IDoNotMock, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.DoNotMock
Xamarin.Google.ErrorProne.Annotations.IDoNotMock;com.google.errorprone.annotations.DoNotMock
com.google.errorprone.annotations.DoNotMock;com.google.errorprone.annotations.DoNotMock
Xamarin.Google.ErrorProne.Annotations.IFormatMethod, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.FormatMethod
Xamarin.Google.ErrorProne.Annotations.IFormatMethod;com.google.errorprone.annotations.FormatMethod
com.google.errorprone.annotations.FormatMethod;com.google.errorprone.annotations.FormatMethod
Xamarin.Google.ErrorProne.Annotations.IFormatString, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.FormatString
Xamarin.Google.ErrorProne.Annotations.IFormatString;com.google.errorprone.annotations.FormatString
com.google.errorprone.annotations.FormatString;com.google.errorprone.annotations.FormatString
Xamarin.Google.ErrorProne.Annotations.IForOverride, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.ForOverride
Xamarin.Google.ErrorProne.Annotations.IForOverride;com.google.errorprone.annotations.ForOverride
com.google.errorprone.annotations.ForOverride;com.google.errorprone.annotations.ForOverride
Xamarin.Google.ErrorProne.Annotations.IImmutable, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.Immutable
Xamarin.Google.ErrorProne.Annotations.IImmutable;com.google.errorprone.annotations.Immutable
com.google.errorprone.annotations.Immutable;com.google.errorprone.annotations.Immutable
Xamarin.Google.ErrorProne.Annotations.IIncompatibleModifiers, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.IncompatibleModifiers
Xamarin.Google.ErrorProne.Annotations.IIncompatibleModifiers;com.google.errorprone.annotations.IncompatibleModifiers
com.google.errorprone.annotations.IncompatibleModifiers;com.google.errorprone.annotations.IncompatibleModifiers
Xamarin.Google.ErrorProne.Annotations.IInlineMe, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.InlineMe
Xamarin.Google.ErrorProne.Annotations.IInlineMe;com.google.errorprone.annotations.InlineMe
com.google.errorprone.annotations.InlineMe;com.google.errorprone.annotations.InlineMe
Xamarin.Google.ErrorProne.Annotations.IInlineMeValidationDisabled, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.InlineMeValidationDisabled
Xamarin.Google.ErrorProne.Annotations.IInlineMeValidationDisabled;com.google.errorprone.annotations.InlineMeValidationDisabled
com.google.errorprone.annotations.InlineMeValidationDisabled;com.google.errorprone.annotations.InlineMeValidationDisabled
Xamarin.Google.ErrorProne.Annotations.IKeep, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.Keep
Xamarin.Google.ErrorProne.Annotations.IKeep;com.google.errorprone.annotations.Keep
com.google.errorprone.annotations.Keep;com.google.errorprone.annotations.Keep
Xamarin.Google.ErrorProne.Annotations.IMustBeClosed, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.MustBeClosed
Xamarin.Google.ErrorProne.Annotations.IMustBeClosed;com.google.errorprone.annotations.MustBeClosed
com.google.errorprone.annotations.MustBeClosed;com.google.errorprone.annotations.MustBeClosed
Xamarin.Google.ErrorProne.Annotations.INoAllocation, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.NoAllocation
Xamarin.Google.ErrorProne.Annotations.INoAllocation;com.google.errorprone.annotations.NoAllocation
com.google.errorprone.annotations.NoAllocation;com.google.errorprone.annotations.NoAllocation
Xamarin.Google.ErrorProne.Annotations.IOverridingMethodsMustInvokeSuper, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.OverridingMethodsMustInvokeSuper
Xamarin.Google.ErrorProne.Annotations.IOverridingMethodsMustInvokeSuper;com.google.errorprone.annotations.OverridingMethodsMustInvokeSuper
com.google.errorprone.annotations.OverridingMethodsMustInvokeSuper;com.google.errorprone.annotations.OverridingMethodsMustInvokeSuper
Xamarin.Google.ErrorProne.Annotations.IRequiredModifiers, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.RequiredModifiers
Xamarin.Google.ErrorProne.Annotations.IRequiredModifiers;com.google.errorprone.annotations.RequiredModifiers
com.google.errorprone.annotations.RequiredModifiers;com.google.errorprone.annotations.RequiredModifiers
Xamarin.Google.ErrorProne.Annotations.IRestrictedApi, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.RestrictedApi
Xamarin.Google.ErrorProne.Annotations.IRestrictedApi;com.google.errorprone.annotations.RestrictedApi
com.google.errorprone.annotations.RestrictedApi;com.google.errorprone.annotations.RestrictedApi
Xamarin.Google.ErrorProne.Annotations.ISuppressPackageLocation, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.SuppressPackageLocation
Xamarin.Google.ErrorProne.Annotations.ISuppressPackageLocation;com.google.errorprone.annotations.SuppressPackageLocation
com.google.errorprone.annotations.SuppressPackageLocation;com.google.errorprone.annotations.SuppressPackageLocation
Xamarin.Google.ErrorProne.Annotations.IThreadSafe, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.ThreadSafe
Xamarin.Google.ErrorProne.Annotations.IThreadSafe;com.google.errorprone.annotations.ThreadSafe
com.google.errorprone.annotations.ThreadSafe;com.google.errorprone.annotations.ThreadSafe
Xamarin.Google.ErrorProne.Annotations.IVar, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.Var
Xamarin.Google.ErrorProne.Annotations.IVar;com.google.errorprone.annotations.Var
com.google.errorprone.annotations.Var;com.google.errorprone.annotations.Var
Xamarin.Google.ErrorProne.Annotations.Concurrent.IGuardedBy, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.concurrent.GuardedBy
Xamarin.Google.ErrorProne.Annotations.Concurrent.IGuardedBy;com.google.errorprone.annotations.concurrent.GuardedBy
com.google.errorprone.annotations.concurrent.GuardedBy;com.google.errorprone.annotations.concurrent.GuardedBy
Xamarin.Google.ErrorProne.Annotations.Concurrent.ILazyInit, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.concurrent.LazyInit
Xamarin.Google.ErrorProne.Annotations.Concurrent.ILazyInit;com.google.errorprone.annotations.concurrent.LazyInit
com.google.errorprone.annotations.concurrent.LazyInit;com.google.errorprone.annotations.concurrent.LazyInit
Xamarin.Google.ErrorProne.Annotations.Concurrent.ILockMethod, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.concurrent.LockMethod
Xamarin.Google.ErrorProne.Annotations.Concurrent.ILockMethod;com.google.errorprone.annotations.concurrent.LockMethod
com.google.errorprone.annotations.concurrent.LockMethod;com.google.errorprone.annotations.concurrent.LockMethod
Xamarin.Google.ErrorProne.Annotations.Concurrent.IUnlockMethod, Xamarin.Google.ErrorProne.Annotations;com.google.errorprone.annotations.concurrent.UnlockMethod
Xamarin.Google.ErrorProne.Annotations.Concurrent.IUnlockMethod;com.google.errorprone.annotations.concurrent.UnlockMethod
com.google.errorprone.annotations.concurrent.UnlockMethod;com.google.errorprone.annotations.concurrent.UnlockMethod
Google.Common.Util.Concurrent.IListenableFuture, Xamarin.Google.Guava.ListenableFuture;com.google.common.util.concurrent.ListenableFuture
Google.Common.Util.Concurrent.IListenableFuture;com.google.common.util.concurrent.ListenableFuture
com.google.common.util.concurrent.ListenableFuture;com.google.common.util.concurrent.ListenableFuture
JetBrains.Annotations.ApiStatus+IAvailableSince, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$AvailableSince
JetBrains.Annotations.ApiStatus.IAvailableSince;org.jetbrains.annotations.ApiStatus$AvailableSince
org.jetbrains.annotations.ApiStatus$AvailableSince;org.jetbrains.annotations.ApiStatus$AvailableSince
JetBrains.Annotations.ApiStatus+IExperimental, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$Experimental
JetBrains.Annotations.ApiStatus.IExperimental;org.jetbrains.annotations.ApiStatus$Experimental
org.jetbrains.annotations.ApiStatus$Experimental;org.jetbrains.annotations.ApiStatus$Experimental
JetBrains.Annotations.ApiStatus+IInternal, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$Internal
JetBrains.Annotations.ApiStatus.IInternal;org.jetbrains.annotations.ApiStatus$Internal
org.jetbrains.annotations.ApiStatus$Internal;org.jetbrains.annotations.ApiStatus$Internal
JetBrains.Annotations.ApiStatus+INonExtendable, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$NonExtendable
JetBrains.Annotations.ApiStatus.INonExtendable;org.jetbrains.annotations.ApiStatus$NonExtendable
org.jetbrains.annotations.ApiStatus$NonExtendable;org.jetbrains.annotations.ApiStatus$NonExtendable
JetBrains.Annotations.ApiStatus+IObsolete, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$Obsolete
JetBrains.Annotations.ApiStatus.IObsolete;org.jetbrains.annotations.ApiStatus$Obsolete
org.jetbrains.annotations.ApiStatus$Obsolete;org.jetbrains.annotations.ApiStatus$Obsolete
JetBrains.Annotations.ApiStatus+IOverrideOnly, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$OverrideOnly
JetBrains.Annotations.ApiStatus.IOverrideOnly;org.jetbrains.annotations.ApiStatus$OverrideOnly
org.jetbrains.annotations.ApiStatus$OverrideOnly;org.jetbrains.annotations.ApiStatus$OverrideOnly
JetBrains.Annotations.ApiStatus+IScheduledForRemoval, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.ApiStatus$ScheduledForRemoval
JetBrains.Annotations.ApiStatus.IScheduledForRemoval;org.jetbrains.annotations.ApiStatus$ScheduledForRemoval
org.jetbrains.annotations.ApiStatus$ScheduledForRemoval;org.jetbrains.annotations.ApiStatus$ScheduledForRemoval
JetBrains.Annotations.Async+IExecute, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Async$Execute
JetBrains.Annotations.Async.IExecute;org.jetbrains.annotations.Async$Execute
org.jetbrains.annotations.Async$Execute;org.jetbrains.annotations.Async$Execute
JetBrains.Annotations.Async+ISchedule, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Async$Schedule
JetBrains.Annotations.Async.ISchedule;org.jetbrains.annotations.Async$Schedule
org.jetbrains.annotations.Async$Schedule;org.jetbrains.annotations.Async$Schedule
JetBrains.Annotations.Debug+IRenderer, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Debug$Renderer
JetBrains.Annotations.Debug.IRenderer;org.jetbrains.annotations.Debug$Renderer
org.jetbrains.annotations.Debug$Renderer;org.jetbrains.annotations.Debug$Renderer
JetBrains.Annotations.IBlocking, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Blocking
JetBrains.Annotations.IBlocking;org.jetbrains.annotations.Blocking
org.jetbrains.annotations.Blocking;org.jetbrains.annotations.Blocking
JetBrains.Annotations.IBlockingExecutor, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.BlockingExecutor
JetBrains.Annotations.IBlockingExecutor;org.jetbrains.annotations.BlockingExecutor
org.jetbrains.annotations.BlockingExecutor;org.jetbrains.annotations.BlockingExecutor
JetBrains.Annotations.ICheckReturnValue, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.CheckReturnValue
JetBrains.Annotations.ICheckReturnValue;org.jetbrains.annotations.CheckReturnValue
org.jetbrains.annotations.CheckReturnValue;org.jetbrains.annotations.CheckReturnValue
JetBrains.Annotations.IContract, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Contract
JetBrains.Annotations.IContract;org.jetbrains.annotations.Contract
org.jetbrains.annotations.Contract;org.jetbrains.annotations.Contract
JetBrains.Annotations.IMustBeInvokedByOverriders, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.MustBeInvokedByOverriders
JetBrains.Annotations.IMustBeInvokedByOverriders;org.jetbrains.annotations.MustBeInvokedByOverriders
org.jetbrains.annotations.MustBeInvokedByOverriders;org.jetbrains.annotations.MustBeInvokedByOverriders
JetBrains.Annotations.INls, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Nls
JetBrains.Annotations.INls;org.jetbrains.annotations.Nls
org.jetbrains.annotations.Nls;org.jetbrains.annotations.Nls
JetBrains.Annotations.INonBlocking, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.NonBlocking
JetBrains.Annotations.INonBlocking;org.jetbrains.annotations.NonBlocking
org.jetbrains.annotations.NonBlocking;org.jetbrains.annotations.NonBlocking
JetBrains.Annotations.INonBlockingExecutor, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.NonBlockingExecutor
JetBrains.Annotations.INonBlockingExecutor;org.jetbrains.annotations.NonBlockingExecutor
org.jetbrains.annotations.NonBlockingExecutor;org.jetbrains.annotations.NonBlockingExecutor
JetBrains.Annotations.INonNls, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.NonNls
JetBrains.Annotations.INonNls;org.jetbrains.annotations.NonNls
org.jetbrains.annotations.NonNls;org.jetbrains.annotations.NonNls
JetBrains.Annotations.INotNull, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.NotNull
JetBrains.Annotations.INotNull;org.jetbrains.annotations.NotNull
org.jetbrains.annotations.NotNull;org.jetbrains.annotations.NotNull
JetBrains.Annotations.INullable, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Nullable
JetBrains.Annotations.INullable;org.jetbrains.annotations.Nullable
org.jetbrains.annotations.Nullable;org.jetbrains.annotations.Nullable
JetBrains.Annotations.IPropertyKey, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.PropertyKey
JetBrains.Annotations.IPropertyKey;org.jetbrains.annotations.PropertyKey
org.jetbrains.annotations.PropertyKey;org.jetbrains.annotations.PropertyKey
JetBrains.Annotations.IRange, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Range
JetBrains.Annotations.IRange;org.jetbrains.annotations.Range
org.jetbrains.annotations.Range;org.jetbrains.annotations.Range
JetBrains.Annotations.ITestOnly, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.TestOnly
JetBrains.Annotations.ITestOnly;org.jetbrains.annotations.TestOnly
org.jetbrains.annotations.TestOnly;org.jetbrains.annotations.TestOnly
JetBrains.Annotations.IUnknownNullability, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.UnknownNullability
JetBrains.Annotations.IUnknownNullability;org.jetbrains.annotations.UnknownNullability
org.jetbrains.annotations.UnknownNullability;org.jetbrains.annotations.UnknownNullability
JetBrains.Annotations.IUnmodifiable, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.Unmodifiable
JetBrains.Annotations.IUnmodifiable;org.jetbrains.annotations.Unmodifiable
org.jetbrains.annotations.Unmodifiable;org.jetbrains.annotations.Unmodifiable
JetBrains.Annotations.IUnmodifiableView, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.UnmodifiableView
JetBrains.Annotations.IUnmodifiableView;org.jetbrains.annotations.UnmodifiableView
org.jetbrains.annotations.UnmodifiableView;org.jetbrains.annotations.UnmodifiableView
JetBrains.Annotations.IVisibleForTesting, Xamarin.Jetbrains.Annotations;org.jetbrains.annotations.VisibleForTesting
JetBrains.Annotations.IVisibleForTesting;org.jetbrains.annotations.VisibleForTesting
org.jetbrains.annotations.VisibleForTesting;org.jetbrains.annotations.VisibleForTesting
IntelliJ.Lang.Annotations.IFlow, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.Flow
IntelliJ.Lang.Annotations.IFlow;org.intellij.lang.annotations.Flow
org.intellij.lang.annotations.Flow;org.intellij.lang.annotations.Flow
IntelliJ.Lang.Annotations.IIdentifier, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.Identifier
IntelliJ.Lang.Annotations.IIdentifier;org.intellij.lang.annotations.Identifier
org.intellij.lang.annotations.Identifier;org.intellij.lang.annotations.Identifier
IntelliJ.Lang.Annotations.ILanguage, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.Language
IntelliJ.Lang.Annotations.ILanguage;org.intellij.lang.annotations.Language
org.intellij.lang.annotations.Language;org.intellij.lang.annotations.Language
IntelliJ.Lang.Annotations.IMagicConstant, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.MagicConstant
IntelliJ.Lang.Annotations.IMagicConstant;org.intellij.lang.annotations.MagicConstant
org.intellij.lang.annotations.MagicConstant;org.intellij.lang.annotations.MagicConstant
IntelliJ.Lang.Annotations.IPattern, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.Pattern
IntelliJ.Lang.Annotations.IPattern;org.intellij.lang.annotations.Pattern
org.intellij.lang.annotations.Pattern;org.intellij.lang.annotations.Pattern
IntelliJ.Lang.Annotations.IPrintFormat, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.PrintFormat
IntelliJ.Lang.Annotations.IPrintFormat;org.intellij.lang.annotations.PrintFormat
org.intellij.lang.annotations.PrintFormat;org.intellij.lang.annotations.PrintFormat
IntelliJ.Lang.Annotations.IRegExp, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.RegExp
IntelliJ.Lang.Annotations.IRegExp;org.intellij.lang.annotations.RegExp
org.intellij.lang.annotations.RegExp;org.intellij.lang.annotations.RegExp
IntelliJ.Lang.Annotations.ISubst, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.Subst
IntelliJ.Lang.Annotations.ISubst;org.intellij.lang.annotations.Subst
org.intellij.lang.annotations.Subst;org.intellij.lang.annotations.Subst
IntelliJ.Lang.Annotations.JdkConstants+IAdjustableOrientation, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$AdjustableOrientation
IntelliJ.Lang.Annotations.JdkConstants.IAdjustableOrientation;org.intellij.lang.annotations.JdkConstants$AdjustableOrientation
org.intellij.lang.annotations.JdkConstants$AdjustableOrientation;org.intellij.lang.annotations.JdkConstants$AdjustableOrientation
IntelliJ.Lang.Annotations.JdkConstants+IBoxLayoutAxis, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$BoxLayoutAxis
IntelliJ.Lang.Annotations.JdkConstants.IBoxLayoutAxis;org.intellij.lang.annotations.JdkConstants$BoxLayoutAxis
org.intellij.lang.annotations.JdkConstants$BoxLayoutAxis;org.intellij.lang.annotations.JdkConstants$BoxLayoutAxis
IntelliJ.Lang.Annotations.JdkConstants+ICalendarMonth, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$CalendarMonth
IntelliJ.Lang.Annotations.JdkConstants.ICalendarMonth;org.intellij.lang.annotations.JdkConstants$CalendarMonth
org.intellij.lang.annotations.JdkConstants$CalendarMonth;org.intellij.lang.annotations.JdkConstants$CalendarMonth
IntelliJ.Lang.Annotations.JdkConstants+ICursorType, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$CursorType
IntelliJ.Lang.Annotations.JdkConstants.ICursorType;org.intellij.lang.annotations.JdkConstants$CursorType
org.intellij.lang.annotations.JdkConstants$CursorType;org.intellij.lang.annotations.JdkConstants$CursorType
IntelliJ.Lang.Annotations.JdkConstants+IFlowLayoutAlignment, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$FlowLayoutAlignment
IntelliJ.Lang.Annotations.JdkConstants.IFlowLayoutAlignment;org.intellij.lang.annotations.JdkConstants$FlowLayoutAlignment
org.intellij.lang.annotations.JdkConstants$FlowLayoutAlignment;org.intellij.lang.annotations.JdkConstants$FlowLayoutAlignment
IntelliJ.Lang.Annotations.JdkConstants+IFontStyle, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$FontStyle
IntelliJ.Lang.Annotations.JdkConstants.IFontStyle;org.intellij.lang.annotations.JdkConstants$FontStyle
org.intellij.lang.annotations.JdkConstants$FontStyle;org.intellij.lang.annotations.JdkConstants$FontStyle
IntelliJ.Lang.Annotations.JdkConstants+IHorizontalAlignment, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$HorizontalAlignment
IntelliJ.Lang.Annotations.JdkConstants.IHorizontalAlignment;org.intellij.lang.annotations.JdkConstants$HorizontalAlignment
org.intellij.lang.annotations.JdkConstants$HorizontalAlignment;org.intellij.lang.annotations.JdkConstants$HorizontalAlignment
IntelliJ.Lang.Annotations.JdkConstants+IHorizontalScrollBarPolicy, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$HorizontalScrollBarPolicy
IntelliJ.Lang.Annotations.JdkConstants.IHorizontalScrollBarPolicy;org.intellij.lang.annotations.JdkConstants$HorizontalScrollBarPolicy
org.intellij.lang.annotations.JdkConstants$HorizontalScrollBarPolicy;org.intellij.lang.annotations.JdkConstants$HorizontalScrollBarPolicy
IntelliJ.Lang.Annotations.JdkConstants+IInputEventMask, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$InputEventMask
IntelliJ.Lang.Annotations.JdkConstants.IInputEventMask;org.intellij.lang.annotations.JdkConstants$InputEventMask
org.intellij.lang.annotations.JdkConstants$InputEventMask;org.intellij.lang.annotations.JdkConstants$InputEventMask
IntelliJ.Lang.Annotations.JdkConstants+IListSelectionMode, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$ListSelectionMode
IntelliJ.Lang.Annotations.JdkConstants.IListSelectionMode;org.intellij.lang.annotations.JdkConstants$ListSelectionMode
org.intellij.lang.annotations.JdkConstants$ListSelectionMode;org.intellij.lang.annotations.JdkConstants$ListSelectionMode
IntelliJ.Lang.Annotations.JdkConstants+IPatternFlags, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$PatternFlags
IntelliJ.Lang.Annotations.JdkConstants.IPatternFlags;org.intellij.lang.annotations.JdkConstants$PatternFlags
org.intellij.lang.annotations.JdkConstants$PatternFlags;org.intellij.lang.annotations.JdkConstants$PatternFlags
IntelliJ.Lang.Annotations.JdkConstants+ITabLayoutPolicy, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$TabLayoutPolicy
IntelliJ.Lang.Annotations.JdkConstants.ITabLayoutPolicy;org.intellij.lang.annotations.JdkConstants$TabLayoutPolicy
org.intellij.lang.annotations.JdkConstants$TabLayoutPolicy;org.intellij.lang.annotations.JdkConstants$TabLayoutPolicy
IntelliJ.Lang.Annotations.JdkConstants+ITabPlacement, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$TabPlacement
IntelliJ.Lang.Annotations.JdkConstants.ITabPlacement;org.intellij.lang.annotations.JdkConstants$TabPlacement
org.intellij.lang.annotations.JdkConstants$TabPlacement;org.intellij.lang.annotations.JdkConstants$TabPlacement
IntelliJ.Lang.Annotations.JdkConstants+ITitledBorderJustification, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$TitledBorderJustification
IntelliJ.Lang.Annotations.JdkConstants.ITitledBorderJustification;org.intellij.lang.annotations.JdkConstants$TitledBorderJustification
org.intellij.lang.annotations.JdkConstants$TitledBorderJustification;org.intellij.lang.annotations.JdkConstants$TitledBorderJustification
IntelliJ.Lang.Annotations.JdkConstants+ITitledBorderTitlePosition, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$TitledBorderTitlePosition
IntelliJ.Lang.Annotations.JdkConstants.ITitledBorderTitlePosition;org.intellij.lang.annotations.JdkConstants$TitledBorderTitlePosition
org.intellij.lang.annotations.JdkConstants$TitledBorderTitlePosition;org.intellij.lang.annotations.JdkConstants$TitledBorderTitlePosition
IntelliJ.Lang.Annotations.JdkConstants+ITreeSelectionMode, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$TreeSelectionMode
IntelliJ.Lang.Annotations.JdkConstants.ITreeSelectionMode;org.intellij.lang.annotations.JdkConstants$TreeSelectionMode
org.intellij.lang.annotations.JdkConstants$TreeSelectionMode;org.intellij.lang.annotations.JdkConstants$TreeSelectionMode
IntelliJ.Lang.Annotations.JdkConstants+IVerticalScrollBarPolicy, Xamarin.Jetbrains.Annotations;org.intellij.lang.annotations.JdkConstants$VerticalScrollBarPolicy
IntelliJ.Lang.Annotations.JdkConstants.IVerticalScrollBarPolicy;org.intellij.lang.annotations.JdkConstants$VerticalScrollBarPolicy
org.intellij.lang.annotations.JdkConstants$VerticalScrollBarPolicy;org.intellij.lang.annotations.JdkConstants$VerticalScrollBarPolicy
Kotlin.IBuilderInference, Xamarin.Kotlin.StdLib;kotlin.BuilderInference
Kotlin.IBuilderInference;kotlin.BuilderInference
kotlin.BuilderInference;kotlin.BuilderInference
Kotlin.IContextFunctionTypeParams, Xamarin.Kotlin.StdLib;kotlin.ContextFunctionTypeParams
Kotlin.IContextFunctionTypeParams;kotlin.ContextFunctionTypeParams
kotlin.ContextFunctionTypeParams;kotlin.ContextFunctionTypeParams
Kotlin.IDeprecated, Xamarin.Kotlin.StdLib;kotlin.Deprecated
Kotlin.IDeprecated;kotlin.Deprecated
kotlin.Deprecated;kotlin.Deprecated
Kotlin.IDeprecatedSinceKotlin, Xamarin.Kotlin.StdLib;kotlin.DeprecatedSinceKotlin
Kotlin.IDeprecatedSinceKotlin;kotlin.DeprecatedSinceKotlin
kotlin.DeprecatedSinceKotlin;kotlin.DeprecatedSinceKotlin
Kotlin.IDslMarker, Xamarin.Kotlin.StdLib;kotlin.DslMarker
Kotlin.IDslMarker;kotlin.DslMarker
kotlin.DslMarker;kotlin.DslMarker
Kotlin.IExperimentalMultiplatform, Xamarin.Kotlin.StdLib;kotlin.ExperimentalMultiplatform
Kotlin.IExperimentalMultiplatform;kotlin.ExperimentalMultiplatform
kotlin.ExperimentalMultiplatform;kotlin.ExperimentalMultiplatform
Kotlin.IExperimentalStdlibApi, Xamarin.Kotlin.StdLib;kotlin.ExperimentalStdlibApi
Kotlin.IExperimentalStdlibApi;kotlin.ExperimentalStdlibApi
kotlin.ExperimentalStdlibApi;kotlin.ExperimentalStdlibApi
Kotlin.IExperimentalSubclassOptIn, Xamarin.Kotlin.StdLib;kotlin.ExperimentalSubclassOptIn
Kotlin.IExperimentalSubclassOptIn;kotlin.ExperimentalSubclassOptIn
kotlin.ExperimentalSubclassOptIn;kotlin.ExperimentalSubclassOptIn
Kotlin.IExperimentalUnsignedTypes, Xamarin.Kotlin.StdLib;kotlin.ExperimentalUnsignedTypes
Kotlin.IExperimentalUnsignedTypes;kotlin.ExperimentalUnsignedTypes
kotlin.ExperimentalUnsignedTypes;kotlin.ExperimentalUnsignedTypes
Kotlin.IExtensionFunctionType, Xamarin.Kotlin.StdLib;kotlin.ExtensionFunctionType
Kotlin.IExtensionFunctionType;kotlin.ExtensionFunctionType
kotlin.ExtensionFunctionType;kotlin.ExtensionFunctionType
Kotlin.IFunction, Xamarin.Kotlin.StdLib;kotlin.Function
Kotlin.IFunction;kotlin.Function
kotlin.Function;kotlin.Function
Kotlin.ILazy, Xamarin.Kotlin.StdLib;kotlin.Lazy
Kotlin.ILazy;kotlin.Lazy
kotlin.Lazy;kotlin.Lazy
Kotlin.IMetadata, Xamarin.Kotlin.StdLib;kotlin.Metadata
Kotlin.IMetadata;kotlin.Metadata
kotlin.Metadata;kotlin.Metadata
Kotlin.IOptIn, Xamarin.Kotlin.StdLib;kotlin.OptIn
Kotlin.IOptIn;kotlin.OptIn
kotlin.OptIn;kotlin.OptIn
Kotlin.IOptionalExpectation, Xamarin.Kotlin.StdLib;kotlin.OptionalExpectation
Kotlin.IOptionalExpectation;kotlin.OptionalExpectation
kotlin.OptionalExpectation;kotlin.OptionalExpectation
Kotlin.IOverloadResolutionByLambdaReturnType, Xamarin.Kotlin.StdLib;kotlin.OverloadResolutionByLambdaReturnType
Kotlin.IOverloadResolutionByLambdaReturnType;kotlin.OverloadResolutionByLambdaReturnType
kotlin.OverloadResolutionByLambdaReturnType;kotlin.OverloadResolutionByLambdaReturnType
Kotlin.IParameterName, Xamarin.Kotlin.StdLib;kotlin.ParameterName
Kotlin.IParameterName;kotlin.ParameterName
kotlin.ParameterName;kotlin.ParameterName
Kotlin.IPublishedApi, Xamarin.Kotlin.StdLib;kotlin.PublishedApi
Kotlin.IPublishedApi;kotlin.PublishedApi
kotlin.PublishedApi;kotlin.PublishedApi
Kotlin.IReplaceWith, Xamarin.Kotlin.StdLib;kotlin.ReplaceWith
Kotlin.IReplaceWith;kotlin.ReplaceWith
kotlin.ReplaceWith;kotlin.ReplaceWith
Kotlin.IRequiresOptIn, Xamarin.Kotlin.StdLib;kotlin.RequiresOptIn
Kotlin.IRequiresOptIn;kotlin.RequiresOptIn
kotlin.RequiresOptIn;kotlin.RequiresOptIn
Kotlin.ISinceKotlin, Xamarin.Kotlin.StdLib;kotlin.SinceKotlin
Kotlin.ISinceKotlin;kotlin.SinceKotlin
kotlin.SinceKotlin;kotlin.SinceKotlin
Kotlin.ISubclassOptInRequired, Xamarin.Kotlin.StdLib;kotlin.SubclassOptInRequired
Kotlin.ISubclassOptInRequired;kotlin.SubclassOptInRequired
kotlin.SubclassOptInRequired;kotlin.SubclassOptInRequired
Kotlin.ISuppress, Xamarin.Kotlin.StdLib;kotlin.Suppress
Kotlin.ISuppress;kotlin.Suppress
kotlin.Suppress;kotlin.Suppress
Kotlin.IUnsafeVariance, Xamarin.Kotlin.StdLib;kotlin.UnsafeVariance
Kotlin.IUnsafeVariance;kotlin.UnsafeVariance
kotlin.UnsafeVariance;kotlin.UnsafeVariance
Kotlin.Time.IComparableTimeMark, Xamarin.Kotlin.StdLib;kotlin.time.ComparableTimeMark
Kotlin.Time.IComparableTimeMark;kotlin.time.ComparableTimeMark
kotlin.time.ComparableTimeMark;kotlin.time.ComparableTimeMark
Kotlin.Time.IExperimentalTime, Xamarin.Kotlin.StdLib;kotlin.time.ExperimentalTime
Kotlin.Time.IExperimentalTime;kotlin.time.ExperimentalTime
kotlin.time.ExperimentalTime;kotlin.time.ExperimentalTime
Kotlin.Time.ITimeMark, Xamarin.Kotlin.StdLib;kotlin.time.TimeMark
Kotlin.Time.ITimeMark;kotlin.time.TimeMark
kotlin.time.TimeMark;kotlin.time.TimeMark
Kotlin.Time.ITimeSourceWithComparableMarks, Xamarin.Kotlin.StdLib;kotlin.time.TimeSource$WithComparableMarks
Kotlin.Time.ITimeSourceWithComparableMarks;kotlin.time.TimeSource$WithComparableMarks
kotlin.time.TimeSource$WithComparableMarks;kotlin.time.TimeSource$WithComparableMarks
Kotlin.Time.ITimeSource, Xamarin.Kotlin.StdLib;kotlin.time.TimeSource
Kotlin.Time.ITimeSource;kotlin.time.TimeSource
kotlin.time.TimeSource;kotlin.time.TimeSource
Kotlin.Text.IMatchGroupCollection, Xamarin.Kotlin.StdLib;kotlin.text.MatchGroupCollection
Kotlin.Text.IMatchGroupCollection;kotlin.text.MatchGroupCollection
kotlin.text.MatchGroupCollection;kotlin.text.MatchGroupCollection
Kotlin.Text.IMatchNamedGroupCollection, Xamarin.Kotlin.StdLib;kotlin.text.MatchNamedGroupCollection
Kotlin.Text.IMatchNamedGroupCollection;kotlin.text.MatchNamedGroupCollection
kotlin.text.MatchNamedGroupCollection;kotlin.text.MatchNamedGroupCollection
Kotlin.Text.IMatchResult, Xamarin.Kotlin.StdLib;kotlin.text.MatchResult
Kotlin.Text.IMatchResult;kotlin.text.MatchResult
kotlin.text.MatchResult;kotlin.text.MatchResult
Kotlin.Sequences.ISequence, Xamarin.Kotlin.StdLib;kotlin.sequences.Sequence
Kotlin.Sequences.ISequence;kotlin.sequences.Sequence
kotlin.sequences.Sequence;kotlin.sequences.Sequence
Kotlin.Reflect.IKAnnotatedElement, Xamarin.Kotlin.StdLib;kotlin.reflect.KAnnotatedElement
Kotlin.Reflect.IKAnnotatedElement;kotlin.reflect.KAnnotatedElement
kotlin.reflect.KAnnotatedElement;kotlin.reflect.KAnnotatedElement
Kotlin.Reflect.IKCallable, Xamarin.Kotlin.StdLib;kotlin.reflect.KCallable
Kotlin.Reflect.IKCallable;kotlin.reflect.KCallable
kotlin.reflect.KCallable;kotlin.reflect.KCallable
Kotlin.Reflect.IKClass, Xamarin.Kotlin.StdLib;kotlin.reflect.KClass
Kotlin.Reflect.IKClass;kotlin.reflect.KClass
kotlin.reflect.KClass;kotlin.reflect.KClass
Kotlin.Reflect.IKClassifier, Xamarin.Kotlin.StdLib;kotlin.reflect.KClassifier
Kotlin.Reflect.IKClassifier;kotlin.reflect.KClassifier
kotlin.reflect.KClassifier;kotlin.reflect.KClassifier
Kotlin.Reflect.IKDeclarationContainer, Xamarin.Kotlin.StdLib;kotlin.reflect.KDeclarationContainer
Kotlin.Reflect.IKDeclarationContainer;kotlin.reflect.KDeclarationContainer
kotlin.reflect.KDeclarationContainer;kotlin.reflect.KDeclarationContainer
Kotlin.Reflect.IKFunction, Xamarin.Kotlin.StdLib;kotlin.reflect.KFunction
Kotlin.Reflect.IKFunction;kotlin.reflect.KFunction
kotlin.reflect.KFunction;kotlin.reflect.KFunction
Kotlin.Reflect.IKMutablePropertySetter, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty$Setter
Kotlin.Reflect.IKMutablePropertySetter;kotlin.reflect.KMutableProperty$Setter
kotlin.reflect.KMutableProperty$Setter;kotlin.reflect.KMutableProperty$Setter
Kotlin.Reflect.IKMutableProperty, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty
Kotlin.Reflect.IKMutableProperty;kotlin.reflect.KMutableProperty
kotlin.reflect.KMutableProperty;kotlin.reflect.KMutableProperty
Kotlin.Reflect.IKMutableProperty0Setter, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty0$Setter
Kotlin.Reflect.IKMutableProperty0Setter;kotlin.reflect.KMutableProperty0$Setter
kotlin.reflect.KMutableProperty0$Setter;kotlin.reflect.KMutableProperty0$Setter
Kotlin.Reflect.IKMutableProperty0, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty0
Kotlin.Reflect.IKMutableProperty0;kotlin.reflect.KMutableProperty0
kotlin.reflect.KMutableProperty0;kotlin.reflect.KMutableProperty0
Kotlin.Reflect.IKMutableProperty1Setter, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty1$Setter
Kotlin.Reflect.IKMutableProperty1Setter;kotlin.reflect.KMutableProperty1$Setter
kotlin.reflect.KMutableProperty1$Setter;kotlin.reflect.KMutableProperty1$Setter
Kotlin.Reflect.IKMutableProperty1, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty1
Kotlin.Reflect.IKMutableProperty1;kotlin.reflect.KMutableProperty1
kotlin.reflect.KMutableProperty1;kotlin.reflect.KMutableProperty1
Kotlin.Reflect.IKMutableProperty2Setter, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty2$Setter
Kotlin.Reflect.IKMutableProperty2Setter;kotlin.reflect.KMutableProperty2$Setter
kotlin.reflect.KMutableProperty2$Setter;kotlin.reflect.KMutableProperty2$Setter
Kotlin.Reflect.IKMutableProperty2, Xamarin.Kotlin.StdLib;kotlin.reflect.KMutableProperty2
Kotlin.Reflect.IKMutableProperty2;kotlin.reflect.KMutableProperty2
kotlin.reflect.KMutableProperty2;kotlin.reflect.KMutableProperty2
Kotlin.Reflect.IKParameter, Xamarin.Kotlin.StdLib;kotlin.reflect.KParameter
Kotlin.Reflect.IKParameter;kotlin.reflect.KParameter
kotlin.reflect.KParameter;kotlin.reflect.KParameter
Kotlin.Reflect.IKPropertyAccessor, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty$Accessor
Kotlin.Reflect.IKPropertyAccessor;kotlin.reflect.KProperty$Accessor
kotlin.reflect.KProperty$Accessor;kotlin.reflect.KProperty$Accessor
Kotlin.Reflect.IKPropertyGetter, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty$Getter
Kotlin.Reflect.IKPropertyGetter;kotlin.reflect.KProperty$Getter
kotlin.reflect.KProperty$Getter;kotlin.reflect.KProperty$Getter
Kotlin.Reflect.IKProperty, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty
Kotlin.Reflect.IKProperty;kotlin.reflect.KProperty
kotlin.reflect.KProperty;kotlin.reflect.KProperty
Kotlin.Reflect.IKProperty0Getter, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty0$Getter
Kotlin.Reflect.IKProperty0Getter;kotlin.reflect.KProperty0$Getter
kotlin.reflect.KProperty0$Getter;kotlin.reflect.KProperty0$Getter
Kotlin.Reflect.IKProperty0, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty0
Kotlin.Reflect.IKProperty0;kotlin.reflect.KProperty0
kotlin.reflect.KProperty0;kotlin.reflect.KProperty0
Kotlin.Reflect.IKProperty1Getter, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty1$Getter
Kotlin.Reflect.IKProperty1Getter;kotlin.reflect.KProperty1$Getter
kotlin.reflect.KProperty1$Getter;kotlin.reflect.KProperty1$Getter
Kotlin.Reflect.IKProperty1, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty1
Kotlin.Reflect.IKProperty1;kotlin.reflect.KProperty1
kotlin.reflect.KProperty1;kotlin.reflect.KProperty1
Kotlin.Reflect.IKProperty2Getter, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty2$Getter
Kotlin.Reflect.IKProperty2Getter;kotlin.reflect.KProperty2$Getter
kotlin.reflect.KProperty2$Getter;kotlin.reflect.KProperty2$Getter
Kotlin.Reflect.IKProperty2, Xamarin.Kotlin.StdLib;kotlin.reflect.KProperty2
Kotlin.Reflect.IKProperty2;kotlin.reflect.KProperty2
kotlin.reflect.KProperty2;kotlin.reflect.KProperty2
Kotlin.Reflect.IKType, Xamarin.Kotlin.StdLib;kotlin.reflect.KType
Kotlin.Reflect.IKType;kotlin.reflect.KType
kotlin.reflect.KType;kotlin.reflect.KType
Kotlin.Reflect.IKTypeParameter, Xamarin.Kotlin.StdLib;kotlin.reflect.KTypeParameter
Kotlin.Reflect.IKTypeParameter;kotlin.reflect.KTypeParameter
kotlin.reflect.KTypeParameter;kotlin.reflect.KTypeParameter
Kotlin.Properties.IPropertyDelegateProvider, Xamarin.Kotlin.StdLib;kotlin.properties.PropertyDelegateProvider
Kotlin.Properties.IPropertyDelegateProvider;kotlin.properties.PropertyDelegateProvider
kotlin.properties.PropertyDelegateProvider;kotlin.properties.PropertyDelegateProvider
Kotlin.Properties.IReadOnlyProperty, Xamarin.Kotlin.StdLib;kotlin.properties.ReadOnlyProperty
Kotlin.Properties.IReadOnlyProperty;kotlin.properties.ReadOnlyProperty
kotlin.properties.ReadOnlyProperty;kotlin.properties.ReadOnlyProperty
Kotlin.Properties.IReadWriteProperty, Xamarin.Kotlin.StdLib;kotlin.properties.ReadWriteProperty
Kotlin.Properties.IReadWriteProperty;kotlin.properties.ReadWriteProperty
kotlin.properties.ReadWriteProperty;kotlin.properties.ReadWriteProperty
Kotlin.Jvm.IImplicitlyActualizedByJvmDeclaration, Xamarin.Kotlin.StdLib;kotlin.jvm.ImplicitlyActualizedByJvmDeclaration
Kotlin.Jvm.IImplicitlyActualizedByJvmDeclaration;kotlin.jvm.ImplicitlyActualizedByJvmDeclaration
kotlin.jvm.ImplicitlyActualizedByJvmDeclaration;kotlin.jvm.ImplicitlyActualizedByJvmDeclaration
Kotlin.Jvm.IJvmDefault, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmDefault
Kotlin.Jvm.IJvmDefault;kotlin.jvm.JvmDefault
kotlin.jvm.JvmDefault;kotlin.jvm.JvmDefault
Kotlin.Jvm.IJvmDefaultWithCompatibility, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmDefaultWithCompatibility
Kotlin.Jvm.IJvmDefaultWithCompatibility;kotlin.jvm.JvmDefaultWithCompatibility
kotlin.jvm.JvmDefaultWithCompatibility;kotlin.jvm.JvmDefaultWithCompatibility
Kotlin.Jvm.IJvmDefaultWithoutCompatibility, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmDefaultWithoutCompatibility
Kotlin.Jvm.IJvmDefaultWithoutCompatibility;kotlin.jvm.JvmDefaultWithoutCompatibility
kotlin.jvm.JvmDefaultWithoutCompatibility;kotlin.jvm.JvmDefaultWithoutCompatibility
Kotlin.Jvm.IJvmField, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmField
Kotlin.Jvm.IJvmField;kotlin.jvm.JvmField
kotlin.jvm.JvmField;kotlin.jvm.JvmField
Kotlin.Jvm.IJvmInline, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmInline
Kotlin.Jvm.IJvmInline;kotlin.jvm.JvmInline
kotlin.jvm.JvmInline;kotlin.jvm.JvmInline
Kotlin.Jvm.IJvmMultifileClass, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmMultifileClass
Kotlin.Jvm.IJvmMultifileClass;kotlin.jvm.JvmMultifileClass
kotlin.jvm.JvmMultifileClass;kotlin.jvm.JvmMultifileClass
Kotlin.Jvm.IJvmName, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmName
Kotlin.Jvm.IJvmName;kotlin.jvm.JvmName
kotlin.jvm.JvmName;kotlin.jvm.JvmName
Kotlin.Jvm.IJvmOverloads, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmOverloads
Kotlin.Jvm.IJvmOverloads;kotlin.jvm.JvmOverloads
kotlin.jvm.JvmOverloads;kotlin.jvm.JvmOverloads
Kotlin.Jvm.IJvmRecord, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmRecord
Kotlin.Jvm.IJvmRecord;kotlin.jvm.JvmRecord
kotlin.jvm.JvmRecord;kotlin.jvm.JvmRecord
Kotlin.Jvm.IJvmSerializableLambda, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmSerializableLambda
Kotlin.Jvm.IJvmSerializableLambda;kotlin.jvm.JvmSerializableLambda
kotlin.jvm.JvmSerializableLambda;kotlin.jvm.JvmSerializableLambda
Kotlin.Jvm.IJvmStatic, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmStatic
Kotlin.Jvm.IJvmStatic;kotlin.jvm.JvmStatic
kotlin.jvm.JvmStatic;kotlin.jvm.JvmStatic
Kotlin.Jvm.IJvmSuppressWildcards, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmSuppressWildcards
Kotlin.Jvm.IJvmSuppressWildcards;kotlin.jvm.JvmSuppressWildcards
kotlin.jvm.JvmSuppressWildcards;kotlin.jvm.JvmSuppressWildcards
Kotlin.Jvm.IJvmSynthetic, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmSynthetic
Kotlin.Jvm.IJvmSynthetic;kotlin.jvm.JvmSynthetic
kotlin.jvm.JvmSynthetic;kotlin.jvm.JvmSynthetic
Kotlin.Jvm.IJvmWildcard, Xamarin.Kotlin.StdLib;kotlin.jvm.JvmWildcard
Kotlin.Jvm.IJvmWildcard;kotlin.jvm.JvmWildcard
kotlin.jvm.JvmWildcard;kotlin.jvm.JvmWildcard
Kotlin.Jvm.IPurelyImplements, Xamarin.Kotlin.StdLib;kotlin.jvm.PurelyImplements
Kotlin.Jvm.IPurelyImplements;kotlin.jvm.PurelyImplements
kotlin.jvm.PurelyImplements;kotlin.jvm.PurelyImplements
Kotlin.Jvm.IStrictfp, Xamarin.Kotlin.StdLib;kotlin.jvm.Strictfp
Kotlin.Jvm.IStrictfp;kotlin.jvm.Strictfp
kotlin.jvm.Strictfp;kotlin.jvm.Strictfp
Kotlin.Jvm.ISynchronized, Xamarin.Kotlin.StdLib;kotlin.jvm.Synchronized
Kotlin.Jvm.ISynchronized;kotlin.jvm.Synchronized
kotlin.jvm.Synchronized;kotlin.jvm.Synchronized
Kotlin.Jvm.IThrows, Xamarin.Kotlin.StdLib;kotlin.jvm.Throws
Kotlin.Jvm.IThrows;kotlin.jvm.Throws
kotlin.jvm.Throws;kotlin.jvm.Throws
Kotlin.Jvm.ITransient, Xamarin.Kotlin.StdLib;kotlin.jvm.Transient
Kotlin.Jvm.ITransient;kotlin.jvm.Transient
kotlin.jvm.Transient;kotlin.jvm.Transient
Kotlin.Jvm.IVolatile, Xamarin.Kotlin.StdLib;kotlin.jvm.Volatile
Kotlin.Jvm.IVolatile;kotlin.jvm.Volatile
kotlin.jvm.Volatile;kotlin.jvm.Volatile
Kotlin.Jvm.Internal.IClassBasedDeclarationContainer, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.ClassBasedDeclarationContainer
Kotlin.Jvm.Internal.IClassBasedDeclarationContainer;kotlin.jvm.internal.ClassBasedDeclarationContainer
kotlin.jvm.internal.ClassBasedDeclarationContainer;kotlin.jvm.internal.ClassBasedDeclarationContainer
Kotlin.Jvm.Internal.IFunctionAdapter, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.FunctionAdapter
Kotlin.Jvm.Internal.IFunctionAdapter;kotlin.jvm.internal.FunctionAdapter
kotlin.jvm.internal.FunctionAdapter;kotlin.jvm.internal.FunctionAdapter
Kotlin.Jvm.Internal.IFunctionBase, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.FunctionBase
Kotlin.Jvm.Internal.IFunctionBase;kotlin.jvm.internal.FunctionBase
kotlin.jvm.internal.FunctionBase;kotlin.jvm.internal.FunctionBase
Kotlin.Jvm.Internal.IKTypeBase, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.KTypeBase
Kotlin.Jvm.Internal.IKTypeBase;kotlin.jvm.internal.KTypeBase
kotlin.jvm.internal.KTypeBase;kotlin.jvm.internal.KTypeBase
Kotlin.Jvm.Internal.IRepeatableContainer, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.RepeatableContainer
Kotlin.Jvm.Internal.IRepeatableContainer;kotlin.jvm.internal.RepeatableContainer
kotlin.jvm.internal.RepeatableContainer;kotlin.jvm.internal.RepeatableContainer
Kotlin.Jvm.Internal.ISerializedIr, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.SerializedIr
Kotlin.Jvm.Internal.ISerializedIr;kotlin.jvm.internal.SerializedIr
kotlin.jvm.internal.SerializedIr;kotlin.jvm.internal.SerializedIr
Kotlin.Jvm.Internal.ISourceDebugExtension, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.SourceDebugExtension
Kotlin.Jvm.Internal.ISourceDebugExtension;kotlin.jvm.internal.SourceDebugExtension
kotlin.jvm.internal.SourceDebugExtension;kotlin.jvm.internal.SourceDebugExtension
Kotlin.Jvm.Internal.Markers.IKMappedMarker, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMappedMarker
Kotlin.Jvm.Internal.Markers.IKMappedMarker;kotlin.jvm.internal.markers.KMappedMarker
kotlin.jvm.internal.markers.KMappedMarker;kotlin.jvm.internal.markers.KMappedMarker
Kotlin.Jvm.Internal.Markers.IKMutableCollection, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableCollection
Kotlin.Jvm.Internal.Markers.IKMutableCollection;kotlin.jvm.internal.markers.KMutableCollection
kotlin.jvm.internal.markers.KMutableCollection;kotlin.jvm.internal.markers.KMutableCollection
Kotlin.Jvm.Internal.Markers.IKMutableIterable, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableIterable
Kotlin.Jvm.Internal.Markers.IKMutableIterable;kotlin.jvm.internal.markers.KMutableIterable
kotlin.jvm.internal.markers.KMutableIterable;kotlin.jvm.internal.markers.KMutableIterable
Kotlin.Jvm.Internal.Markers.IKMutableIterator, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableIterator
Kotlin.Jvm.Internal.Markers.IKMutableIterator;kotlin.jvm.internal.markers.KMutableIterator
kotlin.jvm.internal.markers.KMutableIterator;kotlin.jvm.internal.markers.KMutableIterator
Kotlin.Jvm.Internal.Markers.IKMutableList, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableList
Kotlin.Jvm.Internal.Markers.IKMutableList;kotlin.jvm.internal.markers.KMutableList
kotlin.jvm.internal.markers.KMutableList;kotlin.jvm.internal.markers.KMutableList
Kotlin.Jvm.Internal.Markers.IKMutableListIterator, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableListIterator
Kotlin.Jvm.Internal.Markers.IKMutableListIterator;kotlin.jvm.internal.markers.KMutableListIterator
kotlin.jvm.internal.markers.KMutableListIterator;kotlin.jvm.internal.markers.KMutableListIterator
Kotlin.Jvm.Internal.Markers.IKMutableMapEntry, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableMap$Entry
Kotlin.Jvm.Internal.Markers.IKMutableMapEntry;kotlin.jvm.internal.markers.KMutableMap$Entry
kotlin.jvm.internal.markers.KMutableMap$Entry;kotlin.jvm.internal.markers.KMutableMap$Entry
Kotlin.Jvm.Internal.Markers.IKMutableMap, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableMap
Kotlin.Jvm.Internal.Markers.IKMutableMap;kotlin.jvm.internal.markers.KMutableMap
kotlin.jvm.internal.markers.KMutableMap;kotlin.jvm.internal.markers.KMutableMap
Kotlin.Jvm.Internal.Markers.IKMutableSet, Xamarin.Kotlin.StdLib;kotlin.jvm.internal.markers.KMutableSet
Kotlin.Jvm.Internal.Markers.IKMutableSet;kotlin.jvm.internal.markers.KMutableSet
kotlin.jvm.internal.markers.KMutableSet;kotlin.jvm.internal.markers.KMutableSet
Kotlin.Jvm.Functions.IFunction0, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function0
Kotlin.Jvm.Functions.IFunction0;kotlin.jvm.functions.Function0
kotlin.jvm.functions.Function0;kotlin.jvm.functions.Function0
Kotlin.Jvm.Functions.IFunction1, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function1
Kotlin.Jvm.Functions.IFunction1;kotlin.jvm.functions.Function1
kotlin.jvm.functions.Function1;kotlin.jvm.functions.Function1
Kotlin.Jvm.Functions.IFunction10, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function10
Kotlin.Jvm.Functions.IFunction10;kotlin.jvm.functions.Function10
kotlin.jvm.functions.Function10;kotlin.jvm.functions.Function10
Kotlin.Jvm.Functions.IFunction11, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function11
Kotlin.Jvm.Functions.IFunction11;kotlin.jvm.functions.Function11
kotlin.jvm.functions.Function11;kotlin.jvm.functions.Function11
Kotlin.Jvm.Functions.IFunction12, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function12
Kotlin.Jvm.Functions.IFunction12;kotlin.jvm.functions.Function12
kotlin.jvm.functions.Function12;kotlin.jvm.functions.Function12
Kotlin.Jvm.Functions.IFunction13, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function13
Kotlin.Jvm.Functions.IFunction13;kotlin.jvm.functions.Function13
kotlin.jvm.functions.Function13;kotlin.jvm.functions.Function13
Kotlin.Jvm.Functions.IFunction14, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function14
Kotlin.Jvm.Functions.IFunction14;kotlin.jvm.functions.Function14
kotlin.jvm.functions.Function14;kotlin.jvm.functions.Function14
Kotlin.Jvm.Functions.IFunction15, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function15
Kotlin.Jvm.Functions.IFunction15;kotlin.jvm.functions.Function15
kotlin.jvm.functions.Function15;kotlin.jvm.functions.Function15
Kotlin.Jvm.Functions.IFunction16, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function16
Kotlin.Jvm.Functions.IFunction16;kotlin.jvm.functions.Function16
kotlin.jvm.functions.Function16;kotlin.jvm.functions.Function16
Kotlin.Jvm.Functions.IFunction17, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function17
Kotlin.Jvm.Functions.IFunction17;kotlin.jvm.functions.Function17
kotlin.jvm.functions.Function17;kotlin.jvm.functions.Function17
Kotlin.Jvm.Functions.IFunction18, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function18
Kotlin.Jvm.Functions.IFunction18;kotlin.jvm.functions.Function18
kotlin.jvm.functions.Function18;kotlin.jvm.functions.Function18
Kotlin.Jvm.Functions.IFunction19, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function19
Kotlin.Jvm.Functions.IFunction19;kotlin.jvm.functions.Function19
kotlin.jvm.functions.Function19;kotlin.jvm.functions.Function19
Kotlin.Jvm.Functions.IFunction2, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function2
Kotlin.Jvm.Functions.IFunction2;kotlin.jvm.functions.Function2
kotlin.jvm.functions.Function2;kotlin.jvm.functions.Function2
Kotlin.Jvm.Functions.IFunction20, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function20
Kotlin.Jvm.Functions.IFunction20;kotlin.jvm.functions.Function20
kotlin.jvm.functions.Function20;kotlin.jvm.functions.Function20
Kotlin.Jvm.Functions.IFunction21, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function21
Kotlin.Jvm.Functions.IFunction21;kotlin.jvm.functions.Function21
kotlin.jvm.functions.Function21;kotlin.jvm.functions.Function21
Kotlin.Jvm.Functions.IFunction22, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function22
Kotlin.Jvm.Functions.IFunction22;kotlin.jvm.functions.Function22
kotlin.jvm.functions.Function22;kotlin.jvm.functions.Function22
Kotlin.Jvm.Functions.IFunction3, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function3
Kotlin.Jvm.Functions.IFunction3;kotlin.jvm.functions.Function3
kotlin.jvm.functions.Function3;kotlin.jvm.functions.Function3
Kotlin.Jvm.Functions.IFunction4, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function4
Kotlin.Jvm.Functions.IFunction4;kotlin.jvm.functions.Function4
kotlin.jvm.functions.Function4;kotlin.jvm.functions.Function4
Kotlin.Jvm.Functions.IFunction5, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function5
Kotlin.Jvm.Functions.IFunction5;kotlin.jvm.functions.Function5
kotlin.jvm.functions.Function5;kotlin.jvm.functions.Function5
Kotlin.Jvm.Functions.IFunction6, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function6
Kotlin.Jvm.Functions.IFunction6;kotlin.jvm.functions.Function6
kotlin.jvm.functions.Function6;kotlin.jvm.functions.Function6
Kotlin.Jvm.Functions.IFunction7, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function7
Kotlin.Jvm.Functions.IFunction7;kotlin.jvm.functions.Function7
kotlin.jvm.functions.Function7;kotlin.jvm.functions.Function7
Kotlin.Jvm.Functions.IFunction8, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function8
Kotlin.Jvm.Functions.IFunction8;kotlin.jvm.functions.Function8
kotlin.jvm.functions.Function8;kotlin.jvm.functions.Function8
Kotlin.Jvm.Functions.IFunction9, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.Function9
Kotlin.Jvm.Functions.IFunction9;kotlin.jvm.functions.Function9
kotlin.jvm.functions.Function9;kotlin.jvm.functions.Function9
Kotlin.Jvm.Functions.IFunctionN, Xamarin.Kotlin.StdLib;kotlin.jvm.functions.FunctionN
Kotlin.Jvm.Functions.IFunctionN;kotlin.jvm.functions.FunctionN
kotlin.jvm.functions.FunctionN;kotlin.jvm.functions.FunctionN
Kotlin.JS.IExperimentalJsCollectionsApi, Xamarin.Kotlin.StdLib;kotlin.js.ExperimentalJsCollectionsApi
Kotlin.JS.IExperimentalJsCollectionsApi;kotlin.js.ExperimentalJsCollectionsApi
kotlin.js.ExperimentalJsCollectionsApi;kotlin.js.ExperimentalJsCollectionsApi
Kotlin.JS.IExperimentalJsExport, Xamarin.Kotlin.StdLib;kotlin.js.ExperimentalJsExport
Kotlin.JS.IExperimentalJsExport;kotlin.js.ExperimentalJsExport
kotlin.js.ExperimentalJsExport;kotlin.js.ExperimentalJsExport
Kotlin.JS.IExperimentalJsFileName, Xamarin.Kotlin.StdLib;kotlin.js.ExperimentalJsFileName
Kotlin.JS.IExperimentalJsFileName;kotlin.js.ExperimentalJsFileName
kotlin.js.ExperimentalJsFileName;kotlin.js.ExperimentalJsFileName
Kotlin.JS.IExperimentalJsReflectionCreateInstance, Xamarin.Kotlin.StdLib;kotlin.js.ExperimentalJsReflectionCreateInstance
Kotlin.JS.IExperimentalJsReflectionCreateInstance;kotlin.js.ExperimentalJsReflectionCreateInstance
kotlin.js.ExperimentalJsReflectionCreateInstance;kotlin.js.ExperimentalJsReflectionCreateInstance
Kotlin.IO.Path.ICopyActionContext, Xamarin.Kotlin.StdLib;kotlin.io.path.CopyActionContext
Kotlin.IO.Path.ICopyActionContext;kotlin.io.path.CopyActionContext
kotlin.io.path.CopyActionContext;kotlin.io.path.CopyActionContext
Kotlin.IO.Path.IExperimentalPathApi, Xamarin.Kotlin.StdLib;kotlin.io.path.ExperimentalPathApi
Kotlin.IO.Path.IExperimentalPathApi;kotlin.io.path.ExperimentalPathApi
kotlin.io.path.ExperimentalPathApi;kotlin.io.path.ExperimentalPathApi
Kotlin.IO.Path.IFileVisitorBuilder, Xamarin.Kotlin.StdLib;kotlin.io.path.FileVisitorBuilder
Kotlin.IO.Path.IFileVisitorBuilder;kotlin.io.path.FileVisitorBuilder
kotlin.io.path.FileVisitorBuilder;kotlin.io.path.FileVisitorBuilder
Kotlin.IO.Encoding.IExperimentalEncodingApi, Xamarin.Kotlin.StdLib;kotlin.io.encoding.ExperimentalEncodingApi
Kotlin.IO.Encoding.IExperimentalEncodingApi;kotlin.io.encoding.ExperimentalEncodingApi
kotlin.io.encoding.ExperimentalEncodingApi;kotlin.io.encoding.ExperimentalEncodingApi
Kotlin.Experimental.IExperimentalNativeApi, Xamarin.Kotlin.StdLib;kotlin.experimental.ExperimentalNativeApi
Kotlin.Experimental.IExperimentalNativeApi;kotlin.experimental.ExperimentalNativeApi
kotlin.experimental.ExperimentalNativeApi;kotlin.experimental.ExperimentalNativeApi
Kotlin.Experimental.IExperimentalObjCName, Xamarin.Kotlin.StdLib;kotlin.experimental.ExperimentalObjCName
Kotlin.Experimental.IExperimentalObjCName;kotlin.experimental.ExperimentalObjCName
kotlin.experimental.ExperimentalObjCName;kotlin.experimental.ExperimentalObjCName
Kotlin.Experimental.IExperimentalObjCRefinement, Xamarin.Kotlin.StdLib;kotlin.experimental.ExperimentalObjCRefinement
Kotlin.Experimental.IExperimentalObjCRefinement;kotlin.experimental.ExperimentalObjCRefinement
kotlin.experimental.ExperimentalObjCRefinement;kotlin.experimental.ExperimentalObjCRefinement
Kotlin.Experimental.IExperimentalTypeInference, Xamarin.Kotlin.StdLib;kotlin.experimental.ExperimentalTypeInference
Kotlin.Experimental.IExperimentalTypeInference;kotlin.experimental.ExperimentalTypeInference
kotlin.experimental.ExperimentalTypeInference;kotlin.experimental.ExperimentalTypeInference
Kotlin.Enums.IEnumEntries, Xamarin.Kotlin.StdLib;kotlin.enums.EnumEntries
Kotlin.Enums.IEnumEntries;kotlin.enums.EnumEntries
kotlin.enums.EnumEntries;kotlin.enums.EnumEntries
Kotlin.Coroutines.IContinuation, Xamarin.Kotlin.StdLib;kotlin.coroutines.Continuation
Kotlin.Coroutines.IContinuation;kotlin.coroutines.Continuation
kotlin.coroutines.Continuation;kotlin.coroutines.Continuation
Kotlin.Coroutines.IContinuationInterceptor, Xamarin.Kotlin.StdLib;kotlin.coroutines.ContinuationInterceptor
Kotlin.Coroutines.IContinuationInterceptor;kotlin.coroutines.ContinuationInterceptor
kotlin.coroutines.ContinuationInterceptor;kotlin.coroutines.ContinuationInterceptor
Kotlin.Coroutines.ICoroutineContextElement, Xamarin.Kotlin.StdLib;kotlin.coroutines.CoroutineContext$Element
Kotlin.Coroutines.ICoroutineContextElement;kotlin.coroutines.CoroutineContext$Element
kotlin.coroutines.CoroutineContext$Element;kotlin.coroutines.CoroutineContext$Element
Kotlin.Coroutines.ICoroutineContextKey, Xamarin.Kotlin.StdLib;kotlin.coroutines.CoroutineContext$Key
Kotlin.Coroutines.ICoroutineContextKey;kotlin.coroutines.CoroutineContext$Key
kotlin.coroutines.CoroutineContext$Key;kotlin.coroutines.CoroutineContext$Key
Kotlin.Coroutines.ICoroutineContext, Xamarin.Kotlin.StdLib;kotlin.coroutines.CoroutineContext
Kotlin.Coroutines.ICoroutineContext;kotlin.coroutines.CoroutineContext
kotlin.coroutines.CoroutineContext;kotlin.coroutines.CoroutineContext
Kotlin.Coroutines.IRestrictsSuspension, Xamarin.Kotlin.StdLib;kotlin.coroutines.RestrictsSuspension
Kotlin.Coroutines.IRestrictsSuspension;kotlin.coroutines.RestrictsSuspension
kotlin.coroutines.RestrictsSuspension;kotlin.coroutines.RestrictsSuspension
Kotlin.Coroutines.Jvm.Internal.ICoroutineStackFrame, Xamarin.Kotlin.StdLib;kotlin.coroutines.jvm.internal.CoroutineStackFrame
Kotlin.Coroutines.Jvm.Internal.ICoroutineStackFrame;kotlin.coroutines.jvm.internal.CoroutineStackFrame
kotlin.coroutines.jvm.internal.CoroutineStackFrame;kotlin.coroutines.jvm.internal.CoroutineStackFrame
Kotlin.Contracts.ICallsInPlace, Xamarin.Kotlin.StdLib;kotlin.contracts.CallsInPlace
Kotlin.Contracts.ICallsInPlace;kotlin.contracts.CallsInPlace
kotlin.contracts.CallsInPlace;kotlin.contracts.CallsInPlace
Kotlin.Contracts.IConditionalEffect, Xamarin.Kotlin.StdLib;kotlin.contracts.ConditionalEffect
Kotlin.Contracts.IConditionalEffect;kotlin.contracts.ConditionalEffect
kotlin.contracts.ConditionalEffect;kotlin.contracts.ConditionalEffect
Kotlin.Contracts.IContractBuilder, Xamarin.Kotlin.StdLib;kotlin.contracts.ContractBuilder
Kotlin.Contracts.IContractBuilder;kotlin.contracts.ContractBuilder
kotlin.contracts.ContractBuilder;kotlin.contracts.ContractBuilder
Kotlin.Contracts.IEffect, Xamarin.Kotlin.StdLib;kotlin.contracts.Effect
Kotlin.Contracts.IEffect;kotlin.contracts.Effect
kotlin.contracts.Effect;kotlin.contracts.Effect
Kotlin.Contracts.IExperimentalContracts, Xamarin.Kotlin.StdLib;kotlin.contracts.ExperimentalContracts
Kotlin.Contracts.IExperimentalContracts;kotlin.contracts.ExperimentalContracts
kotlin.contracts.ExperimentalContracts;kotlin.contracts.ExperimentalContracts
Kotlin.Contracts.IReturns, Xamarin.Kotlin.StdLib;kotlin.contracts.Returns
Kotlin.Contracts.IReturns;kotlin.contracts.Returns
kotlin.contracts.Returns;kotlin.contracts.Returns
Kotlin.Contracts.IReturnsNotNull, Xamarin.Kotlin.StdLib;kotlin.contracts.ReturnsNotNull
Kotlin.Contracts.IReturnsNotNull;kotlin.contracts.ReturnsNotNull
kotlin.contracts.ReturnsNotNull;kotlin.contracts.ReturnsNotNull
Kotlin.Contracts.ISimpleEffect, Xamarin.Kotlin.StdLib;kotlin.contracts.SimpleEffect
Kotlin.Contracts.ISimpleEffect;kotlin.contracts.SimpleEffect
kotlin.contracts.SimpleEffect;kotlin.contracts.SimpleEffect
Kotlin.Annotation.IMustBeDocumented, Xamarin.Kotlin.StdLib;kotlin.annotation.MustBeDocumented
Kotlin.Annotation.IMustBeDocumented;kotlin.annotation.MustBeDocumented
kotlin.annotation.MustBeDocumented;kotlin.annotation.MustBeDocumented
Kotlin.Annotation.IRepeatable, Xamarin.Kotlin.StdLib;kotlin.annotation.Repeatable
Kotlin.Annotation.IRepeatable;kotlin.annotation.Repeatable
kotlin.annotation.Repeatable;kotlin.annotation.Repeatable
Kotlin.Annotation.IRetention, Xamarin.Kotlin.StdLib;kotlin.annotation.Retention
Kotlin.Annotation.IRetention;kotlin.annotation.Retention
kotlin.annotation.Retention;kotlin.annotation.Retention
Kotlin.Annotation.ITarget, Xamarin.Kotlin.StdLib;kotlin.annotation.Target
Kotlin.Annotation.ITarget;kotlin.annotation.Target
kotlin.annotation.Target;kotlin.annotation.Target
Kotlin.Ranges.IClosedFloatingPointRange, Xamarin.Kotlin.StdLib;kotlin.ranges.ClosedFloatingPointRange
Kotlin.Ranges.IClosedFloatingPointRange;kotlin.ranges.ClosedFloatingPointRange
kotlin.ranges.ClosedFloatingPointRange;kotlin.ranges.ClosedFloatingPointRange
Kotlin.Ranges.IClosedRange, Xamarin.Kotlin.StdLib;kotlin.ranges.ClosedRange
Kotlin.Ranges.IClosedRange;kotlin.ranges.ClosedRange
kotlin.ranges.ClosedRange;kotlin.ranges.ClosedRange
Kotlin.Ranges.IOpenEndRange, Xamarin.Kotlin.StdLib;kotlin.ranges.OpenEndRange
Kotlin.Ranges.IOpenEndRange;kotlin.ranges.OpenEndRange
kotlin.ranges.OpenEndRange;kotlin.ranges.OpenEndRange
Kotlin.Collections.IGrouping, Xamarin.Kotlin.StdLib;kotlin.collections.Grouping
Kotlin.Collections.IGrouping;kotlin.collections.Grouping
kotlin.collections.Grouping;kotlin.collections.Grouping
Xamarin.KotlinX.Coroutines.ICancellableContinuation, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CancellableContinuation
Xamarin.KotlinX.Coroutines.ICancellableContinuation;kotlinx.coroutines.CancellableContinuation
kotlinx.coroutines.CancellableContinuation;kotlinx.coroutines.CancellableContinuation
Xamarin.KotlinX.Coroutines.IChildHandle, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ChildHandle
Xamarin.KotlinX.Coroutines.IChildHandle;kotlinx.coroutines.ChildHandle
kotlinx.coroutines.ChildHandle;kotlinx.coroutines.ChildHandle
Xamarin.KotlinX.Coroutines.IChildJob, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ChildJob
Xamarin.KotlinX.Coroutines.IChildJob;kotlinx.coroutines.ChildJob
kotlinx.coroutines.ChildJob;kotlinx.coroutines.ChildJob
Xamarin.KotlinX.Coroutines.ICompletableDeferred, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CompletableDeferred
Xamarin.KotlinX.Coroutines.ICompletableDeferred;kotlinx.coroutines.CompletableDeferred
kotlinx.coroutines.CompletableDeferred;kotlinx.coroutines.CompletableDeferred
Xamarin.KotlinX.Coroutines.ICompletableJob, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CompletableJob
Xamarin.KotlinX.Coroutines.ICompletableJob;kotlinx.coroutines.CompletableJob
kotlinx.coroutines.CompletableJob;kotlinx.coroutines.CompletableJob
Xamarin.KotlinX.Coroutines.ICopyableThreadContextElement, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CopyableThreadContextElement
Xamarin.KotlinX.Coroutines.ICopyableThreadContextElement;kotlinx.coroutines.CopyableThreadContextElement
kotlinx.coroutines.CopyableThreadContextElement;kotlinx.coroutines.CopyableThreadContextElement
Xamarin.KotlinX.Coroutines.ICopyableThrowable, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CopyableThrowable
Xamarin.KotlinX.Coroutines.ICopyableThrowable;kotlinx.coroutines.CopyableThrowable
kotlinx.coroutines.CopyableThrowable;kotlinx.coroutines.CopyableThrowable
Xamarin.KotlinX.Coroutines.ICoroutineExceptionHandler, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CoroutineExceptionHandler
Xamarin.KotlinX.Coroutines.ICoroutineExceptionHandler;kotlinx.coroutines.CoroutineExceptionHandler
kotlinx.coroutines.CoroutineExceptionHandler;kotlinx.coroutines.CoroutineExceptionHandler
Xamarin.KotlinX.Coroutines.ICoroutineScope, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.CoroutineScope
Xamarin.KotlinX.Coroutines.ICoroutineScope;kotlinx.coroutines.CoroutineScope
kotlinx.coroutines.CoroutineScope;kotlinx.coroutines.CoroutineScope
Xamarin.KotlinX.Coroutines.IDeferred, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.Deferred
Xamarin.KotlinX.Coroutines.IDeferred;kotlinx.coroutines.Deferred
kotlinx.coroutines.Deferred;kotlinx.coroutines.Deferred
Xamarin.KotlinX.Coroutines.IDelay, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.Delay
Xamarin.KotlinX.Coroutines.IDelay;kotlinx.coroutines.Delay
kotlinx.coroutines.Delay;kotlinx.coroutines.Delay
Xamarin.KotlinX.Coroutines.IDelicateCoroutinesApi, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.DelicateCoroutinesApi
Xamarin.KotlinX.Coroutines.IDelicateCoroutinesApi;kotlinx.coroutines.DelicateCoroutinesApi
kotlinx.coroutines.DelicateCoroutinesApi;kotlinx.coroutines.DelicateCoroutinesApi
Xamarin.KotlinX.Coroutines.IDisposableHandle, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.DisposableHandle
Xamarin.KotlinX.Coroutines.IDisposableHandle;kotlinx.coroutines.DisposableHandle
kotlinx.coroutines.DisposableHandle;kotlinx.coroutines.DisposableHandle
Xamarin.KotlinX.Coroutines.IExperimentalCoroutinesApi, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ExperimentalCoroutinesApi
Xamarin.KotlinX.Coroutines.IExperimentalCoroutinesApi;kotlinx.coroutines.ExperimentalCoroutinesApi
kotlinx.coroutines.ExperimentalCoroutinesApi;kotlinx.coroutines.ExperimentalCoroutinesApi
Xamarin.KotlinX.Coroutines.IFlowPreview, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.FlowPreview
Xamarin.KotlinX.Coroutines.IFlowPreview;kotlinx.coroutines.FlowPreview
kotlinx.coroutines.FlowPreview;kotlinx.coroutines.FlowPreview
Xamarin.KotlinX.Coroutines.IInternalCoroutinesApi, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.InternalCoroutinesApi
Xamarin.KotlinX.Coroutines.IInternalCoroutinesApi;kotlinx.coroutines.InternalCoroutinesApi
kotlinx.coroutines.InternalCoroutinesApi;kotlinx.coroutines.InternalCoroutinesApi
Xamarin.KotlinX.Coroutines.IJob, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.Job
Xamarin.KotlinX.Coroutines.IJob;kotlinx.coroutines.Job
kotlinx.coroutines.Job;kotlinx.coroutines.Job
Xamarin.KotlinX.Coroutines.IObsoleteCoroutinesApi, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ObsoleteCoroutinesApi
Xamarin.KotlinX.Coroutines.IObsoleteCoroutinesApi;kotlinx.coroutines.ObsoleteCoroutinesApi
kotlinx.coroutines.ObsoleteCoroutinesApi;kotlinx.coroutines.ObsoleteCoroutinesApi
Xamarin.KotlinX.Coroutines.IParentJob, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ParentJob
Xamarin.KotlinX.Coroutines.IParentJob;kotlinx.coroutines.ParentJob
kotlinx.coroutines.ParentJob;kotlinx.coroutines.ParentJob
Xamarin.KotlinX.Coroutines.IThreadContextElement, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.ThreadContextElement
Xamarin.KotlinX.Coroutines.IThreadContextElement;kotlinx.coroutines.ThreadContextElement
kotlinx.coroutines.ThreadContextElement;kotlinx.coroutines.ThreadContextElement
Xamarin.KotlinX.Coroutines.Sync.IMutex, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.sync.Mutex
Xamarin.KotlinX.Coroutines.Sync.IMutex;kotlinx.coroutines.sync.Mutex
kotlinx.coroutines.sync.Mutex;kotlinx.coroutines.sync.Mutex
Xamarin.KotlinX.Coroutines.Sync.ISemaphore, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.sync.Semaphore
Xamarin.KotlinX.Coroutines.Sync.ISemaphore;kotlinx.coroutines.sync.Semaphore
kotlinx.coroutines.sync.Semaphore;kotlinx.coroutines.sync.Semaphore
Xamarin.KotlinX.Coroutines.Selects.ISelectBuilder, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectBuilder
Xamarin.KotlinX.Coroutines.Selects.ISelectBuilder;kotlinx.coroutines.selects.SelectBuilder
kotlinx.coroutines.selects.SelectBuilder;kotlinx.coroutines.selects.SelectBuilder
Xamarin.KotlinX.Coroutines.Selects.ISelectClause, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectClause
Xamarin.KotlinX.Coroutines.Selects.ISelectClause;kotlinx.coroutines.selects.SelectClause
kotlinx.coroutines.selects.SelectClause;kotlinx.coroutines.selects.SelectClause
Xamarin.KotlinX.Coroutines.Selects.ISelectClause0, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectClause0
Xamarin.KotlinX.Coroutines.Selects.ISelectClause0;kotlinx.coroutines.selects.SelectClause0
kotlinx.coroutines.selects.SelectClause0;kotlinx.coroutines.selects.SelectClause0
Xamarin.KotlinX.Coroutines.Selects.ISelectClause1, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectClause1
Xamarin.KotlinX.Coroutines.Selects.ISelectClause1;kotlinx.coroutines.selects.SelectClause1
kotlinx.coroutines.selects.SelectClause1;kotlinx.coroutines.selects.SelectClause1
Xamarin.KotlinX.Coroutines.Selects.ISelectClause2, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectClause2
Xamarin.KotlinX.Coroutines.Selects.ISelectClause2;kotlinx.coroutines.selects.SelectClause2
kotlinx.coroutines.selects.SelectClause2;kotlinx.coroutines.selects.SelectClause2
Xamarin.KotlinX.Coroutines.Selects.ISelectInstance, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.selects.SelectInstance
Xamarin.KotlinX.Coroutines.Selects.ISelectInstance;kotlinx.coroutines.selects.SelectInstance
kotlinx.coroutines.selects.SelectInstance;kotlinx.coroutines.selects.SelectInstance
Xamarin.KotlinX.Coroutines.Flow.IFlow, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.Flow
Xamarin.KotlinX.Coroutines.Flow.IFlow;kotlinx.coroutines.flow.Flow
kotlinx.coroutines.flow.Flow;kotlinx.coroutines.flow.Flow
Xamarin.KotlinX.Coroutines.Flow.IFlowCollector, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.FlowCollector
Xamarin.KotlinX.Coroutines.Flow.IFlowCollector;kotlinx.coroutines.flow.FlowCollector
kotlinx.coroutines.flow.FlowCollector;kotlinx.coroutines.flow.FlowCollector
Xamarin.KotlinX.Coroutines.Flow.IMutableSharedFlow, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.MutableSharedFlow
Xamarin.KotlinX.Coroutines.Flow.IMutableSharedFlow;kotlinx.coroutines.flow.MutableSharedFlow
kotlinx.coroutines.flow.MutableSharedFlow;kotlinx.coroutines.flow.MutableSharedFlow
Xamarin.KotlinX.Coroutines.Flow.IMutableStateFlow, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.MutableStateFlow
Xamarin.KotlinX.Coroutines.Flow.IMutableStateFlow;kotlinx.coroutines.flow.MutableStateFlow
kotlinx.coroutines.flow.MutableStateFlow;kotlinx.coroutines.flow.MutableStateFlow
Xamarin.KotlinX.Coroutines.Flow.ISharedFlow, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.SharedFlow
Xamarin.KotlinX.Coroutines.Flow.ISharedFlow;kotlinx.coroutines.flow.SharedFlow
kotlinx.coroutines.flow.SharedFlow;kotlinx.coroutines.flow.SharedFlow
Xamarin.KotlinX.Coroutines.Flow.ISharingStarted, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.SharingStarted
Xamarin.KotlinX.Coroutines.Flow.ISharingStarted;kotlinx.coroutines.flow.SharingStarted
kotlinx.coroutines.flow.SharingStarted;kotlinx.coroutines.flow.SharingStarted
Xamarin.KotlinX.Coroutines.Flow.IStateFlow, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.flow.StateFlow
Xamarin.KotlinX.Coroutines.Flow.IStateFlow;kotlinx.coroutines.flow.StateFlow
kotlinx.coroutines.flow.StateFlow;kotlinx.coroutines.flow.StateFlow
Xamarin.KotlinX.Coroutines.Channels.IActorScope, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.ActorScope
Xamarin.KotlinX.Coroutines.Channels.IActorScope;kotlinx.coroutines.channels.ActorScope
kotlinx.coroutines.channels.ActorScope;kotlinx.coroutines.channels.ActorScope
Xamarin.KotlinX.Coroutines.Channels.IBroadcastChannel, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.BroadcastChannel
Xamarin.KotlinX.Coroutines.Channels.IBroadcastChannel;kotlinx.coroutines.channels.BroadcastChannel
kotlinx.coroutines.channels.BroadcastChannel;kotlinx.coroutines.channels.BroadcastChannel
Xamarin.KotlinX.Coroutines.Channels.IChannel, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.Channel
Xamarin.KotlinX.Coroutines.Channels.IChannel;kotlinx.coroutines.channels.Channel
kotlinx.coroutines.channels.Channel;kotlinx.coroutines.channels.Channel
Xamarin.KotlinX.Coroutines.Channels.IChannelIterator, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.ChannelIterator
Xamarin.KotlinX.Coroutines.Channels.IChannelIterator;kotlinx.coroutines.channels.ChannelIterator
kotlinx.coroutines.channels.ChannelIterator;kotlinx.coroutines.channels.ChannelIterator
Xamarin.KotlinX.Coroutines.Channels.IProducerScope, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.ProducerScope
Xamarin.KotlinX.Coroutines.Channels.IProducerScope;kotlinx.coroutines.channels.ProducerScope
kotlinx.coroutines.channels.ProducerScope;kotlinx.coroutines.channels.ProducerScope
Xamarin.KotlinX.Coroutines.Channels.IReceiveChannel, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.ReceiveChannel
Xamarin.KotlinX.Coroutines.Channels.IReceiveChannel;kotlinx.coroutines.channels.ReceiveChannel
kotlinx.coroutines.channels.ReceiveChannel;kotlinx.coroutines.channels.ReceiveChannel
Xamarin.KotlinX.Coroutines.Channels.ISendChannel, Xamarin.KotlinX.Coroutines.Core.Jvm;kotlinx.coroutines.channels.SendChannel
Xamarin.KotlinX.Coroutines.Channels.ISendChannel;kotlinx.coroutines.channels.SendChannel
kotlinx.coroutines.channels.SendChannel;kotlinx.coroutines.channels.SendChannel
KotlinX.Serialization.IBinaryFormat, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.BinaryFormat
KotlinX.Serialization.IBinaryFormat;kotlinx.serialization.BinaryFormat
kotlinx.serialization.BinaryFormat;kotlinx.serialization.BinaryFormat
KotlinX.Serialization.IContextual, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Contextual
KotlinX.Serialization.IContextual;kotlinx.serialization.Contextual
kotlinx.serialization.Contextual;kotlinx.serialization.Contextual
KotlinX.Serialization.IDeserializationStrategy, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.DeserializationStrategy
KotlinX.Serialization.IDeserializationStrategy;kotlinx.serialization.DeserializationStrategy
kotlinx.serialization.DeserializationStrategy;kotlinx.serialization.DeserializationStrategy
KotlinX.Serialization.IEncodeDefault, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.EncodeDefault
KotlinX.Serialization.IEncodeDefault;kotlinx.serialization.EncodeDefault
kotlinx.serialization.EncodeDefault;kotlinx.serialization.EncodeDefault
KotlinX.Serialization.IExperimentalSerializationApi, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.ExperimentalSerializationApi
KotlinX.Serialization.IExperimentalSerializationApi;kotlinx.serialization.ExperimentalSerializationApi
kotlinx.serialization.ExperimentalSerializationApi;kotlinx.serialization.ExperimentalSerializationApi
KotlinX.Serialization.IInheritableSerialInfo, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.InheritableSerialInfo
KotlinX.Serialization.IInheritableSerialInfo;kotlinx.serialization.InheritableSerialInfo
kotlinx.serialization.InheritableSerialInfo;kotlinx.serialization.InheritableSerialInfo
KotlinX.Serialization.IInternalSerializationApi, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.InternalSerializationApi
KotlinX.Serialization.IInternalSerializationApi;kotlinx.serialization.InternalSerializationApi
kotlinx.serialization.InternalSerializationApi;kotlinx.serialization.InternalSerializationApi
KotlinX.Serialization.IKeepGeneratedSerializer, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.KeepGeneratedSerializer
KotlinX.Serialization.IKeepGeneratedSerializer;kotlinx.serialization.KeepGeneratedSerializer
kotlinx.serialization.KeepGeneratedSerializer;kotlinx.serialization.KeepGeneratedSerializer
KotlinX.Serialization.IKSerializer, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.KSerializer
KotlinX.Serialization.IKSerializer;kotlinx.serialization.KSerializer
kotlinx.serialization.KSerializer;kotlinx.serialization.KSerializer
KotlinX.Serialization.IMetaSerializable, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.MetaSerializable
KotlinX.Serialization.IMetaSerializable;kotlinx.serialization.MetaSerializable
kotlinx.serialization.MetaSerializable;kotlinx.serialization.MetaSerializable
KotlinX.Serialization.IPolymorphic, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Polymorphic
KotlinX.Serialization.IPolymorphic;kotlinx.serialization.Polymorphic
kotlinx.serialization.Polymorphic;kotlinx.serialization.Polymorphic
KotlinX.Serialization.IRequired, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Required
KotlinX.Serialization.IRequired;kotlinx.serialization.Required
kotlinx.serialization.Required;kotlinx.serialization.Required
KotlinX.Serialization.ISerialFormat, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.SerialFormat
KotlinX.Serialization.ISerialFormat;kotlinx.serialization.SerialFormat
kotlinx.serialization.SerialFormat;kotlinx.serialization.SerialFormat
KotlinX.Serialization.ISerialInfo, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.SerialInfo
KotlinX.Serialization.ISerialInfo;kotlinx.serialization.SerialInfo
kotlinx.serialization.SerialInfo;kotlinx.serialization.SerialInfo
KotlinX.Serialization.ISerializable, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Serializable
KotlinX.Serialization.ISerializable;kotlinx.serialization.Serializable
kotlinx.serialization.Serializable;kotlinx.serialization.Serializable
KotlinX.Serialization.ISerializationStrategy, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.SerializationStrategy
KotlinX.Serialization.ISerializationStrategy;kotlinx.serialization.SerializationStrategy
kotlinx.serialization.SerializationStrategy;kotlinx.serialization.SerializationStrategy
KotlinX.Serialization.ISerializer, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Serializer
KotlinX.Serialization.ISerializer;kotlinx.serialization.Serializer
kotlinx.serialization.Serializer;kotlinx.serialization.Serializer
KotlinX.Serialization.ISerialName, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.SerialName
KotlinX.Serialization.ISerialName;kotlinx.serialization.SerialName
kotlinx.serialization.SerialName;kotlinx.serialization.SerialName
KotlinX.Serialization.IStringFormat, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.StringFormat
KotlinX.Serialization.IStringFormat;kotlinx.serialization.StringFormat
kotlinx.serialization.StringFormat;kotlinx.serialization.StringFormat
KotlinX.Serialization.ITransient, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.Transient
KotlinX.Serialization.ITransient;kotlinx.serialization.Transient
kotlinx.serialization.Transient;kotlinx.serialization.Transient
KotlinX.Serialization.IUseContextualSerialization, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.UseContextualSerialization
KotlinX.Serialization.IUseContextualSerialization;kotlinx.serialization.UseContextualSerialization
kotlinx.serialization.UseContextualSerialization;kotlinx.serialization.UseContextualSerialization
KotlinX.Serialization.IUseSerializers, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.UseSerializers
KotlinX.Serialization.IUseSerializers;kotlinx.serialization.UseSerializers
kotlinx.serialization.UseSerializers;kotlinx.serialization.UseSerializers
KotlinX.Serialization.Modules.ISerializersModuleCollector, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.modules.SerializersModuleCollector
KotlinX.Serialization.Modules.ISerializersModuleCollector;kotlinx.serialization.modules.SerializersModuleCollector
kotlinx.serialization.modules.SerializersModuleCollector;kotlinx.serialization.modules.SerializersModuleCollector
KotlinX.Serialization.Encoding.IChunkedDecoder, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.encoding.ChunkedDecoder
KotlinX.Serialization.Encoding.IChunkedDecoder;kotlinx.serialization.encoding.ChunkedDecoder
kotlinx.serialization.encoding.ChunkedDecoder;kotlinx.serialization.encoding.ChunkedDecoder
KotlinX.Serialization.Encoding.ICompositeDecoder, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.encoding.CompositeDecoder
KotlinX.Serialization.Encoding.ICompositeDecoder;kotlinx.serialization.encoding.CompositeDecoder
kotlinx.serialization.encoding.CompositeDecoder;kotlinx.serialization.encoding.CompositeDecoder
KotlinX.Serialization.Encoding.ICompositeEncoder, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.encoding.CompositeEncoder
KotlinX.Serialization.Encoding.ICompositeEncoder;kotlinx.serialization.encoding.CompositeEncoder
kotlinx.serialization.encoding.CompositeEncoder;kotlinx.serialization.encoding.CompositeEncoder
KotlinX.Serialization.Encoding.IDecoder, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.encoding.Decoder
KotlinX.Serialization.Encoding.IDecoder;kotlinx.serialization.encoding.Decoder
kotlinx.serialization.encoding.Decoder;kotlinx.serialization.encoding.Decoder
KotlinX.Serialization.Encoding.IEncoder, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.encoding.Encoder
KotlinX.Serialization.Encoding.IEncoder;kotlinx.serialization.encoding.Encoder
kotlinx.serialization.encoding.Encoder;kotlinx.serialization.encoding.Encoder
KotlinX.Serialization.Descriptors.ISerialDescriptor, Xamarin.KotlinX.Serialization.Core.Jvm;kotlinx.serialization.descriptors.SerialDescriptor
KotlinX.Serialization.Descriptors.ISerialDescriptor;kotlinx.serialization.descriptors.SerialDescriptor
kotlinx.serialization.descriptors.SerialDescriptor;kotlinx.serialization.descriptors.SerialDescriptor
